---
title: Module.Tooltip
description: Accessible tooltip component for displaying contextual information on hover or focus.
sidebar:
  order: 11
---

Accessible tooltip component for displaying contextual information on hover or focus with Miss UI Web.

## Features

- **Accessible Design** - Full ARIA support and keyboard navigation
- **Smart Positioning** - Automatic placement with collision detection
- **Multiple Triggers** - Hover, focus, click, and manual control
- **Rich Content** - Support for text, HTML, and React components
- **Animation Support** - Smooth enter/exit animations
- **Delay Control** - Configurable show/hide delays
- **Portal Rendering** - Renders outside DOM hierarchy to avoid z-index issues
- **Touch Support** - Mobile-friendly touch interactions
- **Theme Integration** - Consistent styling with design system
- **Performance Optimized** - Efficient rendering and event handling

## Basic Usage

### Simple Tooltip

```tsx
import { Module } from '@miss-ui/web';

function BasicTooltip() {
  return (
    <Module.Tooltip content="This is a helpful tooltip">
      <button>Hover me</button>
    </Module.Tooltip>
  );
}
```

### With Custom Placement

```tsx
function PlacementTooltip() {
  return (
    <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
      <Module.Tooltip content="Top tooltip" placement="top">
        <button>Top</button>
      </Module.Tooltip>
      
      <Module.Tooltip content="Right tooltip" placement="right">
        <button>Right</button>
      </Module.Tooltip>
      
      <Module.Tooltip content="Bottom tooltip" placement="bottom">
        <button>Bottom</button>
      </Module.Tooltip>
      
      <Module.Tooltip content="Left tooltip" placement="left">
        <button>Left</button>
      </Module.Tooltip>
    </div>
  );
}
```

### Rich Content Tooltip

```tsx
function RichContentTooltip() {
  const tooltipContent = (
    <div>
      <strong>Rich Tooltip</strong>
      <p>This tooltip contains formatted content with multiple elements.</p>
      <ul>
        <li>Feature 1</li>
        <li>Feature 2</li>
        <li>Feature 3</li>
      </ul>
    </div>
  );

  return (
    <Module.Tooltip content={tooltipContent}>
      <button>Rich content</button>
    </Module.Tooltip>
  );
}
```

## Variants

### Different Triggers

```tsx
function TriggerVariants() {
  return (
    <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
      <Module.Tooltip 
        content="Hover to show" 
        trigger="hover"
      >
        <button>Hover trigger</button>
      </Module.Tooltip>
      
      <Module.Tooltip 
        content="Focus to show" 
        trigger="focus"
      >
        <input placeholder="Focus trigger" />
      </Module.Tooltip>
      
      <Module.Tooltip 
        content="Click to show" 
        trigger="click"
      >
        <button>Click trigger</button>
      </Module.Tooltip>
      
      <Module.Tooltip 
        content="Multiple triggers" 
        trigger={['hover', 'focus']}
      >
        <button>Hover or focus</button>
      </Module.Tooltip>
    </div>
  );
}
```

### With Delays

```tsx
function DelayedTooltips() {
  return (
    <div style={{ display: 'flex', gap: '16px' }}>
      <Module.Tooltip 
        content="Shows immediately"
        showDelay={0}
      >
        <button>No delay</button>
      </Module.Tooltip>
      
      <Module.Tooltip 
        content="Shows after 500ms"
        showDelay={500}
      >
        <button>500ms delay</button>
      </Module.Tooltip>
      
      <Module.Tooltip 
        content="Custom hide delay"
        showDelay={200}
        hideDelay={1000}
      >
        <button>Custom delays</button>
      </Module.Tooltip>
    </div>
  );
}
```

### Controlled Tooltip

```tsx
function ControlledTooltip() {
  const [isOpen, setIsOpen] = React.useState(false);

  return (
    <div style={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
      <Module.Tooltip 
        content="This is a controlled tooltip"
        isOpen={isOpen}
        onOpenChange={setIsOpen}
        trigger="manual"
      >
        <button>Controlled target</button>
      </Module.Tooltip>
      
      <button onClick={() => setIsOpen(!isOpen)}>
        {isOpen ? 'Hide' : 'Show'} Tooltip
      </button>
    </div>
  );
}
```

## Advanced Features

### Custom Styling

```tsx
function StyledTooltips() {
  return (
    <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
      <Module.Tooltip 
        content="Success tooltip"
        variant="success"
      >
        <button>Success</button>
      </Module.Tooltip>
      
      <Module.Tooltip 
        content="Warning tooltip"
        variant="warning"
      >
        <button>Warning</button>
      </Module.Tooltip>
      
      <Module.Tooltip 
        content="Error tooltip"
        variant="error"
      >
        <button>Error</button>
      </Module.Tooltip>
      
      <Module.Tooltip 
        content="Custom styled tooltip"
        className="custom-tooltip"
        style={{
          backgroundColor: '#6366f1',
          color: 'white',
          borderRadius: '8px',
        }}
      >
        <button>Custom style</button>
      </Module.Tooltip>
    </div>
  );
}
```

### With Arrow Customization

```tsx
function ArrowTooltips() {
  return (
    <div style={{ display: 'flex', gap: '16px' }}>
      <Module.Tooltip 
        content="Tooltip with arrow"
        showArrow={true}
      >
        <button>With arrow</button>
      </Module.Tooltip>
      
      <Module.Tooltip 
        content="Tooltip without arrow"
        showArrow={false}
      >
        <button>No arrow</button>
      </Module.Tooltip>
      
      <Module.Tooltip 
        content="Custom arrow size"
        showArrow={true}
        arrowSize={12}
      >
        <button>Large arrow</button>
      </Module.Tooltip>
    </div>
  );
}
```

### Interactive Tooltip

```tsx
function InteractiveTooltip() {
  return (
    <Module.Tooltip 
      content={
        <div>
          <p>This tooltip is interactive!</p>
          <button 
            onClick={() => alert('Button clicked!')}
            style={{ marginTop: '8px' }}
          >
            Click me
          </button>
        </div>
      }
      interactive={true}
      hideDelay={100}
    >
      <button>Interactive tooltip</button>
    </Module.Tooltip>
  );
}
```

### Tooltip with Maximum Width

```tsx
function MaxWidthTooltip() {
  const longContent = "This is a very long tooltip content that would normally wrap to multiple lines, but we can control its maximum width to ensure it doesn't become too wide on larger screens.";

  return (
    <Module.Tooltip 
      content={longContent}
      maxWidth={300}
    >
      <button>Long content tooltip</button>
    </Module.Tooltip>
  );
}
```

## Props

### Module.Tooltip Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `content` | `ReactNode` | - | Tooltip content to display |
| `children` | `ReactNode` | - | Element that triggers the tooltip |
| `placement` | `Placement` | `'top'` | Preferred placement of tooltip |
| `trigger` | `Trigger \| Trigger[]` | `'hover'` | Events that trigger tooltip |
| `isOpen` | `boolean` | - | Controlled open state |
| `defaultOpen` | `boolean` | `false` | Default open state |
| `onOpenChange` | `(open: boolean) => void` | - | Callback when open state changes |
| `showDelay` | `number` | `200` | Delay before showing (ms) |
| `hideDelay` | `number` | `200` | Delay before hiding (ms) |
| `showArrow` | `boolean` | `true` | Whether to show arrow |
| `arrowSize` | `number` | `8` | Size of the arrow |
| `interactive` | `boolean` | `false` | Whether tooltip content is interactive |
| `maxWidth` | `number \| string` | `300` | Maximum width of tooltip |
| `variant` | `'default' \| 'success' \| 'warning' \| 'error'` | `'default'` | Visual variant |
| `disabled` | `boolean` | `false` | Whether tooltip is disabled |
| `portal` | `boolean` | `true` | Whether to render in portal |
| `portalContainer` | `HTMLElement` | `document.body` | Portal container element |
| `className` | `string` | - | Additional CSS class names |
| `style` | `CSSProperties` | - | Inline styles |
| `contentClassName` | `string` | - | CSS class for tooltip content |
| `contentStyle` | `CSSProperties` | - | Inline styles for tooltip content |

### Type Definitions

```typescript
type Placement = 
  | 'top'
  | 'top-start'
  | 'top-end'
  | 'bottom'
  | 'bottom-start'
  | 'bottom-end'
  | 'left'
  | 'left-start'
  | 'left-end'
  | 'right'
  | 'right-start'
  | 'right-end';

type Trigger = 'hover' | 'focus' | 'click' | 'manual';
```

## Accessibility

### ARIA Support

```tsx
function AccessibleTooltip() {
  return (
    <Module.Tooltip 
      content="Accessible tooltip with proper ARIA attributes"
      aria-label="Help information"
    >
      <button aria-describedby="tooltip-id">
        Help
      </button>
    </Module.Tooltip>
  );
}
```

### Keyboard Navigation

- **Escape** - Closes the tooltip
- **Tab** - Moves focus and shows/hides tooltip based on trigger
- **Enter/Space** - Activates click trigger when focused

### Screen Reader Support

- Tooltip content is announced when shown
- Proper ARIA relationships are established
- Focus management for interactive tooltips
- Semantic markup for rich content

### Focus Management

```tsx
function FocusManagementTooltip() {
  return (
    <div>
      <Module.Tooltip content="First tooltip">
        <button>First button</button>
      </Module.Tooltip>
      
      <Module.Tooltip 
        content={
          <div>
            <p>Interactive tooltip</p>
            <button>Focusable button</button>
          </div>
        }
        interactive={true}
        trigger="focus"
      >
        <button>Interactive tooltip trigger</button>
      </Module.Tooltip>
      
      <Module.Tooltip content="Third tooltip">
        <button>Third button</button>
      </Module.Tooltip>
    </div>
  );
}
```

## Best Practices

### Do's

- ✅ Keep tooltip content concise and helpful
- ✅ Use appropriate triggers for the context
- ✅ Ensure tooltips don't interfere with user interactions
- ✅ Test with keyboard navigation and screen readers
- ✅ Use semantic HTML within tooltip content
- ✅ Consider mobile users (touch interactions)
- ✅ Provide alternative ways to access information

### Don'ts

- ❌ Don't put essential information only in tooltips
- ❌ Don't make tooltips too large or complex
- ❌ Don't use tooltips for critical actions
- ❌ Don't nest tooltips within each other
- ❌ Don't use tooltips on disabled elements without proper handling
- ❌ Don't rely solely on hover for mobile interfaces
- ❌ Don't use tooltips for form validation errors

## Performance

### Optimization Tips

```tsx
// ✅ Good: Memoize complex tooltip content
const TooltipContent = React.memo(function TooltipContent({ data }) {
  return (
    <div>
      <h4>{data.title}</h4>
      <p>{data.description}</p>
    </div>
  );
});

function OptimizedTooltip({ data }) {
  return (
    <Module.Tooltip content={<TooltipContent data={data} />}>
      <button>Show info</button>
    </Module.Tooltip>
  );
}

// ✅ Good: Use string content when possible
function SimpleTooltip() {
  return (
    <Module.Tooltip content="Simple string content">
      <button>Hover me</button>
    </Module.Tooltip>
  );
}

// ✅ Good: Disable tooltips when not needed
function ConditionalTooltip({ showTooltip, content }) {
  return (
    <Module.Tooltip 
      content={content}
      disabled={!showTooltip}
    >
      <button>Conditional tooltip</button>
    </Module.Tooltip>
  );
}
```

### Bundle Size

- Lightweight implementation with minimal dependencies
- Tree-shakeable positioning utilities
- Optimized event handling and cleanup
- Efficient portal rendering when needed

### Runtime Performance

- Lazy positioning calculations
- Debounced show/hide events
- Efficient DOM updates
- Memory leak prevention with proper cleanup