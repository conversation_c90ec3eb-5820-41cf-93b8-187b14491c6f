---
title: Element.Row
description: Flexible row and column layout components for creating responsive grid systems and structured layouts.
sidebar:
  order: 8
---

Flexible row and column layout components for creating responsive grid systems and structured layouts with Miss UI Web.

## Features

- **Flexible Grid System** - Create responsive layouts with customizable columns
- **Responsive Design** - Automatic breakpoint handling and mobile-first approach
- **Spacing Control** - Built-in gutters and spacing management
- **Alignment Options** - Horizontal and vertical alignment controls
- **Nested Layouts** - Support for complex nested row and column structures
- **Auto-sizing** - Automatic column width distribution
- **Custom Breakpoints** - Define custom responsive breakpoints
- **Offset Support** - Column offset and ordering capabilities
- **Flex Properties** - Full flexbox property support
- **Performance Optimized** - Lightweight CSS-in-JS implementation

## Basic Usage

### Simple Row and Columns

```tsx
import { Element } from '@miss-ui/web';

function BasicLayout() {
  return (
    <Element.Row>
      <Element.Row.Column span={6}>
        <div>Left Column</div>
      </Element.Row.Column>
      <Element.Row.Column span={6}>
        <div>Right Column</div>
      </Element.Row.Column>
    </Element.Row>
  );
}
```

### Responsive Columns

```tsx
function ResponsiveLayout() {
  return (
    <Element.Row>
      <Element.Row.Column 
        span={{ xs: 12, sm: 6, md: 4, lg: 3 }}
      >
        <div>Responsive Column 1</div>
      </Element.Row.Column>
      <Element.Row.Column 
        span={{ xs: 12, sm: 6, md: 4, lg: 3 }}
      >
        <div>Responsive Column 2</div>
      </Element.Row.Column>
      <Element.Row.Column 
        span={{ xs: 12, sm: 12, md: 4, lg: 6 }}
      >
        <div>Responsive Column 3</div>
      </Element.Row.Column>
    </Element.Row>
  );
}
```

### Auto-sizing Columns

```tsx
function AutoSizeLayout() {
  return (
    <Element.Row>
      <Element.Row.Column>
        <div>Auto-sized column</div>
      </Element.Row.Column>
      <Element.Row.Column>
        <div>Auto-sized column</div>
      </Element.Row.Column>
      <Element.Row.Column>
        <div>Auto-sized column</div>
      </Element.Row.Column>
    </Element.Row>
  );
}
```

## Variants

### With Gutters

```tsx
function GutterLayout() {
  return (
    <Element.Row gutter={16}>
      <Element.Row.Column span={8}>
        <div style={{ background: '#f0f0f0', padding: '16px' }}>
          Column with gutter
        </div>
      </Element.Row.Column>
      <Element.Row.Column span={8}>
        <div style={{ background: '#f0f0f0', padding: '16px' }}>
          Column with gutter
        </div>
      </Element.Row.Column>
      <Element.Row.Column span={8}>
        <div style={{ background: '#f0f0f0', padding: '16px' }}>
          Column with gutter
        </div>
      </Element.Row.Column>
    </Element.Row>
  );
}
```

### Responsive Gutters

```tsx
function ResponsiveGutters() {
  return (
    <Element.Row 
      gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}
    >
      <Element.Row.Column span={12}>
        <div>Responsive gutter spacing</div>
      </Element.Row.Column>
      <Element.Row.Column span={12}>
        <div>Responsive gutter spacing</div>
      </Element.Row.Column>
    </Element.Row>
  );
}
```

### Alignment Options

```tsx
function AlignmentLayout() {
  return (
    <>
      {/* Horizontal alignment */}
      <Element.Row justify="center">
        <Element.Row.Column span={6}>
          <div>Centered content</div>
        </Element.Row.Column>
      </Element.Row>

      {/* Vertical alignment */}
      <Element.Row align="middle" style={{ minHeight: '200px' }}>
        <Element.Row.Column span={12}>
          <div>Vertically centered</div>
        </Element.Row.Column>
        <Element.Row.Column span={12}>
          <div>Vertically centered</div>
        </Element.Row.Column>
      </Element.Row>

      {/* Space distribution */}
      <Element.Row justify="space-between">
        <Element.Row.Column span={4}>
          <div>Left</div>
        </Element.Row.Column>
        <Element.Row.Column span={4}>
          <div>Center</div>
        </Element.Row.Column>
        <Element.Row.Column span={4}>
          <div>Right</div>
        </Element.Row.Column>
      </Element.Row>
    </>
  );
}
```

## Advanced Features

### Column Offset

```tsx
function OffsetLayout() {
  return (
    <Element.Row>
      <Element.Row.Column span={8} offset={4}>
        <div>Offset by 4 columns</div>
      </Element.Row.Column>
      <Element.Row.Column span={6} offset={2}>
        <div>Offset by 2 columns</div>
      </Element.Row.Column>
    </Element.Row>
  );
}
```

### Column Ordering

```tsx
function OrderedLayout() {
  return (
    <Element.Row>
      <Element.Row.Column span={8} order={2}>
        <div>Second in order</div>
      </Element.Row.Column>
      <Element.Row.Column span={8} order={1}>
        <div>First in order</div>
      </Element.Row.Column>
      <Element.Row.Column span={8} order={3}>
        <div>Third in order</div>
      </Element.Row.Column>
    </Element.Row>
  );
}
```

### Nested Layouts

```tsx
function NestedLayout() {
  return (
    <Element.Row>
      <Element.Row.Column span={12}>
        <div>Outer column</div>
        <Element.Row>
          <Element.Row.Column span={6}>
            <div>Nested column 1</div>
          </Element.Row.Column>
          <Element.Row.Column span={6}>
            <div>Nested column 2</div>
            <Element.Row>
              <Element.Row.Column span={12}>
                <div>Deeply nested column</div>
              </Element.Row.Column>
            </Element.Row>
          </Element.Row.Column>
        </Element.Row>
      </Element.Row.Column>
      <Element.Row.Column span={12}>
        <div>Another outer column</div>
      </Element.Row.Column>
    </Element.Row>
  );
}
```

### Flex Properties

```tsx
function FlexLayout() {
  return (
    <Element.Row>
      <Element.Row.Column flex="none">
        <div>Fixed width content</div>
      </Element.Row.Column>
      <Element.Row.Column flex="auto">
        <div>Flexible content that grows</div>
      </Element.Row.Column>
      <Element.Row.Column flex="0 0 200px">
        <div>200px fixed width</div>
      </Element.Row.Column>
    </Element.Row>
  );
}
```

## Props

### Element.Row Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `gutter` | `number \| ResponsiveValue<number> \| [number, number]` | `0` | Spacing between columns (horizontal, vertical) |
| `justify` | `'start' \| 'end' \| 'center' \| 'space-around' \| 'space-between' \| 'space-evenly'` | `'start'` | Horizontal alignment of columns |
| `align` | `'top' \| 'middle' \| 'bottom' \| 'stretch'` | `'top'` | Vertical alignment of columns |
| `wrap` | `boolean` | `true` | Whether columns should wrap to new lines |
| `className` | `string` | - | Additional CSS class names |
| `style` | `CSSProperties` | - | Inline styles |
| `children` | `ReactNode` | - | Row content (typically Column components) |

### Element.Row.Column Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `span` | `number \| ResponsiveValue<number>` | - | Number of columns to span (1-24) |
| `offset` | `number \| ResponsiveValue<number>` | `0` | Number of columns to offset |
| `order` | `number \| ResponsiveValue<number>` | - | Column order |
| `flex` | `string \| number` | - | Flex property value |
| `className` | `string` | - | Additional CSS class names |
| `style` | `CSSProperties` | - | Inline styles |
| `children` | `ReactNode` | - | Column content |

### ResponsiveValue Type

```typescript
type ResponsiveValue<T> = T | {
  xs?: T;
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
  xxl?: T;
};
```

## Responsive Breakpoints

| Breakpoint | Screen Size | Description |
|------------|-------------|-------------|
| `xs` | `< 576px` | Extra small devices |
| `sm` | `≥ 576px` | Small devices |
| `md` | `≥ 768px` | Medium devices |
| `lg` | `≥ 992px` | Large devices |
| `xl` | `≥ 1200px` | Extra large devices |
| `xxl` | `≥ 1600px` | Extra extra large devices |

## Accessibility

### Semantic Structure

- Use appropriate semantic HTML elements within columns
- Maintain logical reading order regardless of visual layout
- Ensure content is accessible when CSS is disabled

### Screen Reader Support

```tsx
function AccessibleLayout() {
  return (
    <Element.Row role="grid">
      <Element.Row.Column span={12} role="gridcell">
        <h2>Main Content</h2>
        <p>Primary content area</p>
      </Element.Row.Column>
      <Element.Row.Column span={12} role="gridcell">
        <aside aria-label="Sidebar">
          <h3>Related Links</h3>
          <nav>
            <ul>
              <li><a href="#">Link 1</a></li>
              <li><a href="#">Link 2</a></li>
            </ul>
          </nav>
        </aside>
      </Element.Row.Column>
    </Element.Row>
  );
}
```

### Focus Management

- Ensure tab order follows logical content flow
- Test keyboard navigation across different layouts
- Consider focus indicators for interactive elements

## Best Practices

### Do's

- ✅ Use semantic HTML elements within columns
- ✅ Test layouts across different screen sizes
- ✅ Keep column spans within the 24-column grid system
- ✅ Use responsive values for mobile-first design
- ✅ Maintain consistent gutter spacing across layouts
- ✅ Consider content hierarchy when nesting rows
- ✅ Use meaningful column spans that add up logically

### Don'ts

- ❌ Don't exceed 24 columns total in a single row
- ❌ Don't use fixed pixel widths when responsive values are available
- ❌ Don't nest too many levels deep (limit to 3-4 levels)
- ❌ Don't ignore mobile layouts when designing for desktop
- ❌ Don't use rows for non-layout purposes
- ❌ Don't forget to test with actual content
- ❌ Don't use negative margins instead of proper offset

## Performance

### Optimization Tips

```tsx
// ✅ Good: Memoize complex layouts
const OptimizedLayout = React.memo(function Layout({ data }) {
  return (
    <Element.Row>
      {data.map((item, index) => (
        <Element.Row.Column key={item.id} span={6}>
          <ItemComponent item={item} />
        </Element.Row.Column>
      ))}
    </Element.Row>
  );
});

// ✅ Good: Use responsive values efficiently
const responsiveSpan = useMemo(() => ({
  xs: 12,
  md: 6,
  lg: 4,
}), []);

function EfficientLayout() {
  return (
    <Element.Row>
      <Element.Row.Column span={responsiveSpan}>
        Content
      </Element.Row.Column>
    </Element.Row>
  );
}
```

### CSS-in-JS Optimization

- Row and Column components use optimized CSS-in-JS
- Styles are generated only when needed
- Responsive breakpoints are handled efficiently
- Minimal runtime overhead for layout calculations

### Bundle Size

- Lightweight implementation with minimal dependencies
- Tree-shakeable exports for unused breakpoints
- Optimized CSS generation for production builds
- Gzip-friendly output with consistent class names