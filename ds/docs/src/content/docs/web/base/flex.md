---
title: Base.Flex
description: Flexible layout component for creating responsive flexbox layouts with ease.
sidebar:
  order: 5
---

Flexible layout component for creating responsive flexbox layouts with ease in Miss UI Web.

## Features

- **Flexbox Layout** - Complete flexbox implementation with all properties
- **Responsive Design** - Breakpoint-aware responsive props
- **Direction Control** - Row, column, and reverse directions
- **Alignment Options** - Justify content and align items control
- **Gap Support** - Modern gap property for spacing
- **Wrap Control** - Flexible wrapping behavior
- **Grow & Shrink** - Individual item flex properties
- **Theme Integration** - Consistent spacing with design tokens

## Basic Usage

### Simple Flex Container

```tsx
import { Base } from '@miss-ui/web';

function BasicFlex() {
  return (
    <Base.Flex gap="16px">
      <div style={{ padding: '16px', background: '#f0f0f0' }}>Item 1</div>
      <div style={{ padding: '16px', background: '#e0e0e0' }}>Item 2</div>
      <div style={{ padding: '16px', background: '#d0d0d0' }}>Item 3</div>
    </Base.Flex>
  );
}
```

### Column Layout

```tsx
function ColumnFlex() {
  return (
    <Base.Flex direction="column" gap="12px">
      <div style={{ padding: '12px', background: '#ff6b6b' }}>Header</div>
      <div style={{ padding: '12px', background: '#4ecdc4' }}>Content</div>
      <div style={{ padding: '12px', background: '#45b7d1' }}>Footer</div>
    </Base.Flex>
  );
}
```

### Centered Content

```tsx
function CenteredFlex() {
  return (
    <Base.Flex 
      justify="center" 
      align="center" 
      style={{ height: '200px', border: '1px solid #ccc' }}
    >
      <div style={{ padding: '16px', background: '#007bff', color: 'white' }}>
        Centered Content
      </div>
    </Base.Flex>
  );
}
```

## Direction Variants

### All Directions

```tsx
function DirectionVariants() {
  const itemStyle = { padding: '12px', background: '#f8f9fa', border: '1px solid #dee2e6' };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div>
        <h4>Row (default)</h4>
        <Base.Flex gap="8px">
          <div style={itemStyle}>1</div>
          <div style={itemStyle}>2</div>
          <div style={itemStyle}>3</div>
        </Base.Flex>
      </div>
      
      <div>
        <h4>Row Reverse</h4>
        <Base.Flex direction="row-reverse" gap="8px">
          <div style={itemStyle}>1</div>
          <div style={itemStyle}>2</div>
          <div style={itemStyle}>3</div>
        </Base.Flex>
      </div>
      
      <div>
        <h4>Column</h4>
        <Base.Flex direction="column" gap="8px">
          <div style={itemStyle}>1</div>
          <div style={itemStyle}>2</div>
          <div style={itemStyle}>3</div>
        </Base.Flex>
      </div>
      
      <div>
        <h4>Column Reverse</h4>
        <Base.Flex direction="column-reverse" gap="8px">
          <div style={itemStyle}>1</div>
          <div style={itemStyle}>2</div>
          <div style={itemStyle}>3</div>
        </Base.Flex>
      </div>
    </div>
  );
}
```

## Alignment Options

### Justify Content

```tsx
function JustifyVariants() {
  const containerStyle = { 
    border: '1px solid #ccc', 
    padding: '8px', 
    marginBottom: '16px' 
  };
  const itemStyle = { 
    padding: '8px 16px', 
    background: '#007bff', 
    color: 'white' 
  };

  return (
    <div>
      <div>
        <h4>Flex Start</h4>
        <Base.Flex justify="flex-start" gap="8px" style={containerStyle}>
          <div style={itemStyle}>A</div>
          <div style={itemStyle}>B</div>
          <div style={itemStyle}>C</div>
        </Base.Flex>
      </div>
      
      <div>
        <h4>Center</h4>
        <Base.Flex justify="center" gap="8px" style={containerStyle}>
          <div style={itemStyle}>A</div>
          <div style={itemStyle}>B</div>
          <div style={itemStyle}>C</div>
        </Base.Flex>
      </div>
      
      <div>
        <h4>Flex End</h4>
        <Base.Flex justify="flex-end" gap="8px" style={containerStyle}>
          <div style={itemStyle}>A</div>
          <div style={itemStyle}>B</div>
          <div style={itemStyle}>C</div>
        </Base.Flex>
      </div>
      
      <div>
        <h4>Space Between</h4>
        <Base.Flex justify="space-between" style={containerStyle}>
          <div style={itemStyle}>A</div>
          <div style={itemStyle}>B</div>
          <div style={itemStyle}>C</div>
        </Base.Flex>
      </div>
      
      <div>
        <h4>Space Around</h4>
        <Base.Flex justify="space-around" style={containerStyle}>
          <div style={itemStyle}>A</div>
          <div style={itemStyle}>B</div>
          <div style={itemStyle}>C</div>
        </Base.Flex>
      </div>
      
      <div>
        <h4>Space Evenly</h4>
        <Base.Flex justify="space-evenly" style={containerStyle}>
          <div style={itemStyle}>A</div>
          <div style={itemStyle}>B</div>
          <div style={itemStyle}>C</div>
        </Base.Flex>
      </div>
    </div>
  );
}
```

### Align Items

```tsx
function AlignVariants() {
  const containerStyle = { 
    border: '1px solid #ccc', 
    padding: '8px', 
    marginBottom: '16px',
    height: '100px'
  };
  const itemStyle = { 
    padding: '8px 16px', 
    background: '#28a745', 
    color: 'white' 
  };

  return (
    <div>
      <div>
        <h4>Stretch (default)</h4>
        <Base.Flex align="stretch" gap="8px" style={containerStyle}>
          <div style={itemStyle}>A</div>
          <div style={itemStyle}>B</div>
          <div style={itemStyle}>C</div>
        </Base.Flex>
      </div>
      
      <div>
        <h4>Flex Start</h4>
        <Base.Flex align="flex-start" gap="8px" style={containerStyle}>
          <div style={itemStyle}>A</div>
          <div style={itemStyle}>B</div>
          <div style={itemStyle}>C</div>
        </Base.Flex>
      </div>
      
      <div>
        <h4>Center</h4>
        <Base.Flex align="center" gap="8px" style={containerStyle}>
          <div style={itemStyle}>A</div>
          <div style={itemStyle}>B</div>
          <div style={itemStyle}>C</div>
        </Base.Flex>
      </div>
      
      <div>
        <h4>Flex End</h4>
        <Base.Flex align="flex-end" gap="8px" style={containerStyle}>
          <div style={itemStyle}>A</div>
          <div style={itemStyle}>B</div>
          <div style={itemStyle}>C</div>
        </Base.Flex>
      </div>
      
      <div>
        <h4>Baseline</h4>
        <Base.Flex align="baseline" gap="8px" style={containerStyle}>
          <div style={{...itemStyle, fontSize: '12px'}}>Small</div>
          <div style={{...itemStyle, fontSize: '16px'}}>Medium</div>
          <div style={{...itemStyle, fontSize: '20px'}}>Large</div>
        </Base.Flex>
      </div>
    </div>
  );
}
```

## Responsive Design

### Responsive Direction

```tsx
function ResponsiveFlex() {
  return (
    <Base.Flex 
      direction={{ base: 'column', md: 'row' }}
      gap={{ base: '8px', md: '16px' }}
      align={{ base: 'stretch', md: 'center' }}
    >
      <div style={{ padding: '16px', background: '#ff6b6b', flex: 1 }}>
        Responsive Item 1
      </div>
      <div style={{ padding: '16px', background: '#4ecdc4', flex: 1 }}>
        Responsive Item 2
      </div>
      <div style={{ padding: '16px', background: '#45b7d1', flex: 1 }}>
        Responsive Item 3
      </div>
    </Base.Flex>
  );
}
```

### Responsive Gaps

```tsx
function ResponsiveGaps() {
  return (
    <Base.Flex 
      gap={{
        base: '4px',
        sm: '8px',
        md: '16px',
        lg: '24px',
        xl: '32px'
      }}
      wrap="wrap"
    >
      {Array.from({ length: 6 }, (_, i) => (
        <div 
          key={i}
          style={{ 
            padding: '12px', 
            background: '#6c757d', 
            color: 'white',
            minWidth: '120px'
          }}
        >
          Item {i + 1}
        </div>
      ))}
    </Base.Flex>
  );
}
```

## Advanced Features

### Flex Item Properties

```tsx
function FlexItemProps() {
  return (
    <Base.Flex gap="8px" style={{ height: '120px' }}>
      <Base.Flex.Item 
        grow={0} 
        shrink={0} 
        basis="100px"
        style={{ background: '#dc3545', color: 'white', padding: '8px' }}
      >
        Fixed 100px
      </Base.Flex.Item>
      
      <Base.Flex.Item 
        grow={1}
        style={{ background: '#28a745', color: 'white', padding: '8px' }}
      >
        Grows (flex: 1)
      </Base.Flex.Item>
      
      <Base.Flex.Item 
        grow={2}
        style={{ background: '#007bff', color: 'white', padding: '8px' }}
      >
        Grows More (flex: 2)
      </Base.Flex.Item>
      
      <Base.Flex.Item 
        alignSelf="center"
        style={{ background: '#6f42c1', color: 'white', padding: '8px' }}
      >
        Self Centered
      </Base.Flex.Item>
    </Base.Flex>
  );
}
```

### Wrapping Behavior

```tsx
function WrappingFlex() {
  const items = Array.from({ length: 10 }, (_, i) => (
    <div 
      key={i}
      style={{ 
        padding: '12px 16px', 
        background: '#17a2b8', 
        color: 'white',
        minWidth: '120px'
      }}
    >
      Item {i + 1}
    </div>
  ));

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div>
        <h4>No Wrap (default)</h4>
        <Base.Flex gap="8px" style={{ border: '1px solid #ccc', padding: '8px' }}>
          {items.slice(0, 5)}
        </Base.Flex>
      </div>
      
      <div>
        <h4>Wrap</h4>
        <Base.Flex wrap="wrap" gap="8px" style={{ border: '1px solid #ccc', padding: '8px' }}>
          {items}
        </Base.Flex>
      </div>
      
      <div>
        <h4>Wrap Reverse</h4>
        <Base.Flex wrap="wrap-reverse" gap="8px" style={{ border: '1px solid #ccc', padding: '8px' }}>
          {items}
        </Base.Flex>
      </div>
    </div>
  );
}
```

### Complex Layouts

```tsx
function ComplexLayout() {
  return (
    <Base.Flex direction="column" gap="16px" style={{ height: '400px' }}>
      {/* Header */}
      <Base.Flex 
        justify="space-between" 
        align="center"
        style={{ 
          padding: '16px', 
          background: '#343a40', 
          color: 'white' 
        }}
      >
        <h2>Header</h2>
        <nav>
          <Base.Flex gap="16px">
            <a href="#" style={{ color: 'white' }}>Home</a>
            <a href="#" style={{ color: 'white' }}>About</a>
            <a href="#" style={{ color: 'white' }}>Contact</a>
          </Base.Flex>
        </nav>
      </Base.Flex>
      
      {/* Main Content */}
      <Base.Flex grow={1} gap="16px">
        {/* Sidebar */}
        <Base.Flex.Item 
          basis="200px" 
          shrink={0}
          style={{ 
            background: '#f8f9fa', 
            padding: '16px' 
          }}
        >
          <h3>Sidebar</h3>
          <Base.Flex direction="column" gap="8px">
            <a href="#">Link 1</a>
            <a href="#">Link 2</a>
            <a href="#">Link 3</a>
          </Base.Flex>
        </Base.Flex.Item>
        
        {/* Content */}
        <Base.Flex.Item 
          grow={1}
          style={{ 
            background: '#ffffff', 
            padding: '16px',
            border: '1px solid #dee2e6'
          }}
        >
          <h3>Main Content</h3>
          <p>This is the main content area that grows to fill available space.</p>
        </Base.Flex.Item>
      </Base.Flex>
      
      {/* Footer */}
      <Base.Flex 
        justify="center" 
        align="center"
        style={{ 
          padding: '16px', 
          background: '#6c757d', 
          color: 'white' 
        }}
      >
        <p>Footer Content</p>
      </Base.Flex>
    </Base.Flex>
  );
}
```

## Props

### Base.Flex Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `direction` | `FlexDirection \| ResponsiveValue<FlexDirection>` | `'row'` | Flex direction |
| `wrap` | `FlexWrap \| ResponsiveValue<FlexWrap>` | `'nowrap'` | Flex wrap behavior |
| `justify` | `JustifyContent \| ResponsiveValue<JustifyContent>` | `'flex-start'` | Justify content alignment |
| `align` | `AlignItems \| ResponsiveValue<AlignItems>` | `'stretch'` | Align items alignment |
| `alignContent` | `AlignContent \| ResponsiveValue<AlignContent>` | `'stretch'` | Align content for wrapped lines |
| `gap` | `SpaceValue \| ResponsiveValue<SpaceValue>` | - | Gap between items |
| `rowGap` | `SpaceValue \| ResponsiveValue<SpaceValue>` | - | Gap between rows |
| `columnGap` | `SpaceValue \| ResponsiveValue<SpaceValue>` | - | Gap between columns |
| `grow` | `number \| ResponsiveValue<number>` | - | Flex grow for all children |
| `shrink` | `number \| ResponsiveValue<number>` | - | Flex shrink for all children |
| `basis` | `FlexBasis \| ResponsiveValue<FlexBasis>` | - | Flex basis for all children |
| `inline` | `boolean` | `false` | Use inline-flex display |
| `children` | `ReactNode` | - | Child elements |
| `className` | `string` | - | Additional CSS class names |
| `style` | `CSSProperties` | - | Inline styles |

### Base.Flex.Item Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `grow` | `number \| ResponsiveValue<number>` | - | Flex grow value |
| `shrink` | `number \| ResponsiveValue<number>` | - | Flex shrink value |
| `basis` | `FlexBasis \| ResponsiveValue<FlexBasis>` | - | Flex basis value |
| `alignSelf` | `AlignSelf \| ResponsiveValue<AlignSelf>` | - | Individual align self |
| `order` | `number \| ResponsiveValue<number>` | - | Flex order |
| `children` | `ReactNode` | - | Child elements |
| `className` | `string` | - | Additional CSS class names |
| `style` | `CSSProperties` | - | Inline styles |

### Type Definitions

```typescript
type FlexDirection = 'row' | 'row-reverse' | 'column' | 'column-reverse';
type FlexWrap = 'nowrap' | 'wrap' | 'wrap-reverse';
type JustifyContent = 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly';
type AlignItems = 'stretch' | 'flex-start' | 'flex-end' | 'center' | 'baseline';
type AlignContent = 'stretch' | 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around';
type AlignSelf = 'auto' | 'flex-start' | 'flex-end' | 'center' | 'baseline' | 'stretch';
type FlexBasis = string | number | 'auto' | 'content';
type SpaceValue = string | number;

type ResponsiveValue<T> = T | {
  base?: T;
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
  '2xl'?: T;
};
```

## Best Practices

### Do's

- ✅ Use Flex for one-dimensional layouts (row or column)
- ✅ Leverage responsive props for mobile-first design
- ✅ Use gap instead of margins for consistent spacing
- ✅ Combine with Flex.Item for precise control
- ✅ Use semantic HTML elements as children
- ✅ Test layouts across different screen sizes
- ✅ Use appropriate flex properties for content behavior

### Don'ts

- ❌ Don't use Flex for complex two-dimensional layouts (use CSS Grid)
- ❌ Don't rely solely on flex for all spacing needs
- ❌ Don't forget to test with varying content lengths
- ❌ Don't use fixed widths when flex properties would work better
- ❌ Don't nest too many Flex containers unnecessarily
- ❌ Don't ignore accessibility when creating layouts
- ❌ Don't use Flex when a simple block layout would suffice

## Performance

### Optimization Tips

```tsx
// ✅ Good: Memoize complex layouts
const ComplexFlexLayout = React.memo(function ComplexFlexLayout({ items }) {
  return (
    <Base.Flex direction="column" gap="16px">
      {items.map(item => (
        <Base.Flex.Item key={item.id} grow={item.weight}>
          {item.content}
        </Base.Flex.Item>
      ))}
    </Base.Flex>
  );
});

// ✅ Good: Use responsive values efficiently
const responsiveGap = { base: '8px', md: '16px' };

function EfficientResponsive() {
  return (
    <Base.Flex gap={responsiveGap}>
      {/* content */}
    </Base.Flex>
  );
}

// ✅ Good: Avoid unnecessary re-renders
function OptimizedFlex({ children, ...flexProps }) {
  const memoizedProps = React.useMemo(() => flexProps, [JSON.stringify(flexProps)]);
  
  return (
    <Base.Flex {...memoizedProps}>
      {children}
    </Base.Flex>
  );
}
```

### Bundle Size

- Minimal CSS footprint with modern flexbox
- Tree-shakeable responsive utilities
- No external dependencies
- Optimized for modern browsers

### Runtime Performance

- Efficient CSS generation for responsive values
- Minimal DOM manipulation
- Hardware-accelerated layouts
- Optimized re-rendering with proper memoization