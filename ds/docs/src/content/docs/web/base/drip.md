---
title: Base.Drip
description: Ripple effect component for creating material design-style touch feedback animations.
sidebar:
  order: 4
---

Ripple effect component for creating material design-style touch feedback animations in Miss UI Web.

## Features

- **Material Design Ripples** - Authentic material design ripple effects
- **Touch Feedback** - Visual feedback for user interactions
- **Customizable Animation** - Control duration, easing, and appearance
- **Multiple Triggers** - Click, touch, and programmatic triggers
- **Color Customization** - Custom ripple colors and opacity
- **Performance Optimized** - Efficient animations with minimal overhead
- **Accessibility Friendly** - Respects user motion preferences
- **Cross-platform** - Works on desktop and mobile devices

## Basic Usage

### Simple Ripple Effect

```tsx
import { Base } from '@miss-ui/web';

function BasicRipple() {
  return (
    <div style={{ position: 'relative', padding: '20px', background: '#f0f0f0' }}>
      <Base.Drip />
      Click anywhere in this area
    </div>
  );
}
```

### Button with Ripple

```tsx
function RippleButton() {
  return (
    <button 
      style={{ 
        position: 'relative', 
        padding: '12px 24px',
        border: 'none',
        borderRadius: '8px',
        background: '#007bff',
        color: 'white',
        cursor: 'pointer',
        overflow: 'hidden'
      }}
    >
      <Base.Drip color="rgba(255, 255, 255, 0.3)" />
      Click me
    </button>
  );
}
```

### Card with Ripple

```tsx
function RippleCard() {
  return (
    <div 
      style={{ 
        position: 'relative',
        padding: '24px',
        border: '1px solid #e0e0e0',
        borderRadius: '12px',
        cursor: 'pointer',
        overflow: 'hidden'
      }}
    >
      <Base.Drip />
      <h3>Interactive Card</h3>
      <p>Click anywhere on this card to see the ripple effect.</p>
    </div>
  );
}
```

## Variants

### Different Colors

```tsx
function ColoredRipples() {
  return (
    <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
      <button style={{ position: 'relative', padding: '12px 24px', overflow: 'hidden' }}>
        <Base.Drip color="rgba(255, 0, 0, 0.3)" />
        Red Ripple
      </button>
      
      <button style={{ position: 'relative', padding: '12px 24px', overflow: 'hidden' }}>
        <Base.Drip color="rgba(0, 255, 0, 0.3)" />
        Green Ripple
      </button>
      
      <button style={{ position: 'relative', padding: '12px 24px', overflow: 'hidden' }}>
        <Base.Drip color="rgba(0, 0, 255, 0.3)" />
        Blue Ripple
      </button>
      
      <button style={{ position: 'relative', padding: '12px 24px', overflow: 'hidden' }}>
        <Base.Drip color="currentColor" opacity={0.2} />
        Current Color
      </button>
    </div>
  );
}
```

### Different Sizes

```tsx
function SizedRipples() {
  return (
    <div style={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
      <button style={{ position: 'relative', padding: '8px 16px', overflow: 'hidden' }}>
        <Base.Drip size="small" />
        Small
      </button>
      
      <button style={{ position: 'relative', padding: '12px 24px', overflow: 'hidden' }}>
        <Base.Drip size="medium" />
        Medium
      </button>
      
      <button style={{ position: 'relative', padding: '16px 32px', overflow: 'hidden' }}>
        <Base.Drip size="large" />
        Large
      </button>
      
      <button style={{ position: 'relative', padding: '12px 24px', overflow: 'hidden' }}>
        <Base.Drip maxRadius={100} />
        Custom Size
      </button>
    </div>
  );
}
```

### Animation Variants

```tsx
function AnimationVariants() {
  return (
    <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
      <button style={{ position: 'relative', padding: '12px 24px', overflow: 'hidden' }}>
        <Base.Drip duration={200} />
        Fast (200ms)
      </button>
      
      <button style={{ position: 'relative', padding: '12px 24px', overflow: 'hidden' }}>
        <Base.Drip duration={600} />
        Default (600ms)
      </button>
      
      <button style={{ position: 'relative', padding: '12px 24px', overflow: 'hidden' }}>
        <Base.Drip duration={1000} />
        Slow (1000ms)
      </button>
      
      <button style={{ position: 'relative', padding: '12px 24px', overflow: 'hidden' }}>
        <Base.Drip easing="ease-out" />
        Ease Out
      </button>
    </div>
  );
}
```

## Advanced Features

### Programmatic Triggers

```tsx
function ProgrammaticRipple() {
  const dripRef = React.useRef<any>(null);

  const triggerRipple = (event: React.MouseEvent) => {
    if (dripRef.current) {
      dripRef.current.trigger(event);
    }
  };

  const triggerCenterRipple = () => {
    if (dripRef.current) {
      dripRef.current.triggerFromCenter();
    }
  };

  return (
    <div>
      <div 
        style={{ 
          position: 'relative',
          padding: '40px',
          border: '2px dashed #ccc',
          marginBottom: '16px',
          overflow: 'hidden'
        }}
      >
        <Base.Drip ref={dripRef} />
        Ripple target area
      </div>
      
      <div style={{ display: 'flex', gap: '8px' }}>
        <button onClick={triggerRipple}>
          Trigger at Click
        </button>
        <button onClick={triggerCenterRipple}>
          Trigger from Center
        </button>
      </div>
    </div>
  );
}
```

### Multiple Ripples

```tsx
function MultipleRipples() {
  return (
    <div 
      style={{ 
        position: 'relative',
        padding: '40px',
        background: 'linear-gradient(45deg, #ff6b6b, #4ecdc4)',
        borderRadius: '12px',
        color: 'white',
        textAlign: 'center',
        overflow: 'hidden'
      }}
    >
      <Base.Drip 
        color="rgba(255, 255, 255, 0.3)"
        allowMultiple
      />
      <h3>Multiple Ripples</h3>
      <p>Click rapidly to see multiple ripples at once</p>
    </div>
  );
}
```

### Conditional Ripples

```tsx
function ConditionalRipple() {
  const [enabled, setEnabled] = React.useState(true);

  return (
    <div>
      <div style={{ marginBottom: '16px' }}>
        <label>
          <input 
            type="checkbox" 
            checked={enabled}
            onChange={(e) => setEnabled(e.target.checked)}
          />
          Enable ripple effect
        </label>
      </div>
      
      <button 
        style={{ 
          position: 'relative',
          padding: '12px 24px',
          border: 'none',
          borderRadius: '8px',
          background: enabled ? '#007bff' : '#6c757d',
          color: 'white',
          cursor: 'pointer',
          overflow: 'hidden'
        }}
      >
        {enabled && <Base.Drip />}
        {enabled ? 'Ripple Enabled' : 'Ripple Disabled'}
      </button>
    </div>
  );
}
```

### Custom Ripple Shapes

```tsx
function CustomShapes() {
  return (
    <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
      <button style={{ position: 'relative', padding: '12px 24px', overflow: 'hidden' }}>
        <Base.Drip shape="circle" />
        Circle
      </button>
      
      <button style={{ position: 'relative', padding: '12px 24px', overflow: 'hidden' }}>
        <Base.Drip shape="square" />
        Square
      </button>
      
      <button 
        style={{ 
          position: 'relative', 
          padding: '12px 24px', 
          borderRadius: '50px',
          overflow: 'hidden'
        }}
      >
        <Base.Drip shape="inherit" />
        Inherit Shape
      </button>
    </div>
  );
}
```

### Ripple with Callbacks

```tsx
function CallbackRipple() {
  const [rippleCount, setRippleCount] = React.useState(0);
  const [isAnimating, setIsAnimating] = React.useState(false);

  return (
    <div>
      <div style={{ marginBottom: '16px' }}>
        <p>Ripples triggered: {rippleCount}</p>
        <p>Currently animating: {isAnimating ? 'Yes' : 'No'}</p>
      </div>
      
      <button 
        style={{ 
          position: 'relative',
          padding: '16px 32px',
          border: 'none',
          borderRadius: '8px',
          background: '#28a745',
          color: 'white',
          cursor: 'pointer',
          overflow: 'hidden'
        }}
      >
        <Base.Drip 
          onStart={() => {
            setRippleCount(count => count + 1);
            setIsAnimating(true);
          }}
          onComplete={() => {
            setIsAnimating(false);
          }}
        />
        Click to Track Ripples
      </button>
    </div>
  );
}
```

## Props

### Base.Drip Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `color` | `string` | `'rgba(0, 0, 0, 0.1)'` | Color of the ripple effect |
| `opacity` | `number` | `1` | Opacity of the ripple |
| `duration` | `number` | `600` | Animation duration in milliseconds |
| `easing` | `string` | `'ease-out'` | CSS easing function |
| `size` | `'small' \| 'medium' \| 'large'` | `'medium'` | Predefined size of ripple |
| `maxRadius` | `number` | - | Maximum radius of ripple in pixels |
| `shape` | `'circle' \| 'square' \| 'inherit'` | `'circle'` | Shape of the ripple |
| `allowMultiple` | `boolean` | `false` | Allow multiple simultaneous ripples |
| `disabled` | `boolean` | `false` | Disable ripple effect |
| `trigger` | `'click' \| 'mousedown' \| 'touchstart'` | `'click'` | Event that triggers ripple |
| `center` | `boolean` | `false` | Always trigger from center |
| `bounded` | `boolean` | `true` | Constrain ripple to container bounds |
| `onStart` | `(event: Event) => void` | - | Callback when ripple starts |
| `onComplete` | `() => void` | - | Callback when ripple completes |
| `className` | `string` | - | Additional CSS class names |
| `style` | `CSSProperties` | - | Inline styles for container |

### Ref Methods

```typescript
interface DripRef {
  trigger: (event?: MouseEvent | TouchEvent) => void;
  triggerFromCenter: () => void;
  clear: () => void;
}
```

## Accessibility

### Motion Preferences

```tsx
function AccessibleRipple() {
  const [respectsMotion, setRespectsMotion] = React.useState(true);

  React.useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setRespectsMotion(!mediaQuery.matches);
    
    const handler = (e: MediaQueryListEvent) => {
      setRespectsMotion(!e.matches);
    };
    
    mediaQuery.addEventListener('change', handler);
    return () => mediaQuery.removeEventListener('change', handler);
  }, []);

  return (
    <button 
      style={{ 
        position: 'relative',
        padding: '12px 24px',
        overflow: 'hidden'
      }}
    >
      {respectsMotion && <Base.Drip />}
      Accessible Ripple
    </button>
  );
}
```

### Screen Reader Considerations

- Ripple effects are purely visual and don't affect screen readers
- Ensure interactive elements have proper ARIA labels
- Don't rely on ripples alone to indicate interactivity

### Focus Indicators

```tsx
function FocusRipple() {
  return (
    <button 
      style={{ 
        position: 'relative',
        padding: '12px 24px',
        border: '2px solid transparent',
        borderRadius: '8px',
        overflow: 'hidden'
      }}
      onFocus={(e) => {
        e.currentTarget.style.borderColor = '#007bff';
      }}
      onBlur={(e) => {
        e.currentTarget.style.borderColor = 'transparent';
      }}
    >
      <Base.Drip trigger="click" />
      Focus and Click Me
    </button>
  );
}
```

## Best Practices

### Do's

- ✅ Use ripples for interactive elements (buttons, cards, list items)
- ✅ Choose ripple colors that contrast well with the background
- ✅ Respect user motion preferences
- ✅ Keep animation durations reasonable (200-800ms)
- ✅ Ensure parent container has `position: relative` and `overflow: hidden`
- ✅ Use appropriate opacity for subtle feedback
- ✅ Test on both desktop and mobile devices

### Don'ts

- ❌ Don't use ripples on non-interactive elements
- ❌ Don't make ripples too bright or distracting
- ❌ Don't use very long animation durations
- ❌ Don't forget to handle reduced motion preferences
- ❌ Don't use ripples as the only indicator of interactivity
- ❌ Don't nest ripple containers
- ❌ Don't use ripples on elements that navigate away immediately

## Performance

### Optimization Tips

```tsx
// ✅ Good: Conditional rendering for better performance
function OptimizedRipple({ interactive }: { interactive: boolean }) {
  return (
    <div style={{ position: 'relative', overflow: 'hidden' }}>
      {interactive && <Base.Drip />}
      Content
    </div>
  );
}

// ✅ Good: Memoize ripple components
const MemoizedRipple = React.memo(Base.Drip);

// ✅ Good: Use ref for programmatic control
function EfficientRipple() {
  const dripRef = React.useRef<any>(null);
  
  const handleInteraction = React.useCallback((event: React.MouseEvent) => {
    dripRef.current?.trigger(event);
  }, []);

  return (
    <div onClick={handleInteraction}>
      <Base.Drip ref={dripRef} />
      Efficient Ripple
    </div>
  );
}
```

### Bundle Size

- Minimal CSS and JavaScript footprint
- No external animation libraries required
- Tree-shakeable implementation
- Optimized for modern browsers

### Runtime Performance

- Hardware-accelerated animations using CSS transforms
- Efficient event handling with passive listeners
- Automatic cleanup of completed animations
- Minimal DOM manipulation