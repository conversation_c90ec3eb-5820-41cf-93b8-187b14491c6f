---
title: Base.Pre
description: Preformatted text component for displaying code blocks and formatted content with syntax highlighting.
sidebar:
  order: 7
---

Preformatted text component for displaying code blocks and formatted content with syntax highlighting in Miss UI Web.

## Features

- **Syntax Highlighting** - Support for multiple programming languages
- **Line Numbers** - Optional line numbering with customizable start
- **Copy Functionality** - Built-in copy-to-clipboard feature
- **Code Wrapping** - Configurable text wrapping behavior
- **Theme Support** - Multiple color themes for different contexts
- **Scrollable Content** - Horizontal and vertical scrolling for large content
- **Accessibility** - Screen reader friendly with proper semantics
- **Performance Optimized** - Efficient rendering for large code blocks

## Basic Usage

### Simple Preformatted Text

```tsx
import { Base } from '@miss-ui/web';

function BasicPre() {
  const codeContent = `function greet(name) {
  return \`Hello, \${name}!\`;
}

console.log(greet('World'));`;

  return (
    <Base.Pre>
      {codeContent}
    </Base.Pre>
  );
}
```

### With Syntax Highlighting

```tsx
function SyntaxHighlightedPre() {
  const jsCode = `const users = [
  { id: 1, name: 'Alice', active: true },
  { id: 2, name: '<PERSON>', active: false },
  { id: 3, name: 'Charlie', active: true }
];

const activeUsers = users.filter(user => user.active);
console.log(activeUsers);`;

  return (
    <Base.Pre language="javascript" showLineNumbers>
      {jsCode}
    </Base.Pre>
  );
}
```

### With Copy Button

```tsx
function CopyablePre() {
  const bashCode = `# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build`;

  return (
    <Base.Pre 
      language="bash"
      copyable
      filename="setup.sh"
    >
      {bashCode}
    </Base.Pre>
  );
}
```

## Language Support

### Multiple Languages

```tsx
function MultiLanguageExamples() {
  const examples = {
    javascript: `const message = "Hello, World!";
console.log(message);`,
    
    python: `message = "Hello, World!"
print(message)`,
    
    css: `.container {
  display: flex;
  justify-content: center;
  align-items: center;
}`,
    
    html: `<!DOCTYPE html>
<html>
<head>
  <title>Hello World</title>
</head>
<body>
  <h1>Hello, World!</h1>
</body>
</html>`,
    
    json: `{
  "name": "my-project",
  "version": "1.0.0",
  "dependencies": {
    "react": "^18.0.0"
  }
}`,
    
    sql: `SELECT users.name, COUNT(orders.id) as order_count
FROM users
LEFT JOIN orders ON users.id = orders.user_id
GROUP BY users.id
ORDER BY order_count DESC;`
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      {Object.entries(examples).map(([lang, code]) => (
        <div key={lang}>
          <h4 style={{ marginBottom: '8px' }}>
            {lang.charAt(0).toUpperCase() + lang.slice(1)}
          </h4>
          <Base.Pre 
            language={lang}
            showLineNumbers
            copyable
          >
            {code}
          </Base.Pre>
        </div>
      ))}
    </div>
  );
}
```

## Themes

### Different Color Themes

```tsx
function ThemedPre() {
  const sampleCode = `function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

console.log(fibonacci(10));`;

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div>
        <h4>Light Theme</h4>
        <Base.Pre 
          language="javascript"
          theme="light"
          showLineNumbers
        >
          {sampleCode}
        </Base.Pre>
      </div>
      
      <div>
        <h4>Dark Theme</h4>
        <Base.Pre 
          language="javascript"
          theme="dark"
          showLineNumbers
        >
          {sampleCode}
        </Base.Pre>
      </div>
      
      <div>
        <h4>GitHub Theme</h4>
        <Base.Pre 
          language="javascript"
          theme="github"
          showLineNumbers
        >
          {sampleCode}
        </Base.Pre>
      </div>
      
      <div>
        <h4>VS Code Theme</h4>
        <Base.Pre 
          language="javascript"
          theme="vscode"
          showLineNumbers
        >
          {sampleCode}
        </Base.Pre>
      </div>
    </div>
  );
}
```

## Advanced Features

### Line Highlighting

```tsx
function LineHighlighting() {
  const codeWithHighlights = `function processData(data) {
  // Validate input data
  if (!data || !Array.isArray(data)) {
    throw new Error('Invalid data format');
  }
  
  // Process each item
  const processed = data.map(item => {
    return {
      ...item,
      processed: true,
      timestamp: Date.now()
    };
  });
  
  // Return processed data
  return processed;
}`;

  return (
    <Base.Pre 
      language="javascript"
      showLineNumbers
      highlightLines={[3, 4, 7, 8, 9, 10, 11]}
      copyable
    >
      {codeWithHighlights}
    </Base.Pre>
  );
}
```

### Custom Line Numbers

```tsx
function CustomLineNumbers() {
  const codeSnippet = `export default function Component() {
  const [count, setCount] = useState(0);
  
  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={() => setCount(count + 1)}>
        Increment
      </button>
    </div>
  );
}`;

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <div>
        <h4>Starting from line 1</h4>
        <Base.Pre 
          language="jsx"
          showLineNumbers
          startLineNumber={1}
        >
          {codeSnippet}
        </Base.Pre>
      </div>
      
      <div>
        <h4>Starting from line 42</h4>
        <Base.Pre 
          language="jsx"
          showLineNumbers
          startLineNumber={42}
        >
          {codeSnippet}
        </Base.Pre>
      </div>
    </div>
  );
}
```

### Scrollable Content

```tsx
function ScrollablePre() {
  const longCode = Array.from({ length: 50 }, (_, i) => 
    `console.log('This is line ${i + 1} of a very long code block');`
  ).join('\n');

  const wideCode = `const veryLongVariableName = 'This is a very long line that will require horizontal scrolling to see completely';
const anotherLongLine = 'Another extremely long line of code that demonstrates horizontal scrolling behavior in the Pre component';
console.log(veryLongVariableName, anotherLongLine);`;

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div>
        <h4>Vertical Scrolling (Max Height: 200px)</h4>
        <Base.Pre 
          language="javascript"
          showLineNumbers
          maxHeight="200px"
          copyable
        >
          {longCode}
        </Base.Pre>
      </div>
      
      <div>
        <h4>Horizontal Scrolling</h4>
        <Base.Pre 
          language="javascript"
          showLineNumbers
          wrap={false}
          copyable
        >
          {wideCode}
        </Base.Pre>
      </div>
    </div>
  );
}
```

### Interactive Features

```tsx
function InteractivePre() {
  const [theme, setTheme] = React.useState('light');
  const [showLineNumbers, setShowLineNumbers] = React.useState(true);
  const [wrapLines, setWrapLines] = React.useState(true);

  const sampleCode = `import React, { useState, useEffect } from 'react';

function Timer() {
  const [seconds, setSeconds] = useState(0);
  
  useEffect(() => {
    const interval = setInterval(() => {
      setSeconds(prev => prev + 1);
    }, 1000);
    
    return () => clearInterval(interval);
  }, []);
  
  return <div>Timer: {seconds}s</div>;
}`;

  return (
    <div>
      <div style={{ 
        display: 'flex', 
        gap: '16px', 
        marginBottom: '16px',
        padding: '16px',
        background: '#f8f9fa',
        borderRadius: '8px'
      }}>
        <label>
          <select 
            value={theme} 
            onChange={(e) => setTheme(e.target.value)}
            style={{ padding: '4px 8px' }}
          >
            <option value="light">Light</option>
            <option value="dark">Dark</option>
            <option value="github">GitHub</option>
            <option value="vscode">VS Code</option>
          </select>
          Theme
        </label>
        
        <label>
          <input 
            type="checkbox" 
            checked={showLineNumbers}
            onChange={(e) => setShowLineNumbers(e.target.checked)}
          />
          Line Numbers
        </label>
        
        <label>
          <input 
            type="checkbox" 
            checked={wrapLines}
            onChange={(e) => setWrapLines(e.target.checked)}
          />
          Wrap Lines
        </label>
      </div>
      
      <Base.Pre 
        language="jsx"
        theme={theme}
        showLineNumbers={showLineNumbers}
        wrap={wrapLines}
        copyable
        filename="Timer.jsx"
      >
        {sampleCode}
      </Base.Pre>
    </div>
  );
}
```

### Diff Display

```tsx
function DiffPre() {
  const diffContent = `  function greet(name) {
-   return 'Hello ' + name;
+   return \`Hello, \${name}!\`;
  }
  
+ function farewell(name) {
+   return \`Goodbye, \${name}!\`;
+ }
  
  console.log(greet('World'));
+ console.log(farewell('World'));`;

  return (
    <Base.Pre 
      language="diff"
      showLineNumbers
      copyable
      filename="changes.diff"
    >
      {diffContent}
    </Base.Pre>
  );
}
```

## Props

### Base.Pre Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `children` | `string` | - | Code content to display |
| `language` | `string` | - | Programming language for syntax highlighting |
| `theme` | `PreTheme` | `'default'` | Color theme for syntax highlighting |
| `showLineNumbers` | `boolean` | `false` | Whether to show line numbers |
| `startLineNumber` | `number` | `1` | Starting line number |
| `highlightLines` | `number[]` | - | Array of line numbers to highlight |
| `copyable` | `boolean` | `false` | Whether to show copy button |
| `copyText` | `string` | - | Custom text to copy (defaults to children) |
| `onCopy` | `() => void` | - | Callback when code is copied |
| `filename` | `string` | - | Filename to display in header |
| `maxHeight` | `string \| number` | - | Maximum height with scroll |
| `maxWidth` | `string \| number` | - | Maximum width with scroll |
| `wrap` | `boolean` | `true` | Whether to wrap long lines |
| `tabSize` | `number` | `2` | Number of spaces for tab character |
| `fontSize` | `string \| number` | - | Font size override |
| `lineHeight` | `string \| number` | - | Line height override |
| `className` | `string` | - | Additional CSS class names |
| `style` | `CSSProperties` | - | Inline styles |
| `aria-label` | `string` | - | Accessible label |
| `role` | `string` | `'code'` | ARIA role |

### Type Definitions

```typescript
type PreTheme = 
  | 'default'
  | 'light'
  | 'dark'
  | 'github'
  | 'github-dark'
  | 'vscode'
  | 'vscode-dark'
  | 'monokai'
  | 'solarized-light'
  | 'solarized-dark'
  | 'atom-dark'
  | 'material'
  | 'dracula';

type SupportedLanguage = 
  | 'javascript'
  | 'typescript'
  | 'jsx'
  | 'tsx'
  | 'css'
  | 'scss'
  | 'sass'
  | 'less'
  | 'html'
  | 'xml'
  | 'json'
  | 'yaml'
  | 'markdown'
  | 'bash'
  | 'shell'
  | 'powershell'
  | 'python'
  | 'java'
  | 'c'
  | 'cpp'
  | 'csharp'
  | 'php'
  | 'ruby'
  | 'go'
  | 'rust'
  | 'swift'
  | 'kotlin'
  | 'sql'
  | 'graphql'
  | 'dockerfile'
  | 'nginx'
  | 'apache'
  | 'diff';
```

## Accessibility

### Screen Reader Support

```tsx
function AccessiblePre() {
  const codeContent = `function calculateTotal(items) {
  return items.reduce((sum, item) => sum + item.price, 0);
}`;

  return (
    <div>
      <h3 id="code-heading">Price calculation function</h3>
      <Base.Pre 
        language="javascript"
        aria-labelledby="code-heading"
        aria-describedby="code-description"
        role="code"
      >
        {codeContent}
      </Base.Pre>
      <p id="code-description" style={{ fontSize: '14px', color: '#6c757d' }}>
        This function takes an array of items and calculates the total price.
      </p>
    </div>
  );
}
```

### Keyboard Navigation

- **Tab** - Navigate to copy button when copyable
- **Enter/Space** - Activate copy button
- **Arrow Keys** - Scroll content when focused
- **Home/End** - Navigate to beginning/end of content
- **Page Up/Page Down** - Scroll by page when content is scrollable

### Focus Management

```tsx
function FocusManagement() {
  const preRef = React.useRef<HTMLPreElement>(null);

  const focusCode = () => {
    preRef.current?.focus();
  };

  return (
    <div>
      <button onClick={focusCode}>Focus code block</button>
      
      <Base.Pre 
        ref={preRef}
        language="javascript"
        tabIndex={0}
        copyable
        style={{
          outline: '2px solid transparent',
          outlineOffset: '2px'
        }}
        onFocus={(e) => {
          e.currentTarget.style.outline = '2px solid #007bff';
        }}
        onBlur={(e) => {
          e.currentTarget.style.outline = '2px solid transparent';
        }}
      >
        {`const focusable = true;
console.log('This code block is focusable');`}
      </Base.Pre>
    </div>
  );
}
```

## Best Practices

### Do's

- ✅ Use appropriate language specification for syntax highlighting
- ✅ Provide meaningful filenames for context
- ✅ Use line numbers for longer code blocks
- ✅ Choose themes that match your application's design
- ✅ Provide copy functionality for code users might need
- ✅ Use proper ARIA labels and descriptions
- ✅ Test with screen readers and keyboard navigation

### Don'ts

- ❌ Don't use Pre for non-code content
- ❌ Don't make code blocks too wide without horizontal scrolling
- ❌ Don't use overly bright or distracting themes
- ❌ Don't forget to specify language for better highlighting
- ❌ Don't ignore accessibility attributes
- ❌ Don't use Pre for very large files (consider pagination)
- ❌ Don't forget to handle empty or invalid content

## Performance

### Optimization Tips

```tsx
// ✅ Good: Memoize large code blocks
const LargeCodeBlock = React.memo(function LargeCodeBlock({ code, language }) {
  return (
    <Base.Pre 
      language={language}
      showLineNumbers
      maxHeight="400px"
    >
      {code}
    </Base.Pre>
  );
});

// ✅ Good: Lazy load syntax highlighting
function LazyHighlightedPre({ code, language }) {
  const [shouldHighlight, setShouldHighlight] = React.useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setShouldHighlight(true), 100);
    return () => clearTimeout(timer);
  }, []);

  return (
    <Base.Pre 
      language={shouldHighlight ? language : undefined}
      showLineNumbers
    >
      {code}
    </Base.Pre>
  );
}

// ✅ Good: Virtualize very long code blocks
function VirtualizedPre({ lines, language }) {
  const [visibleRange, setVisibleRange] = React.useState({ start: 0, end: 100 });
  const visibleCode = lines.slice(visibleRange.start, visibleRange.end).join('\n');

  return (
    <Base.Pre 
      language={language}
      showLineNumbers
      startLineNumber={visibleRange.start + 1}
      maxHeight="400px"
    >
      {visibleCode}
    </Base.Pre>
  );
}
```

### Bundle Size

- Syntax highlighting is loaded on demand
- Tree-shakeable language support
- Optimized theme loading
- Minimal core bundle impact

### Runtime Performance

- Efficient syntax highlighting engine
- Memoized highlighting results
- Optimized scrolling for large content
- Lazy loading of non-critical features