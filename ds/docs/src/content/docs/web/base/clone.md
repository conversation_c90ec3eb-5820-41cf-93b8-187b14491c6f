---
title: Base.Clone
description: Utility component for cloning React elements with additional props and functionality.
sidebar:
  order: 2
---

Utility component for cloning React elements with additional props and functionality in Miss UI Web.

## Features

- **Element Cloning** - Clone React elements with new props
- **Prop Merging** - Intelligent merging of props and event handlers
- **Ref Forwarding** - Proper ref handling for cloned elements
- **Type Safety** - Full TypeScript support with proper typing
- **Event Handler Composition** - Combine multiple event handlers
- **Style Merging** - Merge inline styles and CSS classes
- **Children Preservation** - Maintain original element children
- **Performance Optimized** - Efficient cloning with minimal overhead

## Basic Usage

### Simple Element Cloning

```tsx
import { Base } from '@miss-ui/web';

function BasicClone() {
  const originalButton = <button>Original Button</button>;

  return (
    <Base.Clone 
      element={originalButton}
      onClick={() => console.log('Cloned button clicked!')}
      className="cloned-button"
    />
  );
}
```

### Cloning with Additional Props

```tsx
function PropsCloning() {
  const originalInput = (
    <input 
      type="text" 
      placeholder="Original placeholder"
      onChange={(e) => console.log('Original:', e.target.value)}
    />
  );

  return (
    <Base.Clone 
      element={originalInput}
      placeholder="Enhanced placeholder"
      onChange={(e) => {
        console.log('Enhanced:', e.target.value);
        // Original onChange will also be called
      }}
      style={{ border: '2px solid blue' }}
    />
  );
}
```

### Ref Forwarding

```tsx
function RefForwarding() {
  const buttonRef = React.useRef<HTMLButtonElement>(null);
  const originalButton = <button>Focus me</button>;

  const handleFocus = () => {
    buttonRef.current?.focus();
  };

  return (
    <div>
      <Base.Clone 
        element={originalButton}
        ref={buttonRef}
        className="focusable-button"
      />
      <button onClick={handleFocus}>Focus cloned button</button>
    </div>
  );
}
```

## Advanced Features

### Event Handler Composition

```tsx
function EventComposition() {
  const originalButton = (
    <button 
      onClick={() => console.log('Original click')}
      onMouseEnter={() => console.log('Original mouse enter')}
    >
      Click me
    </button>
  );

  return (
    <Base.Clone 
      element={originalButton}
      onClick={(e) => {
        console.log('Additional click handler');
        // Original onClick will also be called
      }}
      onMouseEnter={(e) => {
        console.log('Additional mouse enter handler');
        // Original onMouseEnter will also be called
      }}
      onMouseLeave={() => console.log('New mouse leave handler')}
    />
  );
}
```

### Style and Class Merging

```tsx
function StyleMerging() {
  const originalDiv = (
    <div 
      className="original-class"
      style={{ color: 'red', fontSize: '16px' }}
    >
      Styled content
    </div>
  );

  return (
    <Base.Clone 
      element={originalDiv}
      className="additional-class"
      style={{ 
        backgroundColor: 'yellow', 
        fontSize: '18px' // This will override the original fontSize
      }}
    />
  );
}
```

### Conditional Cloning

```tsx
function ConditionalCloning({ shouldEnhance }: { shouldEnhance: boolean }) {
  const originalElement = <span>Original content</span>;

  if (!shouldEnhance) {
    return originalElement;
  }

  return (
    <Base.Clone 
      element={originalElement}
      className="enhanced"
      onClick={() => console.log('Enhanced click')}
    />
  );
}
```

### Complex Element Cloning

```tsx
function ComplexCloning() {
  const ComplexComponent = ({ title, children, ...props }) => (
    <div {...props}>
      <h3>{title}</h3>
      {children}
    </div>
  );

  const originalComponent = (
    <ComplexComponent title="Original Title">
      <p>Original content</p>
    </ComplexComponent>
  );

  return (
    <Base.Clone 
      element={originalComponent}
      title="Enhanced Title"
      className="enhanced-complex"
      onClick={() => console.log('Complex component clicked')}
    />
  );
}
```

### Multiple Cloning

```tsx
function MultipleCloning() {
  const baseButton = <button>Base Button</button>;

  return (
    <div style={{ display: 'flex', gap: '8px' }}>
      <Base.Clone 
        element={baseButton}
        className="primary"
        onClick={() => console.log('Primary clicked')}
      />
      
      <Base.Clone 
        element={baseButton}
        className="secondary"
        onClick={() => console.log('Secondary clicked')}
      />
      
      <Base.Clone 
        element={baseButton}
        className="danger"
        onClick={() => console.log('Danger clicked')}
        disabled
      />
    </div>
  );
}
```

## Props

### Base.Clone Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `element` | `ReactElement` | - | The React element to clone |
| `...props` | `any` | - | Additional props to merge with the original element |

### Merging Behavior

- **Event Handlers**: Original and new handlers are both called
- **className**: Classes are concatenated with spaces
- **style**: Styles are merged with new styles taking precedence
- **Other Props**: New props override original props
- **children**: Original children are preserved unless explicitly overridden
- **ref**: New ref is forwarded, original ref is preserved if possible

## TypeScript Usage

### Type-Safe Cloning

```tsx
interface ButtonProps {
  variant: 'primary' | 'secondary';
  size: 'small' | 'medium' | 'large';
  onClick: () => void;
}

function TypeSafeCloning() {
  const TypedButton = ({ variant, size, onClick, children }: ButtonProps & { children: React.ReactNode }) => (
    <button 
      className={`btn btn-${variant} btn-${size}`}
      onClick={onClick}
    >
      {children}
    </button>
  );

  const originalButton = (
    <TypedButton 
      variant="primary" 
      size="medium"
      onClick={() => console.log('Original')}
    >
      Original
    </TypedButton>
  );

  return (
    <Base.Clone 
      element={originalButton}
      variant="secondary" // Type-safe prop override
      onClick={() => console.log('Cloned')} // Additional handler
    />
  );
}
```

### Generic Cloning

```tsx
function GenericCloning<T extends React.ElementType>() {
  const createClonedElement = <P extends object>(
    element: React.ReactElement<P>,
    additionalProps: Partial<P>
  ) => {
    return (
      <Base.Clone 
        element={element}
        {...additionalProps}
      />
    );
  };

  const button = <button onClick={() => {}}>Button</button>;
  const input = <input type="text" onChange={() => {}} />;

  return (
    <div>
      {createClonedElement(button, { className: 'enhanced-button' })}
      {createClonedElement(input, { placeholder: 'Enhanced input' })}
    </div>
  );
}
```

## Use Cases

### Component Enhancement

```tsx
function ComponentEnhancement({ children }: { children: React.ReactElement }) {
  return (
    <Base.Clone 
      element={children}
      className="enhanced-component"
      onFocus={() => console.log('Component focused')}
      onBlur={() => console.log('Component blurred')}
    />
  );
}

// Usage
function App() {
  return (
    <ComponentEnhancement>
      <input type="text" placeholder="This will be enhanced" />
    </ComponentEnhancement>
  );
}
```

### Wrapper Components

```tsx
function ClickTracker({ children, trackingId }: { 
  children: React.ReactElement;
  trackingId: string;
}) {
  const handleClick = (originalEvent: React.MouseEvent) => {
    console.log(`Tracked click: ${trackingId}`);
    // Original onClick will still be called
  };

  return (
    <Base.Clone 
      element={children}
      onClick={handleClick}
      data-tracking-id={trackingId}
    />
  );
}
```

### Theme Application

```tsx
function ThemedComponent({ children, theme }: { 
  children: React.ReactElement;
  theme: 'light' | 'dark';
}) {
  return (
    <Base.Clone 
      element={children}
      className={`theme-${theme}`}
      style={{
        '--theme-bg': theme === 'light' ? '#fff' : '#000',
        '--theme-color': theme === 'light' ? '#000' : '#fff',
      }}
    />
  );
}
```

## Best Practices

### Do's

- ✅ Use Clone for enhancing existing elements without modifying their source
- ✅ Preserve original functionality while adding new features
- ✅ Use TypeScript for type-safe prop merging
- ✅ Handle refs properly when cloning
- ✅ Test event handler composition thoroughly
- ✅ Document the cloning behavior in your components
- ✅ Use Clone for creating reusable enhancement patterns

### Don'ts

- ❌ Don't clone elements unnecessarily (performance impact)
- ❌ Don't rely on Clone for complex component composition
- ❌ Don't break the original element's contract
- ❌ Don't clone elements with complex internal state
- ❌ Don't use Clone when a simple wrapper would suffice
- ❌ Don't ignore TypeScript warnings about prop types
- ❌ Don't clone elements that manage their own refs internally

## Performance

### Optimization Tips

```tsx
// ✅ Good: Memoize cloned elements when props don't change
const MemoizedClone = React.memo(function MemoizedClone({ element, ...props }) {
  return <Base.Clone element={element} {...props} />;
});

// ✅ Good: Use callback refs for complex ref handling
function CallbackRefCloning() {
  const [element, setElement] = React.useState<HTMLElement | null>(null);

  const refCallback = React.useCallback((node: HTMLElement | null) => {
    setElement(node);
    // Additional ref logic
  }, []);

  return (
    <Base.Clone 
      element={<button>Button</button>}
      ref={refCallback}
    />
  );
}

// ✅ Good: Avoid cloning in render loops
function EfficientList({ items }: { items: Array<{ id: string; element: React.ReactElement }> }) {
  return (
    <div>
      {items.map(({ id, element }) => (
        <Base.Clone 
          key={id}
          element={element}
          className="list-item"
        />
      ))}
    </div>
  );
}
```

### Bundle Size

- Minimal runtime overhead
- Tree-shakeable implementation
- No external dependencies
- Efficient prop merging algorithms