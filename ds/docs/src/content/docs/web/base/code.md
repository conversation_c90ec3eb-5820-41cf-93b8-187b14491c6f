---
title: Base.Code
description: Inline code component for displaying code snippets with syntax highlighting and formatting.
sidebar:
  order: 3
---

Inline code component for displaying code snippets with syntax highlighting and formatting in Miss UI Web.

## Features

- **Syntax Highlighting** - Support for multiple programming languages
- **Theme Integration** - Consistent styling with design system
- **Copy Functionality** - Built-in copy-to-clipboard feature
- **Language Detection** - Automatic language detection for highlighting
- **Responsive Design** - Adapts to different screen sizes
- **Accessibility** - Screen reader friendly with proper semantics
- **Customizable** - Flexible styling and theme options
- **Performance Optimized** - Efficient rendering and highlighting

## Basic Usage

### Simple Inline Code

```tsx
import { Base } from '@miss-ui/web';

function BasicCode() {
  return (
    <p>
      Use the <Base.Code>console.log()</Base.Code> function to debug your code.
    </p>
  );
}
```

### Code with Language

```tsx
function LanguageCode() {
  return (
    <div>
      <p>
        JavaScript: <Base.Code language="javascript">const x = 42;</Base.Code>
      </p>
      <p>
        Python: <Base.Code language="python">x = 42</Base.Code>
      </p>
      <p>
        CSS: <Base.Code language="css">color: #ff0000;</Base.Code>
      </p>
    </div>
  );
}
```

### Multi-line Code

```tsx
function MultilineCode() {
  const codeSnippet = `function greet(name) {
  return \`Hello, \${name}!\`;
}`;

  return (
    <Base.Code 
      language="javascript"
      multiline
    >
      {codeSnippet}
    </Base.Code>
  );
}
```

## Variants

### Different Themes

```tsx
function ThemedCode() {
  const code = "const theme = 'dark';";

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <Base.Code theme="light" language="javascript">
        {code}
      </Base.Code>
      
      <Base.Code theme="dark" language="javascript">
        {code}
      </Base.Code>
      
      <Base.Code theme="github" language="javascript">
        {code}
      </Base.Code>
      
      <Base.Code theme="monokai" language="javascript">
        {code}
      </Base.Code>
    </div>
  );
}
```

### With Copy Button

```tsx
function CopyableCode() {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <Base.Code 
        language="bash"
        copyable
      >
        npm install @miss-ui/web
      </Base.Code>
      
      <Base.Code 
        language="javascript"
        copyable
        copyText="Custom copy text"
      >
        import { Base } from '@miss-ui/web';
      </Base.Code>
    </div>
  );
}
```

### Different Sizes

```tsx
function SizedCode() {
  const code = "console.log('Hello World');";

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <Base.Code size="small" language="javascript">
        {code}
      </Base.Code>
      
      <Base.Code size="medium" language="javascript">
        {code}
      </Base.Code>
      
      <Base.Code size="large" language="javascript">
        {code}
      </Base.Code>
    </div>
  );
}
```

## Advanced Features

### Line Numbers

```tsx
function LineNumbersCode() {
  const codeSnippet = `function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

console.log(fibonacci(10));`;

  return (
    <Base.Code 
      language="javascript"
      multiline
      showLineNumbers
      startLineNumber={1}
    >
      {codeSnippet}
    </Base.Code>
  );
}
```

### Highlighted Lines

```tsx
function HighlightedCode() {
  const codeSnippet = `function processData(data) {
  // Validate input
  if (!data) return null;
  
  // Process the data
  const result = data.map(item => {
    return item.value * 2;
  });
  
  return result;
}`;

  return (
    <Base.Code 
      language="javascript"
      multiline
      showLineNumbers
      highlightLines={[3, 6, 7]}
    >
      {codeSnippet}
    </Base.Code>
  );
}
```

### Custom Styling

```tsx
function CustomStyledCode() {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <Base.Code 
        language="css"
        style={{
          backgroundColor: '#f8f9fa',
          border: '1px solid #e9ecef',
          borderRadius: '8px',
          padding: '12px',
        }}
      >
        .custom-style {{ color: #007bff; }}
      </Base.Code>
      
      <Base.Code 
        language="javascript"
        className="custom-code-block"
        variant="outlined"
      >
        const customCode = true;
      </Base.Code>
    </div>
  );
}
```

### Interactive Code

```tsx
function InteractiveCode() {
  const [code, setCode] = React.useState('console.log("Hello World");');
  const [copied, setCopied] = React.useState(false);

  const handleCopy = () => {
    navigator.clipboard.writeText(code);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div>
      <Base.Code 
        language="javascript"
        copyable
        onCopy={handleCopy}
        copyButtonText={copied ? 'Copied!' : 'Copy'}
      >
        {code}
      </Base.Code>
      
      <div style={{ marginTop: '16px' }}>
        <input 
          type="text" 
          value={code}
          onChange={(e) => setCode(e.target.value)}
          style={{ width: '100%', padding: '8px' }}
        />
      </div>
    </div>
  );
}
```

### Code with Filename

```tsx
function FilenameCode() {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <Base.Code 
        language="typescript"
        filename="utils.ts"
        multiline
        showLineNumbers
      >
        {`export function formatDate(date: Date): string {
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}`}
      </Base.Code>
      
      <Base.Code 
        language="json"
        filename="package.json"
        multiline
      >
        {`{
  "name": "my-project",
  "version": "1.0.0",
  "dependencies": {
    "@miss-ui/web": "^2.0.0"
  }
}`}
      </Base.Code>
    </div>
  );
}
```

## Props

### Base.Code Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `children` | `string` | - | Code content to display |
| `language` | `string` | - | Programming language for syntax highlighting |
| `theme` | `CodeTheme` | `'default'` | Color theme for syntax highlighting |
| `size` | `'small' \| 'medium' \| 'large'` | `'medium'` | Size of the code text |
| `variant` | `'default' \| 'outlined' \| 'filled'` | `'default'` | Visual variant |
| `multiline` | `boolean` | `false` | Whether to display as block code |
| `copyable` | `boolean` | `false` | Whether to show copy button |
| `copyText` | `string` | - | Custom text to copy (defaults to children) |
| `copyButtonText` | `string` | `'Copy'` | Text for copy button |
| `onCopy` | `() => void` | - | Callback when code is copied |
| `showLineNumbers` | `boolean` | `false` | Whether to show line numbers |
| `startLineNumber` | `number` | `1` | Starting line number |
| `highlightLines` | `number[]` | - | Array of line numbers to highlight |
| `filename` | `string` | - | Filename to display above code |
| `maxHeight` | `string \| number` | - | Maximum height with scroll |
| `wrap` | `boolean` | `false` | Whether to wrap long lines |
| `className` | `string` | - | Additional CSS class names |
| `style` | `CSSProperties` | - | Inline styles |

### Type Definitions

```typescript
type CodeTheme = 
  | 'default'
  | 'light'
  | 'dark'
  | 'github'
  | 'monokai'
  | 'solarized-light'
  | 'solarized-dark'
  | 'vs-code'
  | 'atom';

type SupportedLanguage = 
  | 'javascript'
  | 'typescript'
  | 'jsx'
  | 'tsx'
  | 'css'
  | 'scss'
  | 'html'
  | 'json'
  | 'markdown'
  | 'bash'
  | 'shell'
  | 'python'
  | 'java'
  | 'c'
  | 'cpp'
  | 'csharp'
  | 'php'
  | 'ruby'
  | 'go'
  | 'rust'
  | 'sql'
  | 'xml'
  | 'yaml';
```

## Language Support

### Supported Languages

```tsx
function LanguageShowcase() {
  const examples = {
    javascript: 'const greeting = "Hello World";',
    typescript: 'const greeting: string = "Hello World";',
    python: 'greeting = "Hello World"',
    css: '.greeting { color: blue; }',
    html: '<div class="greeting">Hello World</div>',
    json: '{"greeting": "Hello World"}',
    bash: 'echo "Hello World"',
    sql: 'SELECT "Hello World" as greeting;',
  };

  return (
    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '16px' }}>
      {Object.entries(examples).map(([lang, code]) => (
        <div key={lang}>
          <h4>{lang.charAt(0).toUpperCase() + lang.slice(1)}</h4>
          <Base.Code language={lang} copyable>
            {code}
          </Base.Code>
        </div>
      ))}
    </div>
  );
}
```

## Accessibility

### Screen Reader Support

```tsx
function AccessibleCode() {
  return (
    <div>
      <Base.Code 
        language="javascript"
        aria-label="JavaScript code example"
        role="code"
      >
        const accessible = true;
      </Base.Code>
      
      <Base.Code 
        language="bash"
        multiline
        aria-describedby="code-description"
      >
        npm install package
      </Base.Code>
      <p id="code-description">
        This command installs a package using npm.
      </p>
    </div>
  );
}
```

### Keyboard Navigation

- **Tab** - Navigate to copy button when copyable
- **Enter/Space** - Activate copy button
- **Escape** - Close any open tooltips or modals

### Focus Management

```tsx
function FocusManagement() {
  const codeRef = React.useRef<HTMLElement>(null);

  const focusCode = () => {
    codeRef.current?.focus();
  };

  return (
    <div>
      <button onClick={focusCode}>Focus code block</button>
      <Base.Code 
        ref={codeRef}
        language="javascript"
        tabIndex={0}
        copyable
      >
        const focusable = true;
      </Base.Code>
    </div>
  );
}
```

## Best Practices

### Do's

- ✅ Use appropriate language specification for syntax highlighting
- ✅ Keep inline code snippets short and readable
- ✅ Use multiline for code blocks longer than one line
- ✅ Provide copy functionality for code users might need to use
- ✅ Use line numbers for longer code examples
- ✅ Choose themes that match your application's design
- ✅ Include filename context when helpful

### Don'ts

- ❌ Don't use Code for non-code content
- ❌ Don't make inline code too long (use multiline instead)
- ❌ Don't use overly bright or distracting themes
- ❌ Don't forget to specify language for better highlighting
- ❌ Don't use Code for large files (consider external links)
- ❌ Don't ignore accessibility attributes
- ❌ Don't use Code for formatted text that isn't code

## Performance

### Optimization Tips

```tsx
// ✅ Good: Memoize large code blocks
const LargeCodeBlock = React.memo(function LargeCodeBlock({ code }) {
  return (
    <Base.Code 
      language="javascript"
      multiline
      showLineNumbers
    >
      {code}
    </Base.Code>
  );
});

// ✅ Good: Use lazy loading for syntax highlighting
function LazyHighlightedCode({ code, language }) {
  const [shouldHighlight, setShouldHighlight] = React.useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setShouldHighlight(true), 100);
    return () => clearTimeout(timer);
  }, []);

  return (
    <Base.Code 
      language={shouldHighlight ? language : undefined}
      multiline
    >
      {code}
    </Base.Code>
  );
}

// ✅ Good: Virtualize very long code blocks
function VirtualizedCode({ lines }) {
  const [visibleRange, setVisibleRange] = React.useState({ start: 0, end: 50 });
  const visibleLines = lines.slice(visibleRange.start, visibleRange.end);

  return (
    <Base.Code 
      language="javascript"
      multiline
      showLineNumbers
      startLineNumber={visibleRange.start + 1}
      maxHeight={400}
    >
      {visibleLines.join('\n')}
    </Base.Code>
  );
}
```

### Bundle Size

- Syntax highlighting is loaded on demand
- Tree-shakeable language support
- Optimized theme loading
- Minimal core bundle impact

### Runtime Performance

- Efficient syntax highlighting engine
- Memoized highlighting results
- Optimized re-rendering
- Lazy loading of large code blocks