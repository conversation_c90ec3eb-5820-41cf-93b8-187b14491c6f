---
title: Base.ScrollArea
description: Custom scrollable area component with enhanced styling and cross-platform consistency.
sidebar:
  order: 8
---

Custom scrollable area component with enhanced styling and cross-platform consistency in Miss UI Web.

## Features

- **Custom Scrollbars** - Styled scrollbars that work across all browsers
- **Cross-platform Consistency** - Uniform appearance on all operating systems
- **Smooth Scrolling** - Enhanced scrolling experience with momentum
- **Responsive Design** - Adapts to different screen sizes and orientations
- **Accessibility** - Full keyboard navigation and screen reader support
- **Performance Optimized** - Efficient rendering for large content
- **Touch Support** - Native touch scrolling on mobile devices
- **Customizable Appearance** - Flexible styling options for scrollbars

## Basic Usage

### Simple Scroll Area

```tsx
import { Base } from '@miss-ui/web';

function BasicScrollArea() {
  const content = Array.from({ length: 50 }, (_, i) => (
    <div key={i} style={{ padding: '8px', borderBottom: '1px solid #eee' }}>
      Item {i + 1}: This is some content that will be scrollable when it overflows.
    </div>
  ));

  return (
    <Base.ScrollArea style={{ height: '300px', border: '1px solid #ccc' }}>
      {content}
    </Base.ScrollArea>
  );
}
```

### Horizontal Scrolling

```tsx
function HorizontalScrollArea() {
  const items = Array.from({ length: 20 }, (_, i) => (
    <div 
      key={i}
      style={{ 
        minWidth: '150px',
        height: '100px',
        background: `hsl(${i * 20}, 70%, 80%)`,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        margin: '0 8px'
      }}
    >
      Card {i + 1}
    </div>
  ));

  return (
    <Base.ScrollArea 
      orientation="horizontal"
      style={{ width: '100%', border: '1px solid #ccc' }}
    >
      <div style={{ display: 'flex', padding: '16px' }}>
        {items}
      </div>
    </Base.ScrollArea>
  );
}
```

### Both Directions

```tsx
function BidirectionalScrollArea() {
  return (
    <Base.ScrollArea 
      orientation="both"
      style={{ 
        width: '400px', 
        height: '300px', 
        border: '1px solid #ccc' 
      }}
    >
      <div style={{ 
        width: '800px', 
        height: '600px',
        background: 'linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4)',
        padding: '20px',
        color: 'white'
      }}>
        <h3>Large Content Area</h3>
        <p>This content is larger than the scroll area in both dimensions.</p>
        <p>You can scroll both horizontally and vertically to see all content.</p>
        
        <div style={{ marginTop: '200px' }}>
          <p>More content down here...</p>
        </div>
        
        <div style={{ marginLeft: '400px', marginTop: '100px' }}>
          <p>And some content to the right...</p>
        </div>
      </div>
    </Base.ScrollArea>
  );
}
```

## Styling Variants

### Custom Scrollbar Appearance

```tsx
function StyledScrollbars() {
  const content = Array.from({ length: 30 }, (_, i) => (
    <div key={i} style={{ padding: '12px', borderBottom: '1px solid #f0f0f0' }}>
      List item {i + 1} with some content that demonstrates the custom scrollbar styling.
    </div>
  ));

  return (
    <div style={{ display: 'flex', gap: '24px' }}>
      <div>
        <h4>Default Scrollbar</h4>
        <Base.ScrollArea style={{ height: '250px', width: '300px', border: '1px solid #ccc' }}>
          {content}
        </Base.ScrollArea>
      </div>
      
      <div>
        <h4>Thin Scrollbar</h4>
        <Base.ScrollArea 
          scrollbarSize="thin"
          style={{ height: '250px', width: '300px', border: '1px solid #ccc' }}
        >
          {content}
        </Base.ScrollArea>
      </div>
      
      <div>
        <h4>Thick Scrollbar</h4>
        <Base.ScrollArea 
          scrollbarSize="thick"
          style={{ height: '250px', width: '300px', border: '1px solid #ccc' }}
        >
          {content}
        </Base.ScrollArea>
      </div>
    </div>
  );
}
```

### Color Themes

```tsx
function ThemedScrollbars() {
  const content = Array.from({ length: 25 }, (_, i) => (
    <div key={i} style={{ padding: '10px', borderBottom: '1px solid rgba(255,255,255,0.1)' }}>
      Item {i + 1}
    </div>
  ));

  return (
    <div style={{ display: 'flex', gap: '24px' }}>
      <div>
        <h4>Light Theme</h4>
        <Base.ScrollArea 
          theme="light"
          style={{ 
            height: '250px', 
            width: '250px', 
            border: '1px solid #ccc',
            background: '#fff'
          }}
        >
          {content.map((item, i) => 
            React.cloneElement(item, {
              style: { 
                ...item.props.style, 
                borderBottom: '1px solid #eee',
                color: '#333'
              }
            })
          )}
        </Base.ScrollArea>
      </div>
      
      <div>
        <h4>Dark Theme</h4>
        <Base.ScrollArea 
          theme="dark"
          style={{ 
            height: '250px', 
            width: '250px', 
            border: '1px solid #444',
            background: '#1a1a1a',
            color: '#fff'
          }}
        >
          {content}
        </Base.ScrollArea>
      </div>
    </div>
  );
}
```

## Advanced Features

### Scroll Position Control

```tsx
function ControlledScrollArea() {
  const scrollAreaRef = React.useRef<any>(null);
  const [scrollPosition, setScrollPosition] = React.useState({ x: 0, y: 0 });

  const content = Array.from({ length: 50 }, (_, i) => (
    <div key={i} style={{ padding: '12px', borderBottom: '1px solid #eee' }}>
      Item {i + 1}: Scroll position Y: {Math.round(scrollPosition.y)}
    </div>
  ));

  const scrollToTop = () => {
    scrollAreaRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const scrollToBottom = () => {
    scrollAreaRef.current?.scrollTo({ top: 9999, behavior: 'smooth' });
  };

  const scrollToMiddle = () => {
    const element = scrollAreaRef.current?.getScrollElement();
    if (element) {
      const middle = (element.scrollHeight - element.clientHeight) / 2;
      scrollAreaRef.current?.scrollTo({ top: middle, behavior: 'smooth' });
    }
  };

  return (
    <div>
      <div style={{ marginBottom: '16px', display: 'flex', gap: '8px' }}>
        <button onClick={scrollToTop}>Scroll to Top</button>
        <button onClick={scrollToMiddle}>Scroll to Middle</button>
        <button onClick={scrollToBottom}>Scroll to Bottom</button>
      </div>
      
      <Base.ScrollArea 
        ref={scrollAreaRef}
        style={{ height: '300px', border: '1px solid #ccc' }}
        onScroll={(position) => setScrollPosition(position)}
      >
        {content}
      </Base.ScrollArea>
      
      <div style={{ marginTop: '8px', fontSize: '14px', color: '#666' }}>
        Scroll Position: X: {Math.round(scrollPosition.x)}, Y: {Math.round(scrollPosition.y)}
      </div>
    </div>
  );
}
```

### Scroll Indicators

```tsx
function ScrollIndicators() {
  const [scrollState, setScrollState] = React.useState({
    canScrollUp: false,
    canScrollDown: true,
    canScrollLeft: false,
    canScrollRight: false
  });

  const content = Array.from({ length: 40 }, (_, i) => (
    <div key={i} style={{ padding: '12px', borderBottom: '1px solid #eee' }}>
      Scrollable content item {i + 1}
    </div>
  ));

  const handleScroll = (position: { x: number; y: number }, element: HTMLElement) => {
    const { scrollTop, scrollLeft, scrollHeight, scrollWidth, clientHeight, clientWidth } = element;
    
    setScrollState({
      canScrollUp: scrollTop > 0,
      canScrollDown: scrollTop < scrollHeight - clientHeight - 1,
      canScrollLeft: scrollLeft > 0,
      canScrollRight: scrollLeft < scrollWidth - clientWidth - 1
    });
  };

  return (
    <div style={{ position: 'relative' }}>
      {/* Scroll Indicators */}
      {scrollState.canScrollUp && (
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '20px',
          background: 'linear-gradient(to bottom, rgba(0,0,0,0.1), transparent)',
          zIndex: 1,
          pointerEvents: 'none'
        }} />
      )}
      
      {scrollState.canScrollDown && (
        <div style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          height: '20px',
          background: 'linear-gradient(to top, rgba(0,0,0,0.1), transparent)',
          zIndex: 1,
          pointerEvents: 'none'
        }} />
      )}
      
      <Base.ScrollArea 
        style={{ height: '250px', border: '1px solid #ccc' }}
        onScroll={handleScroll}
      >
        {content}
      </Base.ScrollArea>
    </div>
  );
}
```

### Virtual Scrolling

```tsx
function VirtualScrollArea() {
  const [visibleRange, setVisibleRange] = React.useState({ start: 0, end: 20 });
  const itemHeight = 50;
  const totalItems = 10000;
  const containerHeight = 400;
  
  const visibleItems = React.useMemo(() => {
    return Array.from({ length: visibleRange.end - visibleRange.start }, (_, i) => {
      const index = visibleRange.start + i;
      return {
        index,
        content: `Virtual item ${index + 1} - This is efficiently rendered content`
      };
    });
  }, [visibleRange]);

  const handleScroll = (position: { y: number }, element: HTMLElement) => {
    const scrollTop = position.y;
    const start = Math.floor(scrollTop / itemHeight);
    const visibleCount = Math.ceil(containerHeight / itemHeight) + 2; // Buffer
    const end = Math.min(start + visibleCount, totalItems);
    
    setVisibleRange({ start, end });
  };

  return (
    <div>
      <p>Virtual scrolling with {totalItems.toLocaleString()} items</p>
      <Base.ScrollArea 
        style={{ height: `${containerHeight}px`, border: '1px solid #ccc' }}
        onScroll={handleScroll}
      >
        <div style={{ height: `${totalItems * itemHeight}px`, position: 'relative' }}>
          <div style={{
            position: 'absolute',
            top: `${visibleRange.start * itemHeight}px`,
            width: '100%'
          }}>
            {visibleItems.map(item => (
              <div 
                key={item.index}
                style={{
                  height: `${itemHeight}px`,
                  padding: '12px',
                  borderBottom: '1px solid #eee',
                  display: 'flex',
                  alignItems: 'center'
                }}
              >
                {item.content}
              </div>
            ))}
          </div>
        </div>
      </Base.ScrollArea>
      <p style={{ fontSize: '14px', color: '#666' }}>
        Showing items {visibleRange.start + 1} - {visibleRange.end} of {totalItems.toLocaleString()}
      </p>
    </div>
  );
}
```

### Nested Scroll Areas

```tsx
function NestedScrollAreas() {
  const outerContent = Array.from({ length: 20 }, (_, i) => {
    if (i === 10) {
      // Nested scroll area in the middle
      const innerContent = Array.from({ length: 15 }, (_, j) => (
        <div key={j} style={{ padding: '8px', borderBottom: '1px solid #ddd' }}>
          Nested item {j + 1}
        </div>
      ));
      
      return (
        <div key={i} style={{ padding: '16px', background: '#f8f9fa', margin: '8px 0' }}>
          <h4>Nested Scroll Area</h4>
          <Base.ScrollArea 
            style={{ 
              height: '150px', 
              border: '1px solid #ccc',
              background: 'white'
            }}
          >
            {innerContent}
          </Base.ScrollArea>
        </div>
      );
    }
    
    return (
      <div key={i} style={{ padding: '12px', borderBottom: '1px solid #eee' }}>
        Outer content item {i + 1}
      </div>
    );
  });

  return (
    <Base.ScrollArea style={{ height: '400px', border: '1px solid #ccc' }}>
      {outerContent}
    </Base.ScrollArea>
  );
}
```

## Props

### Base.ScrollArea Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `children` | `ReactNode` | - | Content to be scrolled |
| `orientation` | `'vertical' \| 'horizontal' \| 'both'` | `'vertical'` | Scroll direction |
| `scrollbarSize` | `'thin' \| 'normal' \| 'thick'` | `'normal'` | Size of scrollbars |
| `theme` | `'light' \| 'dark' \| 'auto'` | `'auto'` | Scrollbar color theme |
| `hideScrollbars` | `boolean` | `false` | Hide scrollbars (content still scrollable) |
| `scrollbarVisible` | `'auto' \| 'always' \| 'hover'` | `'auto'` | When to show scrollbars |
| `smoothScrolling` | `boolean` | `true` | Enable smooth scrolling |
| `momentum` | `boolean` | `true` | Enable momentum scrolling on touch devices |
| `onScroll` | `(position: ScrollPosition, element: HTMLElement) => void` | - | Scroll event handler |
| `onScrollStart` | `() => void` | - | Scroll start event handler |
| `onScrollEnd` | `() => void` | - | Scroll end event handler |
| `className` | `string` | - | Additional CSS class names |
| `style` | `CSSProperties` | - | Inline styles |
| `aria-label` | `string` | - | Accessible label |
| `role` | `string` | - | ARIA role |

### Ref Methods

```typescript
interface ScrollAreaRef {
  scrollTo: (options: ScrollToOptions) => void;
  scrollIntoView: (element: HTMLElement, options?: ScrollIntoViewOptions) => void;
  getScrollElement: () => HTMLElement | null;
  getScrollPosition: () => ScrollPosition;
}

interface ScrollPosition {
  x: number;
  y: number;
}

interface ScrollToOptions {
  top?: number;
  left?: number;
  behavior?: 'auto' | 'smooth';
}
```

## Accessibility

### Keyboard Navigation

```tsx
function AccessibleScrollArea() {
  const content = Array.from({ length: 30 }, (_, i) => (
    <div 
      key={i} 
      tabIndex={0}
      style={{ 
        padding: '12px', 
        borderBottom: '1px solid #eee',
        outline: 'none'
      }}
      onFocus={(e) => {
        e.currentTarget.style.background = '#e3f2fd';
      }}
      onBlur={(e) => {
        e.currentTarget.style.background = 'transparent';
      }}
    >
      Focusable item {i + 1}
    </div>
  ));

  return (
    <Base.ScrollArea 
      style={{ height: '300px', border: '1px solid #ccc' }}
      aria-label="List of focusable items"
      role="listbox"
    >
      {content}
    </Base.ScrollArea>
  );
}
```

### Screen Reader Support

- **Arrow Keys** - Scroll content when focused
- **Page Up/Page Down** - Scroll by page
- **Home/End** - Scroll to beginning/end
- **Tab** - Navigate through focusable content
- **Space** - Scroll down by page
- **Shift + Space** - Scroll up by page

### Focus Management

```tsx
function FocusManagement() {
  const scrollAreaRef = React.useRef<any>(null);
  const [focusedIndex, setFocusedIndex] = React.useState(0);

  const items = Array.from({ length: 50 }, (_, i) => (
    <button
      key={i}
      style={{
        width: '100%',
        padding: '12px',
        border: 'none',
        borderBottom: '1px solid #eee',
        background: focusedIndex === i ? '#e3f2fd' : 'transparent',
        textAlign: 'left',
        cursor: 'pointer'
      }}
      onFocus={() => {
        setFocusedIndex(i);
        // Scroll focused item into view
        scrollAreaRef.current?.scrollIntoView(
          document.activeElement,
          { behavior: 'smooth', block: 'nearest' }
        );
      }}
    >
      Focusable button {i + 1}
    </button>
  ));

  return (
    <Base.ScrollArea 
      ref={scrollAreaRef}
      style={{ height: '300px', border: '1px solid #ccc' }}
      aria-label="List of buttons"
    >
      {items}
    </Base.ScrollArea>
  );
}
```

## Best Practices

### Do's

- ✅ Use ScrollArea for content that might overflow
- ✅ Provide appropriate ARIA labels for screen readers
- ✅ Test keyboard navigation thoroughly
- ✅ Consider virtual scrolling for large datasets
- ✅ Use scroll indicators for better UX
- ✅ Ensure touch scrolling works on mobile devices
- ✅ Test across different browsers and operating systems

### Don'ts

- ❌ Don't nest too many scroll areas
- ❌ Don't hide scrollbars without providing alternative navigation
- ❌ Don't forget to handle focus management
- ❌ Don't use ScrollArea for small content that doesn't need scrolling
- ❌ Don't ignore performance with very large content
- ❌ Don't forget to test with assistive technologies
- ❌ Don't use custom scrollbars that are too thin for accessibility

## Performance

### Optimization Tips

```tsx
// ✅ Good: Memoize scroll area content
const ScrollableContent = React.memo(function ScrollableContent({ items }) {
  return (
    <Base.ScrollArea style={{ height: '400px' }}>
      {items.map(item => (
        <div key={item.id}>{item.content}</div>
      ))}
    </Base.ScrollArea>
  );
});

// ✅ Good: Debounce scroll events
function DebouncedScrollArea({ onScroll, children }) {
  const debouncedScroll = React.useMemo(
    () => debounce(onScroll, 100),
    [onScroll]
  );

  return (
    <Base.ScrollArea onScroll={debouncedScroll}>
      {children}
    </Base.ScrollArea>
  );
}

// ✅ Good: Use virtual scrolling for large lists
function LargeList({ items }) {
  const [visibleItems, setVisibleItems] = React.useState(items.slice(0, 50));
  
  const handleScroll = React.useCallback((position, element) => {
    // Update visible items based on scroll position
    const startIndex = Math.floor(position.y / ITEM_HEIGHT);
    const endIndex = startIndex + VISIBLE_COUNT;
    setVisibleItems(items.slice(startIndex, endIndex));
  }, [items]);

  return (
    <Base.ScrollArea onScroll={handleScroll}>
      {visibleItems.map(item => (
        <div key={item.id}>{item.content}</div>
      ))}
    </Base.ScrollArea>
  );
}
```

### Bundle Size

- Minimal CSS footprint with efficient scrollbar styling
- No external dependencies
- Tree-shakeable implementation
- Optimized for modern browsers

### Runtime Performance

- Hardware-accelerated scrolling
- Efficient event handling with passive listeners
- Optimized re-rendering with proper memoization
- Smooth animations using CSS transforms