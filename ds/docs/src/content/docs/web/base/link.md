---
title: Base.Link
description: Accessible link component with enhanced styling and navigation features.
sidebar:
  order: 6
---

Accessible link component with enhanced styling and navigation features in Miss UI Web.

## Features

- **Accessibility First** - Full ARIA support and keyboard navigation
- **Router Integration** - Works with React Router and Next.js
- **Visual Variants** - Multiple styling options and states
- **External Link Handling** - Automatic external link detection and security
- **Focus Management** - Enhanced focus indicators and management
- **Theme Integration** - Consistent styling with design system
- **Icon Support** - Built-in icon positioning and styling
- **Performance Optimized** - Efficient rendering and prefetching

## Basic Usage

### Simple Link

```tsx
import { Base } from '@miss-ui/web';

function BasicLink() {
  return (
    <div>
      <p>
        Visit our <Base.Link href="/about">About page</Base.Link> to learn more.
      </p>
      
      <p>
        Check out our <Base.Link href="https://github.com/miss-ui/web" external>
          GitHub repository
        </Base.Link> for the source code.
      </p>
    </div>
  );
}
```

### Internal Navigation

```tsx
function InternalLinks() {
  return (
    <nav>
      <Base.Link href="/" variant="nav">
        Home
      </Base.Link>
      <Base.Link href="/products" variant="nav">
        Products
      </Base.Link>
      <Base.Link href="/about" variant="nav">
        About
      </Base.Link>
      <Base.Link href="/contact" variant="nav">
        Contact
      </Base.Link>
    </nav>
  );
}
```

### External Links

```tsx
function ExternalLinks() {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
      <Base.Link 
        href="https://example.com" 
        external
        showExternalIcon
      >
        External website with icon
      </Base.Link>
      
      <Base.Link 
        href="https://docs.example.com" 
        external
        openInNewTab
      >
        Open in new tab
      </Base.Link>
      
      <Base.Link 
        href="mailto:<EMAIL>"
        variant="email"
      >
        Send email
      </Base.Link>
      
      <Base.Link 
        href="tel:+1234567890"
        variant="phone"
      >
        Call us
      </Base.Link>
    </div>
  );
}
```

## Variants

### Visual Variants

```tsx
function LinkVariants() {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <div>
        <h4>Default</h4>
        <Base.Link href="#">Default link styling</Base.Link>
      </div>
      
      <div>
        <h4>Primary</h4>
        <Base.Link href="#" variant="primary">
          Primary link with emphasis
        </Base.Link>
      </div>
      
      <div>
        <h4>Secondary</h4>
        <Base.Link href="#" variant="secondary">
          Secondary link styling
        </Base.Link>
      </div>
      
      <div>
        <h4>Subtle</h4>
        <Base.Link href="#" variant="subtle">
          Subtle link with minimal styling
        </Base.Link>
      </div>
      
      <div>
        <h4>Button-like</h4>
        <Base.Link href="#" variant="button">
          Link styled as button
        </Base.Link>
      </div>
      
      <div>
        <h4>Navigation</h4>
        <Base.Link href="#" variant="nav">
          Navigation link
        </Base.Link>
      </div>
    </div>
  );
}
```

### Size Variants

```tsx
function SizeVariants() {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
      <Base.Link href="#" size="small">
        Small link text
      </Base.Link>
      
      <Base.Link href="#" size="medium">
        Medium link text (default)
      </Base.Link>
      
      <Base.Link href="#" size="large">
        Large link text
      </Base.Link>
    </div>
  );
}
```

### State Variants

```tsx
function StateVariants() {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
      <Base.Link href="#" state="default">
        Default state
      </Base.Link>
      
      <Base.Link href="#" state="active">
        Active state
      </Base.Link>
      
      <Base.Link href="#" state="visited">
        Visited state
      </Base.Link>
      
      <Base.Link href="#" disabled>
        Disabled link
      </Base.Link>
    </div>
  );
}
```

## Advanced Features

### With Icons

```tsx
function IconLinks() {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
      <Base.Link 
        href="/download" 
        leftIcon={<DownloadIcon />}
      >
        Download file
      </Base.Link>
      
      <Base.Link 
        href="https://github.com" 
        external
        rightIcon={<ExternalLinkIcon />}
      >
        View on GitHub
      </Base.Link>
      
      <Base.Link 
        href="/settings" 
        leftIcon={<SettingsIcon />}
        rightIcon={<ChevronRightIcon />}
      >
        Go to settings
      </Base.Link>
    </div>
  );
}
```

### Router Integration

```tsx
// With React Router
import { Link as RouterLink } from 'react-router-dom';

function ReactRouterIntegration() {
  return (
    <div>
      <Base.Link 
        as={RouterLink}
        to="/dashboard"
        variant="nav"
      >
        Dashboard
      </Base.Link>
      
      <Base.Link 
        as={RouterLink}
        to="/profile"
        state={{ from: 'navigation' }}
      >
        Profile
      </Base.Link>
    </div>
  );
}

// With Next.js
import NextLink from 'next/link';

function NextJSIntegration() {
  return (
    <div>
      <NextLink href="/dashboard" passHref>
        <Base.Link variant="nav">
          Dashboard
        </Base.Link>
      </NextLink>
      
      <Base.Link 
        as={NextLink}
        href="/profile"
        prefetch={false}
      >
        Profile
      </Base.Link>
    </div>
  );
}
```

### Custom Styling

```tsx
function CustomStyledLinks() {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
      <Base.Link 
        href="#"
        style={{
          color: '#ff6b6b',
          textDecoration: 'none',
          borderBottom: '2px solid currentColor',
          paddingBottom: '2px'
        }}
      >
        Custom styled link
      </Base.Link>
      
      <Base.Link 
        href="#"
        className="custom-link"
        variant="button"
        style={{
          background: 'linear-gradient(45deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          padding: '12px 24px',
          borderRadius: '8px',
          textDecoration: 'none'
        }}
      >
        Gradient button link
      </Base.Link>
    </div>
  );
}
```

### Conditional Links

```tsx
function ConditionalLinks({ isLoggedIn, userRole }: { 
  isLoggedIn: boolean; 
  userRole: string; 
}) {
  return (
    <nav style={{ display: 'flex', gap: '16px' }}>
      <Base.Link href="/" variant="nav">
        Home
      </Base.Link>
      
      {isLoggedIn ? (
        <>
          <Base.Link href="/dashboard" variant="nav">
            Dashboard
          </Base.Link>
          
          {userRole === 'admin' && (
            <Base.Link href="/admin" variant="nav">
              Admin Panel
            </Base.Link>
          )}
          
          <Base.Link href="/logout" variant="nav">
            Logout
          </Base.Link>
        </>
      ) : (
        <>
          <Base.Link href="/login" variant="nav">
            Login
          </Base.Link>
          
          <Base.Link href="/signup" variant="button">
            Sign Up
          </Base.Link>
        </>
      )}
    </nav>
  );
}
```

### Link with Loading State

```tsx
function LoadingLink() {
  const [isLoading, setIsLoading] = React.useState(false);

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Simulate async operation
    setTimeout(() => {
      setIsLoading(false);
      // Navigate or perform action
    }, 2000);
  };

  return (
    <Base.Link 
      href="/slow-page"
      onClick={handleClick}
      disabled={isLoading}
      leftIcon={isLoading ? <SpinnerIcon /> : <LinkIcon />}
    >
      {isLoading ? 'Loading...' : 'Load Slow Page'}
    </Base.Link>
  );
}
```

### Breadcrumb Links

```tsx
function BreadcrumbLinks({ breadcrumbs }: { 
  breadcrumbs: Array<{ label: string; href: string; current?: boolean }> 
}) {
  return (
    <nav aria-label="Breadcrumb">
      <ol style={{ display: 'flex', listStyle: 'none', padding: 0, margin: 0 }}>
        {breadcrumbs.map((crumb, index) => (
          <li key={crumb.href} style={{ display: 'flex', alignItems: 'center' }}>
            {index > 0 && (
              <span style={{ margin: '0 8px', color: '#6c757d' }}>/</span>
            )}
            
            {crumb.current ? (
              <span aria-current="page" style={{ color: '#6c757d' }}>
                {crumb.label}
              </span>
            ) : (
              <Base.Link 
                href={crumb.href}
                variant="subtle"
                size="small"
              >
                {crumb.label}
              </Base.Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}
```

## Props

### Base.Link Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `href` | `string` | - | URL or path to navigate to |
| `as` | `ElementType` | `'a'` | Component to render as (for router integration) |
| `variant` | `LinkVariant` | `'default'` | Visual variant of the link |
| `size` | `'small' \| 'medium' \| 'large'` | `'medium'` | Size of the link text |
| `state` | `'default' \| 'active' \| 'visited'` | `'default'` | Visual state |
| `external` | `boolean` | `false` | Whether link is external |
| `openInNewTab` | `boolean` | `false` | Open link in new tab |
| `showExternalIcon` | `boolean` | `false` | Show external link icon |
| `disabled` | `boolean` | `false` | Whether link is disabled |
| `leftIcon` | `ReactNode` | - | Icon to display on the left |
| `rightIcon` | `ReactNode` | - | Icon to display on the right |
| `children` | `ReactNode` | - | Link content |
| `onClick` | `(event: MouseEvent) => void` | - | Click event handler |
| `onFocus` | `(event: FocusEvent) => void` | - | Focus event handler |
| `onBlur` | `(event: FocusEvent) => void` | - | Blur event handler |
| `className` | `string` | - | Additional CSS class names |
| `style` | `CSSProperties` | - | Inline styles |
| `aria-label` | `string` | - | Accessible label |
| `aria-describedby` | `string` | - | ID of describing element |
| `title` | `string` | - | Tooltip text |
| `download` | `boolean \| string` | - | Download attribute |
| `rel` | `string` | - | Relationship attribute |
| `target` | `string` | - | Target attribute |

### Type Definitions

```typescript
type LinkVariant = 
  | 'default'
  | 'primary'
  | 'secondary'
  | 'subtle'
  | 'button'
  | 'nav'
  | 'email'
  | 'phone';
```

## Accessibility

### ARIA Support

```tsx
function AccessibleLinks() {
  return (
    <div>
      <Base.Link 
        href="/report"
        aria-label="Download annual report (PDF, 2.5MB)"
        title="Download annual report"
      >
        Annual Report
      </Base.Link>
      
      <Base.Link 
        href="https://external-site.com"
        external
        aria-describedby="external-warning"
      >
        External Resource
      </Base.Link>
      <p id="external-warning" style={{ fontSize: '12px', color: '#6c757d' }}>
        This link will take you to an external website.
      </p>
      
      <Base.Link 
        href="/dashboard"
        aria-current="page"
        state="active"
      >
        Current Page
      </Base.Link>
    </div>
  );
}
```

### Keyboard Navigation

- **Tab** - Navigate to link
- **Enter** - Activate link
- **Space** - Activate link (when focused)
- **Shift + Tab** - Navigate to previous focusable element

### Screen Reader Support

```tsx
function ScreenReaderFriendly() {
  return (
    <div>
      <Base.Link 
        href="/search?q=react"
        aria-label="Search results for react"
      >
        Search Results
      </Base.Link>
      
      <Base.Link 
        href="mailto:<EMAIL>"
        aria-label="Send email to support team"
      >
        Contact Support
      </Base.Link>
      
      <Base.Link 
        href="/page/2"
        aria-label="Go to page 2"
      >
        Next
      </Base.Link>
    </div>
  );
}
```

### Focus Management

```tsx
function FocusManagement() {
  const linkRef = React.useRef<HTMLAnchorElement>(null);

  const focusLink = () => {
    linkRef.current?.focus();
  };

  return (
    <div>
      <button onClick={focusLink}>Focus the link</button>
      
      <Base.Link 
        ref={linkRef}
        href="/important"
        style={{ 
          outline: '2px solid transparent',
          outlineOffset: '2px'
        }}
        onFocus={(e) => {
          e.currentTarget.style.outline = '2px solid #007bff';
        }}
        onBlur={(e) => {
          e.currentTarget.style.outline = '2px solid transparent';
        }}
      >
        Important Link
      </Base.Link>
    </div>
  );
}
```

## Best Practices

### Do's

- ✅ Use descriptive link text that makes sense out of context
- ✅ Provide appropriate ARIA labels for complex links
- ✅ Use external prop for external links
- ✅ Implement proper focus indicators
- ✅ Use semantic HTML attributes (rel, target, etc.)
- ✅ Test with keyboard navigation and screen readers
- ✅ Use consistent styling for similar link types

### Don'ts

- ❌ Don't use "click here" or "read more" as link text
- ❌ Don't open links in new tabs without user consent
- ❌ Don't remove focus indicators without providing alternatives
- ❌ Don't use links for actions that don't navigate
- ❌ Don't forget to handle loading and error states
- ❌ Don't ignore security considerations for external links
- ❌ Don't make links too small for touch targets

## Performance

### Optimization Tips

```tsx
// ✅ Good: Memoize complex link components
const ComplexLink = React.memo(function ComplexLink({ 
  href, 
  children, 
  ...props 
}) {
  return (
    <Base.Link href={href} {...props}>
      {children}
    </Base.Link>
  );
});

// ✅ Good: Use prefetching for important links
function PrefetchedLink({ href, children }) {
  return (
    <Base.Link 
      href={href}
      onMouseEnter={() => {
        // Prefetch the page
        if (typeof window !== 'undefined') {
          const link = document.createElement('link');
          link.rel = 'prefetch';
          link.href = href;
          document.head.appendChild(link);
        }
      }}
    >
      {children}
    </Base.Link>
  );
}

// ✅ Good: Lazy load external link icons
const ExternalIcon = React.lazy(() => import('./ExternalIcon'));

function LazyExternalLink({ href, children }) {
  return (
    <Base.Link href={href} external>
      {children}
      <React.Suspense fallback={null}>
        <ExternalIcon />
      </React.Suspense>
    </Base.Link>
  );
}
```

### Bundle Size

- Minimal CSS footprint
- Tree-shakeable icon components
- No external dependencies for core functionality
- Optimized for modern browsers

### Runtime Performance

- Efficient event handling
- Minimal re-renders with proper memoization
- Optimized focus management
- Lazy loading of non-critical features