---
title: "Column()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / Column

# Function: Column()

> **Column**(`__namedParameters`): `Element`

Defined in: web/src/components/Core.View/View.Column.tsx:14

Column component - Vertical layout with alignment controls
Provides flexible vertical layout with gap, alignment, and distribution controls

## Parameters

### \_\_namedParameters

[`ColumnProps`](../interfaces/ColumnProps.md)

## Returns

`Element`
