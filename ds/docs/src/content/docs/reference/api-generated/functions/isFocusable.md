---
title: "isFocusable()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / isFocusable

# Function: isFocusable()

> **isFocusable**(`element`): `boolean`

Defined in: web/src/utils/componentHelpers.ts:166

Check if an element is focusable
Useful for keyboard navigation and accessibility

## Parameters

### element

`HTMLElement`

## Returns

`boolean`
