---
title: "createFlexStyles()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / createFlexStyles

# Function: createFlexStyles()

> **createFlexStyles**(`direction`, `align`, `justify`, `wrap`): `StyleRule`

Defined in: web/src/utils/css.ts:296

## Parameters

### direction

`"row"` | `"column"`

### align

`"center"` | `"end"` | `"start"` | `"stretch"`

### justify

`"center"` | `"end"` | `"start"` | `"between"` | `"around"` | `"evenly"`

### wrap

`boolean` = `false`

## Returns

`StyleRule`
