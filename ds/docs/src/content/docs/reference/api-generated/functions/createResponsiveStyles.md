---
title: "createResponsiveStyles()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / createResponsiveStyles

# Function: createResponsiveStyles()

> **createResponsiveStyles**\<`T`\>(`property`, `value`): `StyleRule`

Defined in: web/src/constants/defaults/themeUtils.ts:16

## Type Parameters

### T

`T`

## Parameters

### property

keyof `CSSProperties`

### value

[`ResponsiveValue`](../type-aliases/ResponsiveValue.md)\<`T`\>

## Returns

`StyleRule`
