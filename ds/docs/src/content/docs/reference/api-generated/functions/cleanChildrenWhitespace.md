---
title: "cleanChildrenWhitespace()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / cleanChildrenWhitespace

# Function: cleanChildrenWhitespace()

> **cleanChildrenWhitespace**(`children`): `ReactNode`

Defined in: web/src/utils/componentHelpers.ts:13

Clean whitespace from children, removing string children that are only whitespace
Useful for compound components where whitespace can cause layout issues

## Parameters

### children

`ReactNode`

## Returns

`ReactNode`
