---
title: "cloneChildrenWithProps()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / cloneChildrenWithProps

# Function: cloneChildrenWithProps()

> **cloneChildrenWithProps**\<`T`\>(`children`, `props`): `ReactNode`

Defined in: web/src/utils/componentHelpers.ts:88

Clone children with additional props
Useful for passing context or shared props to all children

## Type Parameters

### T

`T` *extends* `Record`\<`string`, `unknown`\>

## Parameters

### children

`ReactNode`

### props

`T`

## Returns

`ReactNode`
