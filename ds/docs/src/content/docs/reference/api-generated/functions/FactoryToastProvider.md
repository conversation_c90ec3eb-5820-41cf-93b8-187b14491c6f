---
title: "FactoryToastProvider()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / FactoryToastProvider

# Function: FactoryToastProvider()

> **FactoryToastProvider**(`__namedParameters`): `ReactNode`

Defined in: web/src/components/Factory.Toast/Toast.Provider.tsx:9

Complete Toast Provider that includes both context and container

## Parameters

### \_\_namedParameters

[`FactoryToastProviderProps`](../interfaces/FactoryToastProviderProps.md)

## Returns

`ReactNode`
