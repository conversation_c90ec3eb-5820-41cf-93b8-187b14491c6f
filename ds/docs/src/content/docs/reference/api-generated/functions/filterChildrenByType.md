---
title: "filterChildrenByType()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / filterChildrenByType

# Function: filterChildrenByType()

> **filterChildrenByType**\<`T`\>(`children`, `types`): `T`[]

Defined in: web/src/utils/componentHelpers.ts:35

Filter children by component type or display name
Useful for compound components that need to separate different child types

## Type Parameters

### T

`T` = `ReactElement`\<`any`, `string` \| `JSXElementConstructor`\<`any`\>\>

## Parameters

### children

`ReactNode`

### types

`string` | `string`[] | `ComponentType`\<`unknown`\> | `ComponentType`\<`unknown`\>[]

## Returns

`T`[]
