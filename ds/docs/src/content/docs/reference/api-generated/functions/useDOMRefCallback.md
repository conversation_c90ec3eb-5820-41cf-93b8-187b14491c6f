---
title: "useDOMRefCallback()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / useDOMRefCallback

# Function: useDOMRefCallback()

> **useDOMRefCallback**\<`T`\>(`$ref?`, `callback?`): (`node`) => `void`

Defined in: web/src/hooks/useDOMRef.ts:48

A hook that creates a callback ref for handling the $ref prop pattern
This is useful when you need to perform actions when the ref changes

## Type Parameters

### T

`T` *extends* `HTMLElement` = `HTMLElement`

## Parameters

### $ref?

[`DOMRef`](../type-aliases/DOMRef.md)\<`T`\>

The external ref passed via $ref prop

### callback?

(`node`) => `void`

Optional callback to run when the ref is set

## Returns

A callback ref function

> (`node`): `void`

### Parameters

#### node

`null` | `T`

### Returns

`void`
