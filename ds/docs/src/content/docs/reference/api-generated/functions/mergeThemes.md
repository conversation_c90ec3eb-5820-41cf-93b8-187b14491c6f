---
title: "mergeThemes()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / mergeThemes

# Function: mergeThemes()

> **mergeThemes**\<`T`\>(`baseTheme`, `overrideTheme`): `T`

Defined in: web/src/constants/defaults/themeUtils.ts:81

## Type Parameters

### T

`T` *extends* `Record`\<`string`, `any`\>

## Parameters

### baseTheme

`T`

### overrideTheme

`Partial`\<`T`\>

## Returns

`T`
