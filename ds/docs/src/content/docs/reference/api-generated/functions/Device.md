---
title: "Device()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / Device

# Function: Device()

> **Device**(`__namedParameters`): `Element`

Defined in: web/src/components/Core.View/View.Device.tsx:20

Device component - Media query and device-related utilities
Provides responsive visibility controls based on breakpoints and device capabilities

## Parameters

### \_\_namedParameters

[`DeviceProps`](../interfaces/DeviceProps.md)

## Returns

`Element`
