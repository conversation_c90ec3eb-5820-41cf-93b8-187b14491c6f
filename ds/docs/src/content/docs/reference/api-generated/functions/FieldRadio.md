---
title: "FieldRadio()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / FieldRadio

# Function: FieldRadio()

> **FieldRadio**(`__namedParameters`): `Element`

Defined in: web/src/components/Base/Field/Field.Radio.tsx:20

Field.Radio component for radio button input
Supports controlled/uncontrolled modes for single selection

## Parameters

### \_\_namedParameters

[`FieldRadioProps`](../interfaces/FieldRadioProps.md)

## Returns

`Element`
