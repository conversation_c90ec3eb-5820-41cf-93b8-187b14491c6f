---
title: "DripWithHook()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / DripWithHook

# Function: DripWithHook()

> **DripWithHook**(`__namedParameters`): `object`

Defined in: web/src/components/Base/Drip/Drip.tsx:159

Drip component that uses the useDrip hook internally
Provides both the drip rendering and event handlers

## Parameters

### \_\_namedParameters

[`UseDripComponentProps`](../interfaces/UseDripComponentProps.md)

## Returns

`object`

### dripComponent

> **dripComponent**: `Element`

### dripHandlers

> **dripHandlers**: `object`

#### dripHandlers.onMouseDown()

> **onMouseDown**: (`event`) => `void`

##### Parameters

###### event

`MouseEvent`\<`HTMLElement`\>

##### Returns

`void`

#### dripHandlers.onTouchStart()

> **onTouchStart**: (`event`) => `void`

##### Parameters

###### event

`TouchEvent`\<`HTMLElement`\>

##### Returns

`void`
