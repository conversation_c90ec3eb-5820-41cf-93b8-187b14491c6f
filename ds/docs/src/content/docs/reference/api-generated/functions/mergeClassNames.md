---
title: "mergeClassNames()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / mergeClassNames

# Function: mergeClassNames()

> **mergeClassNames**(...`classNames`): `string`

Defined in: web/src/utils/componentHelpers.ts:148

Merge class names, filtering out falsy values
Simple utility for conditional class names

## Parameters

### classNames

...(`undefined` \| `null` \| `string` \| `false`)[]

## Returns

`string`
