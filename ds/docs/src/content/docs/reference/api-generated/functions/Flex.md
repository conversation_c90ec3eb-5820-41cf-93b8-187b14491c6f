---
title: "Flex()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / Flex

# Function: Flex()

> **Flex**(`__namedParameters`): `Element`

Defined in: web/src/components/Base/Flex/Flex.tsx:77

Flex component for flexible layouts
Provides a convenient wrapper around CSS flexbox

## Parameters

### \_\_namedParameters

[`FlexProps`](../interfaces/FlexProps.md)

## Returns

`Element`
