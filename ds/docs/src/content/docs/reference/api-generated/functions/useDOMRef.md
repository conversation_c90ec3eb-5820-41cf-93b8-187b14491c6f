---
title: "useDOMRef()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / useDOMRef

# Function: useDOMRef()

> **useDOMRef**\<`T`\>(`$ref?`): `RefObject`\<`T`\>

Defined in: web/src/hooks/useDOMRef.ts:25

A hook that creates a DOM ref and handles the $ref prop pattern
This allows components to accept a $ref prop instead of using forwardRef

## Type Parameters

### T

`T` *extends* `HTMLElement` = `HTMLElement`

## Parameters

### $ref?

[`DOMRef`](../type-aliases/DOMRef.md)\<`T`\>

The external ref passed via $ref prop

## Returns

`RefObject`\<`T`\>

A ref object that can be attached to DOM elements
