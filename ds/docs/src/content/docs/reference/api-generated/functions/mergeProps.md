---
title: "mergeProps()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / mergeProps

# Function: mergeProps()

> **mergeProps**(`slotProps`, `childProps`): `object`

Defined in: web/src/components/Base/Clone/Clone.tsx:42

Merge props from slot and slottable, with slottable taking precedence

## Parameters

### slotProps

`Record`\<`string`, `any`\>

### childProps

`Record`\<`string`, `any`\>

## Returns

`object`
