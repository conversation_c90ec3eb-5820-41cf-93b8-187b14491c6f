---
title: "getFocusableElements()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / getFocusableElements

# Function: getFocusableElements()

> **getFocusableElements**(`container`): `HTMLElement`[]

Defined in: web/src/utils/componentHelpers.ts:199

Get all focusable elements within a container
Useful for focus management in modals and dropdowns

## Parameters

### container

`HTMLElement`

## Returns

`HTMLElement`[]
