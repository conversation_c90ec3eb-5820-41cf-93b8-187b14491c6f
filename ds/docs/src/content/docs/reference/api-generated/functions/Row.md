---
title: "Row()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / Row

# Function: Row()

> **Row**(`__namedParameters`): `Element`

Defined in: web/src/components/Core.View/View.Row.tsx:14

Row component - Horizontal layout with gap and alignment controls
Provides flexible horizontal layout with gap, alignment, and wrapping controls

## Parameters

### \_\_namedParameters

[`RowProps`](../interfaces/RowProps.md)

## Returns

`Element`
