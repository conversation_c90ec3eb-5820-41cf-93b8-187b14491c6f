---
title: "DocumentTitle()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / DocumentTitle

# Function: DocumentTitle()

> **DocumentTitle**(`__namedParameters`): `null`

Defined in: web/src/components/Core.Document/Document.Title.tsx:13

Document Title component
Manages dynamic document title with template support

## Parameters

### \_\_namedParameters

[`DocumentTitleProps`](../interfaces/DocumentTitleProps.md)

## Returns

`null`
