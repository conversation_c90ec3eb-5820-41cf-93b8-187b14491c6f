---
title: "isSlottable()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / isSlottable

# Function: isSlottable()

> **isSlottable**(`child`): child is ReactElement\<any, string \| JSXElementConstructor\<any\>\>

Defined in: web/src/components/Base/Clone/Clone.tsx:34

Check if a React element is a Slottable component

## Parameters

### child

`ReactNode`

## Returns

child is ReactElement\<any, string \| JSXElementConstructor\<any\>\>
