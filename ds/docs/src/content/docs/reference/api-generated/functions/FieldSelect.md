---
title: "FieldSelect()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / FieldSelect

# Function: FieldSelect()

> **FieldSelect**(`__namedParameters`): `Element`

Defined in: web/src/components/Base/Field/Field.Select.tsx:16

Field.Select component for dropdown selection
Supports both single and multiple selection modes

## Parameters

### \_\_namedParameters

[`FieldSelectProps`](../interfaces/FieldSelectProps.md)

## Returns

`Element`
