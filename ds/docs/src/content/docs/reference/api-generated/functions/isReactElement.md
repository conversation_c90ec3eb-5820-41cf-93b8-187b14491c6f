---
title: "isReactElement()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / isReactElement

# Function: isReactElement()

> **isReactElement**(`value`): value is ReactElement\<any, string \| JSXElementConstructor\<any\>\>

Defined in: web/src/utils/componentHelpers.ts:108

Check if a value is a valid React element
Type guard for React elements

## Parameters

### value

`unknown`

## Returns

value is ReactElement\<any, string \| JSXElementConstructor\<any\>\>
