---
title: "createComponentRecipe()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / createComponentRecipe

# Function: createComponentRecipe()

> **createComponentRecipe**(`baseStyles`, `variants`, `defaultVariants?`): `RuntimeFn`\<`Record`\<`string`, `Record`\<`string`, `StyleRule`\>\>\>

Defined in: web/src/utils/css.ts:424

## Parameters

### baseStyles

`StyleRule`

### variants

`Record`\<`string`, `Record`\<`string`, `StyleRule`\>\>

### defaultVariants?

`Record`\<`string`, `string`\>

## Returns

`RuntimeFn`\<`Record`\<`string`, `Record`\<`string`, `StyleRule`\>\>\>
