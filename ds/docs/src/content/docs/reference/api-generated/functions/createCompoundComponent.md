---
title: "createCompoundComponent()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / createCompoundComponent

# Function: createCompoundComponent()

> **createCompoundComponent**\<`T`, `U`\>(`MainComponent`, `subComponents`): `T` & `U`

Defined in: web/src/utils/componentHelpers.ts:126

Create a compound component with proper TypeScript support
Helps with creating components that have sub-components attached

## Type Parameters

### T

`T` *extends* `ComponentType`\<`unknown`\>

### U

`U` *extends* `Record`\<`string`, `ComponentType`\<`unknown`\>\>

## Parameters

### MainComponent

`T`

### subComponents

`U`

## Returns

`T` & `U`
