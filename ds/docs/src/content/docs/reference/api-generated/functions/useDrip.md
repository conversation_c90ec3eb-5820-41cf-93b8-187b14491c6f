---
title: "useDrip()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / useDrip

# Function: useDrip()

> **useDrip**(`options`): [`UseDripReturn`](../interfaces/UseDripReturn.md)

Defined in: web/src/hooks/useDrip.ts:39

A hook that provides ripple effect functionality
Creates and manages drip animations for interactive components

## Parameters

### options

[`DripOptions`](../interfaces/DripOptions.md) = `{}`

Configuration options for the drip effect

## Returns

[`UseDripReturn`](../interfaces/UseDripReturn.md)

Object containing drip state and event handlers
