---
title: "FieldDate()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / FieldDate

# Function: FieldDate()

> **FieldDate**(`__namedParameters`): `Element`

Defined in: web/src/components/Base/Field/Field.Date.tsx:16

Field.Date component for date/time input
Supports various date and time input types

## Parameters

### \_\_namedParameters

[`FieldDateProps`](../interfaces/FieldDateProps.md)

## Returns

`Element`
