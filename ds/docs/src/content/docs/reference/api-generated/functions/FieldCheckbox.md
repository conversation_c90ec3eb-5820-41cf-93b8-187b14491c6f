---
title: "FieldCheckbox()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / FieldCheckbox

# Function: FieldCheckbox()

> **FieldCheckbox**(`__namedParameters`): `Element`

Defined in: web/src/components/Base/Field/Field.Checkbox.tsx:20

Field.Checkbox component for checkbox input
Supports controlled/uncontrolled modes and indeterminate state

## Parameters

### \_\_namedParameters

[`FieldCheckboxProps`](../interfaces/FieldCheckboxProps.md)

## Returns

`Element`
