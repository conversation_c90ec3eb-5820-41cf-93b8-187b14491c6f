---
title: "responsiveStyle()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / responsiveStyle

# Function: responsiveStyle()

> **responsiveStyle**(`styles`): `StyleRule`

Defined in: web/src/utils/css.ts:22

## Parameters

### styles

`Partial`\<`Record`\<[`BreakpointKey`](../type-aliases/BreakpointKey.md), `StyleRule`\>\>

## Returns

`StyleRule`
