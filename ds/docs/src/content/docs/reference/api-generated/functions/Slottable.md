---
title: "Slottable()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / Slottable

# Function: Slottable()

> **Slottable**(`__namedParameters`): `Element`

Defined in: web/src/components/Base/Clone/Clone.tsx:143

Slottable component that marks its children as replaceable
Similar to Radix UI's Slottable component

## Parameters

### \_\_namedParameters

[`SlottableProps`](../interfaces/SlottableProps.md)

## Returns

`Element`
