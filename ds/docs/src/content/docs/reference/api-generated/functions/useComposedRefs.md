---
title: "useComposedRefs()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / useComposedRefs

# Function: useComposedRefs()

> **useComposedRefs**\<`T`\>(...`refs`): (`node`) => `void`

Defined in: web/src/utils/composeRefs.ts:40

A custom hook that composes multiple refs
Accepts callback refs and RefObject(s)

## Type Parameters

### T

`T`

## Parameters

### refs

...`PossibleRef`\<`T`\>[]

## Returns

> (`node`): `void`

### Parameters

#### node

`T`

### Returns

`void`
