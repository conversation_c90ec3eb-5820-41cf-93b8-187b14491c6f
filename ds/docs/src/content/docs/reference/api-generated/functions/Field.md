---
title: "Field()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / Field

# Function: Field()

> **Field**(`__namedParameters`): `Element`

Defined in: web/src/components/Base/Field/Field.tsx:33

Base Field wrapper component
Provides consistent layout and validation for form fields

## Parameters

### \_\_namedParameters

[`FieldProps`](../interfaces/FieldProps.md)

## Returns

`Element`
