---
title: "createKeyboardShortcut()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / createKeyboardShortcut

# Function: createKeyboardShortcut()

> **createKeyboardShortcut**(`key`, `modifiers`): `string`

Defined in: web/src/constants/keyCodes.ts:286

## Parameters

### key

`string`

### modifiers

(`"meta"` \| `"alt"` \| `"ctrl"` \| `"shift"`)[] = `[]`

## Returns

`string`
