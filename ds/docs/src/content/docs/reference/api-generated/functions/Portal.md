---
title: "Portal()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / Portal

# Function: Portal()

> **Portal**(`__namedParameters`): `null` \| `Element`

Defined in: web/src/components/Base/Portal/Portal.tsx:38

Portal component that renders children outside the normal DOM hierarchy
Useful for modals, tooltips, and other overlay components

## Parameters

### \_\_namedParameters

[`PortalProps`](../interfaces/PortalProps.md)

## Returns

`null` \| `Element`
