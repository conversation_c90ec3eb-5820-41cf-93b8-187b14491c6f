---
title: "composeRefs()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / composeRefs

# Function: composeRefs()

> **composeRefs**\<`T`\>(...`refs`): (`node`) => `void`

Defined in: web/src/utils/composeRefs.ts:31

A utility to compose multiple refs together
Accepts callback refs and RefObject(s)

## Type Parameters

### T

`T`

## Parameters

### refs

...`PossibleRef`\<`T`\>[]

## Returns

> (`node`): `void`

### Parameters

#### node

`T`

### Returns

`void`
