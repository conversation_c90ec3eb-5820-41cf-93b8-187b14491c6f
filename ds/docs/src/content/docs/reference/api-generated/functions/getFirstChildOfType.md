---
title: "getFirstChildOfType()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / getFirstChildOfType

# Function: getFirstChildOfType()

> **getFirstChildOfType**\<`T`\>(`children`, `type`): `null` \| `T`

Defined in: web/src/utils/componentHelpers.ts:75

Get only the first child of a specific type
Useful when you need to ensure only one instance of a component type

## Type Parameters

### T

`T` = `ReactElement`\<`any`, `string` \| `JSXElementConstructor`\<`any`\>\>

## Parameters

### children

`ReactNode`

### type

`string` | `ComponentType`\<`unknown`\>

## Returns

`null` \| `T`
