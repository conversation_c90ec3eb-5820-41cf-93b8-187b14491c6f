---
title: "View()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / View

# Function: View()

> **View**(`__namedParameters`): `Element`

Defined in: web/src/components/Core.View/View.tsx:14

View component - Base layout container with responsive capabilities
Provides a foundational container for building layouts

## Parameters

### \_\_namedParameters

[`ViewProps`](../interfaces/ViewProps.md)

## Returns

`Element`
