---
title: "Container()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / Container

# Function: Container()

> **Container**(`__namedParameters`): `Element`

Defined in: web/src/components/Core.View/View.Container.tsx:14

Container component - Constrained width container with breakpoint support
Provides responsive container with maximum width constraints

## Parameters

### \_\_namedParameters

[`ContainerProps`](../interfaces/ContainerProps.md)

## Returns

`Element`
