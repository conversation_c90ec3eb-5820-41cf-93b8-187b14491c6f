---
title: "setRef()"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / setRef

# Function: setRef()

> **setRef**\<`T`\>(`ref`, `value`): `void`

Defined in: web/src/utils/composeRefs.ts:15

Set a given ref to a given value
This utility takes care of different types of refs: callback refs and RefObject(s)

## Type Parameters

### T

`T`

## Parameters

### ref

`PossibleRef`\<`T`\>

### value

`T`

## Returns

`void`
