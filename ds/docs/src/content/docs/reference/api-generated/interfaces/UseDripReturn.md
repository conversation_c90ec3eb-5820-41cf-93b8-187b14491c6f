---
title: "UseDripReturn"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / UseDripReturn

# Interface: UseDripReturn

Defined in: web/src/hooks/useDrip.ts:24

## Properties

### drips

> **drips**: [`DripState`](DripState.md)[]

Defined in: web/src/hooks/useDrip.ts:26

***

### onMouseDown()

> **onMouseDown**: (`event`) => `void`

Defined in: web/src/hooks/useDrip.ts:27

#### Parameters

##### event

`MouseEvent`\<`HTMLElement`\>

#### Returns

`void`

***

### onTouchStart()

> **onTouchStart**: (`event`) => `void`

Defined in: web/src/hooks/useDrip.ts:28

#### Parameters

##### event

`TouchEvent`\<`HTMLElement`\>

#### Returns

`void`

***

### clearDrips()

> **clearDrips**: () => `void`

Defined in: web/src/hooks/useDrip.ts:29

#### Returns

`void`
