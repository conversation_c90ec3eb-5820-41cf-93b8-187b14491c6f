---
title: "StateProps"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / StateProps

# Interface: StateProps

Defined in: web/src/constants/defaults/commonTypes.ts:64

## Extended by

- [`FormFieldProps`](FormFieldProps.md)
- [`ElementButtonProps`](ElementButtonProps.md)
- [`CollectionIndicatorLoadingProps`](CollectionIndicatorLoadingProps.md)
- [`CollectionIndicatorProgressProps`](CollectionIndicatorProgressProps.md)

## Properties

### disabled?

> `optional` **disabled**: `boolean`

Defined in: web/src/constants/defaults/commonTypes.ts:69

Whether the component is disabled

***

### loading?

> `optional` **loading**: `boolean`

Defined in: web/src/constants/defaults/commonTypes.ts:74

Whether the component is in a loading state

***

### readOnly?

> `optional` **readOnly**: `boolean`

Defined in: web/src/constants/defaults/commonTypes.ts:79

Whether the component is read-only

***

### required?

> `optional` **required**: `boolean`

Defined in: web/src/constants/defaults/commonTypes.ts:84

Whether the component is required (for form elements)
