---
title: "TableState"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / TableState

# Interface: TableState

Defined in: web/src/components/Element.Table/Table.types.ts:292

## Properties

### sortDescriptors

> **sortDescriptors**: [`SortDescriptor`](SortDescriptor.md)[]

Defined in: web/src/components/Element.Table/Table.types.ts:297

Current sort descriptors

***

### filterDescriptors

> **filterDescriptors**: [`FilterDescriptor`](FilterDescriptor.md)[]

Defined in: web/src/components/Element.Table/Table.types.ts:302

Current filter descriptors

***

### selectedKeys

> **selectedKeys**: `Set`\<`string` \| `number`\>

Defined in: web/src/components/Element.Table/Table.types.ts:307

Currently selected keys

***

### isSelectAll

> **isSelectAll**: `boolean`

Defined in: web/src/components/Element.Table/Table.types.ts:312

Whether all rows are selected
