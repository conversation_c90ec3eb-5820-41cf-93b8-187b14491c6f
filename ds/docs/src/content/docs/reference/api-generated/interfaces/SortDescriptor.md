---
title: "SortDescriptor"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / SortDescriptor

# Interface: SortDescriptor

Defined in: web/src/components/Element.Table/Table.types.ts:259

## Properties

### column

> **column**: `string` \| `number`

Defined in: web/src/components/Element.Table/Table.types.ts:264

Column key to sort by

***

### direction

> **direction**: `"ascending"` \| `"descending"`

Defined in: web/src/components/Element.Table/Table.types.ts:269

Sort direction
