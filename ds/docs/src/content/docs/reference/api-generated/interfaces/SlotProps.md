---
title: "SlotProps"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / SlotProps

# Interface: SlotProps

Defined in: web/src/components/Base/Clone/Clone.tsx:10

## Extends

- `HTMLAttributes`\<`HTMLElement`\>

## Properties

### children?

> `optional` **children**: `ReactNode`

Defined in: web/src/components/Base/Clone/Clone.tsx:15

The content to render

#### Overrides

`HTMLAttributes.children`

***

### $ref?

> `optional` **$ref**: `null` \| `RefObject`\<`HTMLElement`\>

Defined in: web/src/components/Base/Clone/Clone.tsx:20

Custom ref prop
