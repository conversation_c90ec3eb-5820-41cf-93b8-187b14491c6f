---
title: "UseDripComponentProps"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / UseDripComponentProps

# Interface: UseDripComponentProps

Defined in: web/src/components/Base/Drip/Drip.tsx:127

Hook-based drip component that manages its own state

## Properties

### disabled?

> `optional` **disabled**: `boolean`

Defined in: web/src/components/Base/Drip/Drip.tsx:132

Whether the drip effect is disabled

***

### color?

> `optional` **color**: `string`

Defined in: web/src/components/Base/Drip/Drip.tsx:137

Color of the drip effect

***

### size?

> `optional` **size**: `number`

Defined in: web/src/components/Base/Drip/Drip.tsx:142

Size of the drip effect

***

### duration?

> `optional` **duration**: `number`

Defined in: web/src/components/Base/Drip/Drip.tsx:147

Duration of the drip animation in milliseconds

***

### data-testid?

> `optional` **data-testid**: `string`

Defined in: web/src/components/Base/Drip/Drip.tsx:152

Test ID for testing purposes
