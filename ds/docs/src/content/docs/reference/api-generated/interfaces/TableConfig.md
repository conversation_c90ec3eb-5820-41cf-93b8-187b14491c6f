---
title: "TableConfig"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / TableConfig

# Interface: TableConfig

Defined in: web/src/components/Element.Table/Table.types.ts:316

## Properties

### sortingEnabled?

> `optional` **sortingEnabled**: `boolean`

Defined in: web/src/components/Element.Table/Table.types.ts:321

Whether sorting is enabled

***

### filteringEnabled?

> `optional` **filteringEnabled**: `boolean`

Defined in: web/src/components/Element.Table/Table.types.ts:326

Whether filtering is enabled

***

### selectionEnabled?

> `optional` **selectionEnabled**: `boolean`

Defined in: web/src/components/Element.Table/Table.types.ts:331

Whether selection is enabled

***

### defaultSortDescriptors?

> `optional` **defaultSortDescriptors**: [`SortDescriptor`](SortDescriptor.md)[]

Defined in: web/src/components/Element.Table/Table.types.ts:336

Default sort descriptors

***

### defaultFilterDescriptors?

> `optional` **defaultFilterDescriptors**: [`FilterDescriptor`](FilterDescriptor.md)[]

Defined in: web/src/components/Element.Table/Table.types.ts:341

Default filter descriptors

***

### onSortChange()?

> `optional` **onSortChange**: (`descriptors`) => `void`

Defined in: web/src/components/Element.Table/Table.types.ts:346

Callback fired when sorting changes

#### Parameters

##### descriptors

[`SortDescriptor`](SortDescriptor.md)[]

#### Returns

`void`

***

### onFilterChange()?

> `optional` **onFilterChange**: (`descriptors`) => `void`

Defined in: web/src/components/Element.Table/Table.types.ts:351

Callback fired when filtering changes

#### Parameters

##### descriptors

[`FilterDescriptor`](FilterDescriptor.md)[]

#### Returns

`void`
