---
title: "ViewProps"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / ViewProps

# Interface: ViewProps

Defined in: web/src/components/Core.View/View.types.ts:21

Props for the main View component

## Extends

- [`ViewBaseProps`](ViewBaseProps.md)

## Properties

### as?

> `optional` **as**: keyof IntrinsicElements

Defined in: web/src/components/Core.View/View.types.ts:15

HTML element to render as

#### Inherited from

[`ViewBaseProps`](ViewBaseProps.md).[`as`](ViewBaseProps.md#as)

***

### fullWidth?

> `optional` **fullWidth**: `boolean`

Defined in: web/src/components/Core.View/View.types.ts:26

Whether the view should be full width

***

### fullHeight?

> `optional` **fullHeight**: `boolean`

Defined in: web/src/components/Core.View/View.types.ts:31

Whether the view should be full height

***

### padding?

> `optional` **padding**: `string` \| `number`

Defined in: web/src/components/Core.View/View.types.ts:36

Padding for the view

***

### margin?

> `optional` **margin**: `string` \| `number`

Defined in: web/src/components/Core.View/View.types.ts:41

Margin for the view

***

### className?

> `optional` **className**: `string`

Defined in: web/src/constants/defaults/commonTypes.ts:13

Additional CSS class names to apply to the component

#### Inherited from

[`ViewBaseProps`](ViewBaseProps.md).[`className`](ViewBaseProps.md#classname)

***

### style?

> `optional` **style**: `CSSProperties`

Defined in: web/src/constants/defaults/commonTypes.ts:18

Inline styles to apply to the component

#### Inherited from

[`ViewBaseProps`](ViewBaseProps.md).[`style`](ViewBaseProps.md#style)

***

### $ref?

> `optional` **$ref**: `null` \| `RefObject`\<`HTMLElement`\>

Defined in: web/src/constants/defaults/commonTypes.ts:24

Custom ref prop to avoid forwardRef usage
This provides a cleaner API while maintaining ref functionality

#### Inherited from

[`ViewBaseProps`](ViewBaseProps.md).[`$ref`](ViewBaseProps.md#ref)

***

### children?

> `optional` **children**: `ReactNode`

Defined in: web/src/constants/defaults/commonTypes.ts:29

Child elements to render inside the component

#### Inherited from

[`ViewBaseProps`](ViewBaseProps.md).[`children`](ViewBaseProps.md#children)

***

### data-testid?

> `optional` **data-testid**: `string`

Defined in: web/src/constants/defaults/commonTypes.ts:34

Test ID for testing purposes

#### Inherited from

[`ViewBaseProps`](ViewBaseProps.md).[`data-testid`](ViewBaseProps.md#data-testid)
