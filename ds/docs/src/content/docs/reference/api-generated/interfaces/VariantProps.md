---
title: "VariantProps"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / VariantProps

# Interface: VariantProps

Defined in: web/src/constants/defaults/commonTypes.ts:135

## Extended by

- [`ElementTableProps`](ElementTableProps.md)

## Properties

### size?

> `optional` **size**: [`SizeVariant`](../type-aliases/SizeVariant.md)

Defined in: web/src/constants/defaults/commonTypes.ts:140

Size variant of the component

***

### color?

> `optional` **color**: [`ColorVariant`](../type-aliases/ColorVariant.md)

Defined in: web/src/constants/defaults/commonTypes.ts:145

Color variant of the component

***

### variant?

> `optional` **variant**: [`StyleVariant`](../type-aliases/StyleVariant.md)

Defined in: web/src/constants/defaults/commonTypes.ts:150

Style variant of the component

***

### radius?

> `optional` **radius**: [`RadiusVariant`](../type-aliases/RadiusVariant.md)

Defined in: web/src/constants/defaults/commonTypes.ts:155

Border radius variant
