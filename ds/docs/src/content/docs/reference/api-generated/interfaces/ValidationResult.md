---
title: "ValidationResult"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / ValidationResult

# Interface: ValidationResult

Defined in: web/src/components/Base/Field/Field.types.ts:427

## Properties

### isValid

> **isValid**: `boolean`

Defined in: web/src/components/Base/Field/Field.types.ts:429

***

### errorMessage?

> `optional` **errorMessage**: `string`

Defined in: web/src/components/Base/Field/Field.types.ts:430
