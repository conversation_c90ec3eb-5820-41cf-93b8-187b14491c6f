---
title: "ScrollbarProps"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / ScrollbarProps

# Interface: ScrollbarProps

Defined in: web/src/components/Core.View/View.types.ts:68

Props for the Scrollbar component

## Extends

- [`ViewBaseProps`](ViewBaseProps.md)

## Properties

### as?

> `optional` **as**: keyof IntrinsicElements

Defined in: web/src/components/Core.View/View.types.ts:15

HTML element to render as

#### Inherited from

[`ViewBaseProps`](ViewBaseProps.md).[`as`](ViewBaseProps.md#as)

***

### size?

> `optional` **size**: `"sm"` \| `"md"` \| `"lg"`

Defined in: web/src/components/Core.View/View.types.ts:73

Scrollbar size

***

### hideWhenNotScrolling?

> `optional` **hideWhenNotScrolling**: `boolean`

Defined in: web/src/components/Core.View/View.types.ts:78

Whether to hide scrollbars when not scrolling

***

### native?

> `optional` **native**: `boolean`

Defined in: web/src/components/Core.View/View.types.ts:83

Whether to use native scrollbars

***

### maxHeight?

> `optional` **maxHeight**: `string` \| `number`

Defined in: web/src/components/Core.View/View.types.ts:88

Maximum height before scrolling

***

### maxWidth?

> `optional` **maxWidth**: `string` \| `number`

Defined in: web/src/components/Core.View/View.types.ts:93

Maximum width before scrolling

***

### direction?

> `optional` **direction**: `"both"` \| `"horizontal"` \| `"vertical"`

Defined in: web/src/components/Core.View/View.types.ts:98

Scroll direction

***

### onScroll()?

> `optional` **onScroll**: (`event`) => `void`

Defined in: web/src/components/Core.View/View.types.ts:103

Scroll event handler

#### Parameters

##### event

`UIEvent`\<`HTMLDivElement`\>

#### Returns

`void`

***

### className?

> `optional` **className**: `string`

Defined in: web/src/constants/defaults/commonTypes.ts:13

Additional CSS class names to apply to the component

#### Inherited from

[`ViewBaseProps`](ViewBaseProps.md).[`className`](ViewBaseProps.md#classname)

***

### style?

> `optional` **style**: `CSSProperties`

Defined in: web/src/constants/defaults/commonTypes.ts:18

Inline styles to apply to the component

#### Inherited from

[`ViewBaseProps`](ViewBaseProps.md).[`style`](ViewBaseProps.md#style)

***

### $ref?

> `optional` **$ref**: `null` \| `RefObject`\<`HTMLElement`\>

Defined in: web/src/constants/defaults/commonTypes.ts:24

Custom ref prop to avoid forwardRef usage
This provides a cleaner API while maintaining ref functionality

#### Inherited from

[`ViewBaseProps`](ViewBaseProps.md).[`$ref`](ViewBaseProps.md#ref)

***

### children?

> `optional` **children**: `ReactNode`

Defined in: web/src/constants/defaults/commonTypes.ts:29

Child elements to render inside the component

#### Inherited from

[`ViewBaseProps`](ViewBaseProps.md).[`children`](ViewBaseProps.md#children)

***

### data-testid?

> `optional` **data-testid**: `string`

Defined in: web/src/constants/defaults/commonTypes.ts:34

Test ID for testing purposes

#### Inherited from

[`ViewBaseProps`](ViewBaseProps.md).[`data-testid`](ViewBaseProps.md#data-testid)
