---
title: "ScrollAreaProps"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / ScrollAreaProps

# Interface: ScrollAreaProps

Defined in: web/src/components/Base/ScrollArea/ScrollArea.tsx:10

## Extends

- [`BaseComponentProps`](BaseComponentProps.md)

## Properties

### maxHeight?

> `optional` **maxHeight**: `string` \| `number`

Defined in: web/src/components/Base/ScrollArea/ScrollArea.tsx:15

Maximum height before scrolling

***

### maxWidth?

> `optional` **maxWidth**: `string` \| `number`

Defined in: web/src/components/Base/ScrollArea/ScrollArea.tsx:20

Maximum width before scrolling

***

### horizontal?

> `optional` **horizontal**: `boolean`

Defined in: web/src/components/Base/ScrollArea/ScrollArea.tsx:25

Whether to show horizontal scrollbar

***

### vertical?

> `optional` **vertical**: `boolean`

Defined in: web/src/components/Base/ScrollArea/ScrollArea.tsx:30

Whether to show vertical scrollbar

***

### scrollbarSize?

> `optional` **scrollbarSize**: `"sm"` \| `"md"` \| `"lg"`

Defined in: web/src/components/Base/ScrollArea/ScrollArea.tsx:35

Scrollbar size

***

### hideScrollbars?

> `optional` **hideScrollbars**: `boolean`

Defined in: web/src/components/Base/ScrollArea/ScrollArea.tsx:40

Whether to hide scrollbars when not scrolling

***

### native?

> `optional` **native**: `boolean`

Defined in: web/src/components/Base/ScrollArea/ScrollArea.tsx:45

Whether to use native scrollbars

***

### onScroll()?

> `optional` **onScroll**: (`event`) => `void`

Defined in: web/src/components/Base/ScrollArea/ScrollArea.tsx:50

Scroll event handler

#### Parameters

##### event

`UIEvent`\<`HTMLDivElement`\>

#### Returns

`void`

***

### as?

> `optional` **as**: keyof IntrinsicElements

Defined in: web/src/components/Base/ScrollArea/ScrollArea.tsx:55

HTML element to render as

***

### className?

> `optional` **className**: `string`

Defined in: web/src/constants/defaults/commonTypes.ts:13

Additional CSS class names to apply to the component

#### Inherited from

[`BaseComponentProps`](BaseComponentProps.md).[`className`](BaseComponentProps.md#classname)

***

### style?

> `optional` **style**: `CSSProperties`

Defined in: web/src/constants/defaults/commonTypes.ts:18

Inline styles to apply to the component

#### Inherited from

[`BaseComponentProps`](BaseComponentProps.md).[`style`](BaseComponentProps.md#style)

***

### $ref?

> `optional` **$ref**: `null` \| `RefObject`\<`HTMLElement`\>

Defined in: web/src/constants/defaults/commonTypes.ts:24

Custom ref prop to avoid forwardRef usage
This provides a cleaner API while maintaining ref functionality

#### Inherited from

[`BaseComponentProps`](BaseComponentProps.md).[`$ref`](BaseComponentProps.md#ref)

***

### children?

> `optional` **children**: `ReactNode`

Defined in: web/src/constants/defaults/commonTypes.ts:29

Child elements to render inside the component

#### Inherited from

[`BaseComponentProps`](BaseComponentProps.md).[`children`](BaseComponentProps.md#children)

***

### data-testid?

> `optional` **data-testid**: `string`

Defined in: web/src/constants/defaults/commonTypes.ts:34

Test ID for testing purposes

#### Inherited from

[`BaseComponentProps`](BaseComponentProps.md).[`data-testid`](BaseComponentProps.md#data-testid)
