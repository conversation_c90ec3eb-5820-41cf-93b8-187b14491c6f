---
title: "TransitionProps"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / TransitionProps

# Interface: TransitionProps

Defined in: web/src/components/Base/Transition/Transition.tsx:10

## Properties

### children

> **children**: `ReactNode`

Defined in: web/src/components/Base/Transition/Transition.tsx:15

The content to animate

***

### show

> **show**: `boolean`

Defined in: web/src/components/Base/Transition/Transition.tsx:20

Whether the transition is active/visible

***

### duration?

> `optional` **duration**: `number`

Defined in: web/src/components/Base/Transition/Transition.tsx:25

Duration of the transition in milliseconds

***

### properties?

> `optional` **properties**: `string`[]

Defined in: web/src/components/Base/Transition/Transition.tsx:30

CSS properties to animate

***

### easing?

> `optional` **easing**: `string`

Defined in: web/src/components/Base/Transition/Transition.tsx:35

Easing function for the transition

***

### enterFrom?

> `optional` **enterFrom**: `CSSProperties`

Defined in: web/src/components/Base/Transition/Transition.tsx:40

Styles to apply when entering

***

### enterTo?

> `optional` **enterTo**: `CSSProperties`

Defined in: web/src/components/Base/Transition/Transition.tsx:45

Styles to apply when entered

***

### leaveFrom?

> `optional` **leaveFrom**: `CSSProperties`

Defined in: web/src/components/Base/Transition/Transition.tsx:50

Styles to apply when leaving

***

### leaveTo?

> `optional` **leaveTo**: `CSSProperties`

Defined in: web/src/components/Base/Transition/Transition.tsx:55

Styles to apply when left

***

### onTransitionStart()?

> `optional` **onTransitionStart**: () => `void`

Defined in: web/src/components/Base/Transition/Transition.tsx:60

Callback when transition starts

#### Returns

`void`

***

### onTransitionEnd()?

> `optional` **onTransitionEnd**: () => `void`

Defined in: web/src/components/Base/Transition/Transition.tsx:65

Callback when transition ends

#### Returns

`void`

***

### unmountOnExit?

> `optional` **unmountOnExit**: `boolean`

Defined in: web/src/components/Base/Transition/Transition.tsx:70

Whether to unmount the component when not showing

***

### className?

> `optional` **className**: `string`

Defined in: web/src/components/Base/Transition/Transition.tsx:75

Additional CSS class names

***

### data-testid?

> `optional` **data-testid**: `string`

Defined in: web/src/components/Base/Transition/Transition.tsx:80

Test ID for testing purposes
