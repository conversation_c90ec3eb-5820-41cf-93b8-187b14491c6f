---
title: "ThemeTokens"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / ThemeTokens

# Interface: ThemeTokens

Defined in: web/src/constants/themeTokens.ts:193

## Properties

### fonts

> **fonts**: `object`

Defined in: web/src/constants/themeTokens.ts:195

#### sans

> `readonly` **sans**: "system-ui, -apple-system, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"" = `'system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"'`

#### serif

> `readonly` **serif**: "ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif" = `'ui-serif, Georgia, Cambria, "Times New Roman", Times, serif'`

#### mono

> `readonly` **mono**: "ui-monospace, SFMono-Regular, \"SF Mono\", Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace" = `'ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace'`

***

### fontSizes

> **fontSizes**: `Record`\<`string`, \[`string`, `string`\]\>

Defined in: web/src/constants/themeTokens.ts:196

***

### fontWeights

> **fontWeights**: `object`

Defined in: web/src/constants/themeTokens.ts:197

#### thin

> `readonly` **thin**: `"100"` = `'100'`

#### extralight

> `readonly` **extralight**: `"200"` = `'200'`

#### light

> `readonly` **light**: `"300"` = `'300'`

#### normal

> `readonly` **normal**: `"400"` = `'400'`

#### medium

> `readonly` **medium**: `"500"` = `'500'`

#### semibold

> `readonly` **semibold**: `"600"` = `'600'`

#### bold

> `readonly` **bold**: `"700"` = `'700'`

#### extrabold

> `readonly` **extrabold**: `"800"` = `'800'`

#### black

> `readonly` **black**: `"900"` = `'900'`

***

### lineHeights

> **lineHeights**: `object`

Defined in: web/src/constants/themeTokens.ts:198

#### none

> `readonly` **none**: `"1"` = `'1'`

#### tight

> `readonly` **tight**: `"1.25"` = `'1.25'`

#### snug

> `readonly` **snug**: `"1.375"` = `'1.375'`

#### normal

> `readonly` **normal**: `"1.5"` = `'1.5'`

#### relaxed

> `readonly` **relaxed**: `"1.625"` = `'1.625'`

#### loose

> `readonly` **loose**: `"2"` = `'2'`

#### 3

> `readonly` **3**: `".75rem"` = `'.75rem'`

#### 4

> `readonly` **4**: `"1rem"` = `'1rem'`

#### 5

> `readonly` **5**: `"1.25rem"` = `'1.25rem'`

#### 6

> `readonly` **6**: `"1.5rem"` = `'1.5rem'`

#### 7

> `readonly` **7**: `"1.75rem"` = `'1.75rem'`

#### 8

> `readonly` **8**: `"2rem"` = `'2rem'`

#### 9

> `readonly` **9**: `"2.25rem"` = `'2.25rem'`

#### 10

> `readonly` **10**: `"2.5rem"` = `'2.5rem'`

***

### letterSpacings

> **letterSpacings**: `object`

Defined in: web/src/constants/themeTokens.ts:199

#### tighter

> `readonly` **tighter**: `"-0.05em"` = `'-0.05em'`

#### tight

> `readonly` **tight**: `"-0.025em"` = `'-0.025em'`

#### normal

> `readonly` **normal**: `"0em"` = `'0em'`

#### wide

> `readonly` **wide**: `"0.025em"` = `'0.025em'`

#### wider

> `readonly` **wider**: `"0.05em"` = `'0.05em'`

#### widest

> `readonly` **widest**: `"0.1em"` = `'0.1em'`

***

### space

> **space**: `Record`\<`string`, `string`\>

Defined in: web/src/constants/themeTokens.ts:200

***

### sizes

> **sizes**: `object`

Defined in: web/src/constants/themeTokens.ts:201

#### auto

> `readonly` **auto**: `"auto"` = `'auto'`

#### 1/2

> `readonly` **1/2**: `"50%"` = `'50%'`

#### 1/3

> `readonly` **1/3**: `"33.333333%"` = `'33.333333%'`

#### 2/3

> `readonly` **2/3**: `"66.666667%"` = `'66.666667%'`

#### 1/4

> `readonly` **1/4**: `"25%"` = `'25%'`

#### 2/4

> `readonly` **2/4**: `"50%"` = `'50%'`

#### 3/4

> `readonly` **3/4**: `"75%"` = `'75%'`

#### 1/5

> `readonly` **1/5**: `"20%"` = `'20%'`

#### 2/5

> `readonly` **2/5**: `"40%"` = `'40%'`

#### 3/5

> `readonly` **3/5**: `"60%"` = `'60%'`

#### 4/5

> `readonly` **4/5**: `"80%"` = `'80%'`

#### 1/6

> `readonly` **1/6**: `"16.666667%"` = `'16.666667%'`

#### 2/6

> `readonly` **2/6**: `"33.333333%"` = `'33.333333%'`

#### 3/6

> `readonly` **3/6**: `"50%"` = `'50%'`

#### 4/6

> `readonly` **4/6**: `"66.666667%"` = `'66.666667%'`

#### 5/6

> `readonly` **5/6**: `"83.333333%"` = `'83.333333%'`

#### 1/12

> `readonly` **1/12**: `"8.333333%"` = `'8.333333%'`

#### 2/12

> `readonly` **2/12**: `"16.666667%"` = `'16.666667%'`

#### 3/12

> `readonly` **3/12**: `"25%"` = `'25%'`

#### 4/12

> `readonly` **4/12**: `"33.333333%"` = `'33.333333%'`

#### 5/12

> `readonly` **5/12**: `"41.666667%"` = `'41.666667%'`

#### 6/12

> `readonly` **6/12**: `"50%"` = `'50%'`

#### 7/12

> `readonly` **7/12**: `"58.333333%"` = `'58.333333%'`

#### 8/12

> `readonly` **8/12**: `"66.666667%"` = `'66.666667%'`

#### 9/12

> `readonly` **9/12**: `"75%"` = `'75%'`

#### 10/12

> `readonly` **10/12**: `"83.333333%"` = `'83.333333%'`

#### 11/12

> `readonly` **11/12**: `"91.666667%"` = `'91.666667%'`

#### full

> `readonly` **full**: `"100%"` = `'100%'`

#### screen

> `readonly` **screen**: `"100vh"` = `'100vh'`

#### min

> `readonly` **min**: `"min-content"` = `'min-content'`

#### max

> `readonly` **max**: `"max-content"` = `'max-content'`

#### fit

> `readonly` **fit**: `"fit-content"` = `'fit-content'`

#### prose

> `readonly` **prose**: `"65ch"` = `'65ch'`

#### xs

> `readonly` **xs**: `"20rem"` = `'20rem'`

#### sm

> `readonly` **sm**: `"24rem"` = `'24rem'`

#### md

> `readonly` **md**: `"28rem"` = `'28rem'`

#### lg

> `readonly` **lg**: `"32rem"` = `'32rem'`

#### xl

> `readonly` **xl**: `"36rem"` = `'36rem'`

#### 2xl

> `readonly` **2xl**: `"42rem"` = `'42rem'`

#### 3xl

> `readonly` **3xl**: `"48rem"` = `'48rem'`

#### 4xl

> `readonly` **4xl**: `"56rem"` = `'56rem'`

#### 5xl

> `readonly` **5xl**: `"64rem"` = `'64rem'`

#### 6xl

> `readonly` **6xl**: `"72rem"` = `'72rem'`

#### 7xl

> `readonly` **7xl**: `"80rem"` = `'80rem'`

***

### radii

> **radii**: `Record`\<`string`, `string`\>

Defined in: web/src/constants/themeTokens.ts:202

***

### borderWeights

> **borderWeights**: `object`

Defined in: web/src/constants/themeTokens.ts:203

#### 0

> `readonly` **0**: `"0px"` = `'0px'`

#### 1

> `readonly` **1**: `"1px"` = `'1px'`

#### 2

> `readonly` **2**: `"2px"` = `'2px'`

#### 4

> `readonly` **4**: `"4px"` = `'4px'`

#### 8

> `readonly` **8**: `"8px"` = `'8px'`

***

### shadows

> **shadows**: `Record`\<`string`, `string`\>

Defined in: web/src/constants/themeTokens.ts:204

***

### zIndices

> **zIndices**: `Record`\<`string`, `number`\>

Defined in: web/src/constants/themeTokens.ts:205

***

### transitions

> **transitions**: `Record`\<`string`, `string`\>

Defined in: web/src/constants/themeTokens.ts:206

***

### durations

> **durations**: `Record`\<`string`, `string`\>

Defined in: web/src/constants/themeTokens.ts:207

***

### easings

> **easings**: `Record`\<`string`, `string`\>

Defined in: web/src/constants/themeTokens.ts:208

***

### breakpoints

> **breakpoints**: `object`

Defined in: web/src/constants/themeTokens.ts:209

#### xs

> `readonly` **xs**: `"475px"` = `'475px'`

#### sm

> `readonly` **sm**: `"640px"` = `'640px'`

#### md

> `readonly` **md**: `"768px"` = `'768px'`

#### lg

> `readonly` **lg**: `"1024px"` = `'1024px'`

#### xl

> `readonly` **xl**: `"1280px"` = `'1280px'`

#### 2xl

> `readonly` **2xl**: `"1536px"` = `'1536px'`

***

### mediaQueries

> **mediaQueries**: `object`

Defined in: web/src/constants/themeTokens.ts:210

#### xs

> `readonly` **xs**: `"(min-width: 475px)"`

#### sm

> `readonly` **sm**: `"(min-width: 640px)"`

#### md

> `readonly` **md**: `"(min-width: 768px)"`

#### lg

> `readonly` **lg**: `"(min-width: 1024px)"`

#### xl

> `readonly` **xl**: `"(min-width: 1280px)"`

#### 2xl

> `readonly` **2xl**: `"(min-width: 1536px)"`

#### max-xs

> `readonly` **max-xs**: `"(max-width: 475px)"`

#### max-sm

> `readonly` **max-sm**: `"(max-width: 640px)"`

#### max-md

> `readonly` **max-md**: `"(max-width: 768px)"`

#### max-lg

> `readonly` **max-lg**: `"(max-width: 1024px)"`

#### max-xl

> `readonly` **max-xl**: `"(max-width: 1280px)"`

#### max-2xl

> `readonly` **max-2xl**: `"(max-width: 1536px)"`

#### portrait

> `readonly` **portrait**: `"(orientation: portrait)"` = `'(orientation: portrait)'`

#### landscape

> `readonly` **landscape**: `"(orientation: landscape)"` = `'(orientation: landscape)'`

#### prefers-dark

> `readonly` **prefers-dark**: `"(prefers-color-scheme: dark)"` = `'(prefers-color-scheme: dark)'`

#### prefers-light

> `readonly` **prefers-light**: `"(prefers-color-scheme: light)"` = `'(prefers-color-scheme: light)'`

#### prefers-reduced-motion

> `readonly` **prefers-reduced-motion**: `"(prefers-reduced-motion: reduce)"` = `'(prefers-reduced-motion: reduce)'`

#### prefers-contrast-high

> `readonly` **prefers-contrast-high**: `"(prefers-contrast: high)"` = `'(prefers-contrast: high)'`

#### prefers-contrast-low

> `readonly` **prefers-contrast-low**: `"(prefers-contrast: low)"` = `'(prefers-contrast: low)'`

#### hover-hover

> `readonly` **hover-hover**: `"(hover: hover)"` = `'(hover: hover)'`

#### hover-none

> `readonly` **hover-none**: `"(hover: none)"` = `'(hover: none)'`

#### pointer-coarse

> `readonly` **pointer-coarse**: `"(pointer: coarse)"` = `'(pointer: coarse)'`

#### pointer-fine

> `readonly` **pointer-fine**: `"(pointer: fine)"` = `'(pointer: fine)'`
