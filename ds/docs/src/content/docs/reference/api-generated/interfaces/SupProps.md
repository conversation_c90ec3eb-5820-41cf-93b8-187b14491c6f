---
title: "SupProps"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / SupProps

# Interface: SupProps

Defined in: web/src/components/Core.Text/Text.types.ts:161

Props for Text.Sup component

## Extends

- [`BaseTextProps`](BaseTextProps.md)

## Properties

### children

> **children**: `ReactNode`

Defined in: web/src/components/Core.Text/Text.types.ts:46

#### Inherited from

[`BaseTextProps`](BaseTextProps.md).[`children`](BaseTextProps.md#children)

***

### $ref?

> `optional` **$ref**: `null` \| `RefObject`\<`any`\>

Defined in: web/src/components/Core.Text/Text.types.ts:47

#### Inherited from

[`BaseTextProps`](BaseTextProps.md).[`$ref`](BaseTextProps.md#ref)

***

### className?

> `optional` **className**: `string`

Defined in: web/src/components/Core.Text/Text.types.ts:48

#### Inherited from

[`BaseTextProps`](BaseTextProps.md).[`className`](BaseTextProps.md#classname)

***

### style?

> `optional` **style**: `CSSProperties`

Defined in: web/src/components/Core.Text/Text.types.ts:49

#### Inherited from

[`BaseTextProps`](BaseTextProps.md).[`style`](BaseTextProps.md#style)

***

### bold?

> `optional` **bold**: `true` \| `100` \| `200` \| `300` \| `400` \| `500` \| `600` \| `700` \| `800` \| `900`

Defined in: web/src/components/Core.Text/Text.types.ts:52

#### Inherited from

[`BaseTextProps`](BaseTextProps.md).[`bold`](BaseTextProps.md#bold)

***

### italic?

> `optional` **italic**: `boolean`

Defined in: web/src/components/Core.Text/Text.types.ts:53

#### Inherited from

[`BaseTextProps`](BaseTextProps.md).[`italic`](BaseTextProps.md#italic)

***

### decoration?

> `optional` **decoration**: `"underline"` \| `"overline"` \| `"line-through"`

Defined in: web/src/components/Core.Text/Text.types.ts:54

#### Inherited from

[`BaseTextProps`](BaseTextProps.md).[`decoration`](BaseTextProps.md#decoration)

***

### transform?

> `optional` **transform**: `"capitalize"` \| `"uppercase"` \| `"lowercase"`

Defined in: web/src/components/Core.Text/Text.types.ts:55

#### Inherited from

[`BaseTextProps`](BaseTextProps.md).[`transform`](BaseTextProps.md#transform)

***

### color?

> `optional` **color**: `TextColorVariant`

Defined in: web/src/components/Core.Text/Text.types.ts:58

#### Inherited from

[`BaseTextProps`](BaseTextProps.md).[`color`](BaseTextProps.md#color)

***

### aria-label?

> `optional` **aria-label**: `string`

Defined in: web/src/components/Core.Text/Text.types.ts:61

#### Inherited from

[`BaseTextProps`](BaseTextProps.md).[`aria-label`](BaseTextProps.md#aria-label)

***

### aria-describedby?

> `optional` **aria-describedby**: `string`

Defined in: web/src/components/Core.Text/Text.types.ts:62

#### Inherited from

[`BaseTextProps`](BaseTextProps.md).[`aria-describedby`](BaseTextProps.md#aria-describedby)

***

### id?

> `optional` **id**: `string`

Defined in: web/src/components/Core.Text/Text.types.ts:63

#### Inherited from

[`BaseTextProps`](BaseTextProps.md).[`id`](BaseTextProps.md#id)

***

### role?

> `optional` **role**: `string`

Defined in: web/src/components/Core.Text/Text.types.ts:64

#### Inherited from

[`BaseTextProps`](BaseTextProps.md).[`role`](BaseTextProps.md#role)

***

### tabIndex?

> `optional` **tabIndex**: `number`

Defined in: web/src/components/Core.Text/Text.types.ts:65

#### Inherited from

[`BaseTextProps`](BaseTextProps.md).[`tabIndex`](BaseTextProps.md#tabindex)
