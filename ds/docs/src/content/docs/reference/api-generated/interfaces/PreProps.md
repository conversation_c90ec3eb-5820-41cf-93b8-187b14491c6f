---
title: "PreProps"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / PreProps

# Interface: PreProps

Defined in: web/src/components/Base/Pre/Pre.tsx:10

## Extends

- [`BaseComponentProps`](BaseComponentProps.md)

## Properties

### preserveWhitespace?

> `optional` **preserveWhitespace**: `boolean`

Defined in: web/src/components/Base/Pre/Pre.tsx:15

Whether to preserve whitespace and line breaks

***

### scrollable?

> `optional` **scrollable**: `boolean`

Defined in: web/src/components/Base/Pre/Pre.tsx:20

Whether to allow horizontal scrolling

***

### wrap?

> `optional` **wrap**: `boolean`

Defined in: web/src/components/Base/Pre/Pre.tsx:25

Whether to wrap long lines

***

### fontFamily?

> `optional` **fontFamily**: `string`

Defined in: web/src/components/Base/Pre/Pre.tsx:30

Font family for the preformatted text

***

### fontSize?

> `optional` **fontSize**: `string`

Defined in: web/src/components/Base/Pre/Pre.tsx:35

Font size for the preformatted text

***

### lineHeight?

> `optional` **lineHeight**: `string` \| `number`

Defined in: web/src/components/Base/Pre/Pre.tsx:40

Line height for the preformatted text

***

### backgroundColor?

> `optional` **backgroundColor**: `string`

Defined in: web/src/components/Base/Pre/Pre.tsx:45

Background color

***

### color?

> `optional` **color**: `string`

Defined in: web/src/components/Base/Pre/Pre.tsx:50

Text color

***

### padding?

> `optional` **padding**: `string`

Defined in: web/src/components/Base/Pre/Pre.tsx:55

Padding around the content

***

### borderRadius?

> `optional` **borderRadius**: `string`

Defined in: web/src/components/Base/Pre/Pre.tsx:60

Border radius

***

### maxHeight?

> `optional` **maxHeight**: `string`

Defined in: web/src/components/Base/Pre/Pre.tsx:65

Maximum height before scrolling

***

### as?

> `optional` **as**: `"code"` \| `"div"` \| `"pre"`

Defined in: web/src/components/Base/Pre/Pre.tsx:70

HTML element to render as

***

### className?

> `optional` **className**: `string`

Defined in: web/src/constants/defaults/commonTypes.ts:13

Additional CSS class names to apply to the component

#### Inherited from

[`BaseComponentProps`](BaseComponentProps.md).[`className`](BaseComponentProps.md#classname)

***

### style?

> `optional` **style**: `CSSProperties`

Defined in: web/src/constants/defaults/commonTypes.ts:18

Inline styles to apply to the component

#### Inherited from

[`BaseComponentProps`](BaseComponentProps.md).[`style`](BaseComponentProps.md#style)

***

### $ref?

> `optional` **$ref**: `null` \| `RefObject`\<`HTMLElement`\>

Defined in: web/src/constants/defaults/commonTypes.ts:24

Custom ref prop to avoid forwardRef usage
This provides a cleaner API while maintaining ref functionality

#### Inherited from

[`BaseComponentProps`](BaseComponentProps.md).[`$ref`](BaseComponentProps.md#ref)

***

### children?

> `optional` **children**: `ReactNode`

Defined in: web/src/constants/defaults/commonTypes.ts:29

Child elements to render inside the component

#### Inherited from

[`BaseComponentProps`](BaseComponentProps.md).[`children`](BaseComponentProps.md#children)

***

### data-testid?

> `optional` **data-testid**: `string`

Defined in: web/src/constants/defaults/commonTypes.ts:34

Test ID for testing purposes

#### Inherited from

[`BaseComponentProps`](BaseComponentProps.md).[`data-testid`](BaseComponentProps.md#data-testid)
