---
title: "ViewBaseProps"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / ViewBaseProps

# Interface: ViewBaseProps

Defined in: web/src/components/Core.View/View.types.ts:10

Base props for all View components

## Extends

- [`BaseComponentProps`](BaseComponentProps.md)

## Extended by

- [`ViewProps`](ViewProps.md)
- [`ContainerProps`](ContainerProps.md)
- [`ScrollbarProps`](ScrollbarProps.md)
- [`DividerProps`](DividerProps.md)
- [`SpacerProps`](SpacerProps.md)
- [`DeviceProps`](DeviceProps.md)
- [`RowProps`](RowProps.md)
- [`ColumnProps`](ColumnProps.md)

## Properties

### as?

> `optional` **as**: keyof IntrinsicElements

Defined in: web/src/components/Core.View/View.types.ts:15

HTML element to render as

***

### className?

> `optional` **className**: `string`

Defined in: web/src/constants/defaults/commonTypes.ts:13

Additional CSS class names to apply to the component

#### Inherited from

[`BaseComponentProps`](BaseComponentProps.md).[`className`](BaseComponentProps.md#classname)

***

### style?

> `optional` **style**: `CSSProperties`

Defined in: web/src/constants/defaults/commonTypes.ts:18

Inline styles to apply to the component

#### Inherited from

[`BaseComponentProps`](BaseComponentProps.md).[`style`](BaseComponentProps.md#style)

***

### $ref?

> `optional` **$ref**: `null` \| `RefObject`\<`HTMLElement`\>

Defined in: web/src/constants/defaults/commonTypes.ts:24

Custom ref prop to avoid forwardRef usage
This provides a cleaner API while maintaining ref functionality

#### Inherited from

[`BaseComponentProps`](BaseComponentProps.md).[`$ref`](BaseComponentProps.md#ref)

***

### children?

> `optional` **children**: `ReactNode`

Defined in: web/src/constants/defaults/commonTypes.ts:29

Child elements to render inside the component

#### Inherited from

[`BaseComponentProps`](BaseComponentProps.md).[`children`](BaseComponentProps.md#children)

***

### data-testid?

> `optional` **data-testid**: `string`

Defined in: web/src/constants/defaults/commonTypes.ts:34

Test ID for testing purposes

#### Inherited from

[`BaseComponentProps`](BaseComponentProps.md).[`data-testid`](BaseComponentProps.md#data-testid)
