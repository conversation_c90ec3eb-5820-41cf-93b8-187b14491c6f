---
title: "SpacerProps"
description: "API documentation"
---

[**@miss-ui/web**](../README.md)

***

[@miss-ui/web](../README.md) / SpacerProps

# Interface: SpacerProps

Defined in: web/src/components/Core.View/View.types.ts:140

Props for the Spacer component

## Extends

- [`ViewBaseProps`](ViewBaseProps.md)

## Properties

### as?

> `optional` **as**: keyof IntrinsicElements

Defined in: web/src/components/Core.View/View.types.ts:15

HTML element to render as

#### Inherited from

[`ViewBaseProps`](ViewBaseProps.md).[`as`](ViewBaseProps.md#as)

***

### size?

> `optional` **size**: `string` \| `number`

Defined in: web/src/components/Core.View/View.types.ts:145

Size of the spacer

***

### direction?

> `optional` **direction**: `"horizontal"` \| `"vertical"`

Defined in: web/src/components/Core.View/View.types.ts:150

Direction of the spacer

***

### grow?

> `optional` **grow**: `boolean`

Defined in: web/src/components/Core.View/View.types.ts:155

Whether the spacer should grow to fill available space

***

### className?

> `optional` **className**: `string`

Defined in: web/src/constants/defaults/commonTypes.ts:13

Additional CSS class names to apply to the component

#### Inherited from

[`ViewBaseProps`](ViewBaseProps.md).[`className`](ViewBaseProps.md#classname)

***

### style?

> `optional` **style**: `CSSProperties`

Defined in: web/src/constants/defaults/commonTypes.ts:18

Inline styles to apply to the component

#### Inherited from

[`ViewBaseProps`](ViewBaseProps.md).[`style`](ViewBaseProps.md#style)

***

### $ref?

> `optional` **$ref**: `null` \| `RefObject`\<`HTMLElement`\>

Defined in: web/src/constants/defaults/commonTypes.ts:24

Custom ref prop to avoid forwardRef usage
This provides a cleaner API while maintaining ref functionality

#### Inherited from

[`ViewBaseProps`](ViewBaseProps.md).[`$ref`](ViewBaseProps.md#ref)

***

### children?

> `optional` **children**: `ReactNode`

Defined in: web/src/constants/defaults/commonTypes.ts:29

Child elements to render inside the component

#### Inherited from

[`ViewBaseProps`](ViewBaseProps.md).[`children`](ViewBaseProps.md#children)

***

### data-testid?

> `optional` **data-testid**: `string`

Defined in: web/src/constants/defaults/commonTypes.ts:34

Test ID for testing purposes

#### Inherited from

[`ViewBaseProps`](ViewBaseProps.md).[`data-testid`](ViewBaseProps.md#data-testid)
