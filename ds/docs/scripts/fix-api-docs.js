#!/usr/bin/env node

import { readdir, readFile, writeFile } from 'fs/promises';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const API_DOCS_DIR = join(__dirname, '../src/content/docs/reference/api-generated');

/**
 * Extract title from markdown content
 */
function extractTitle(content) {
	// Look for the main heading (# Variable: BaseField, # Function: applyTheme, etc.)
	const titleMatch = content.match(/^# (?:Variable|Function|Interface|Type Alias|Class|Enum): (.+)$/m);
	if (titleMatch) {
		return titleMatch[1];
	}

	// Fallback to any # heading
	const headingMatch = content.match(/^# (.+)$/m);
	if (headingMatch) {
		return headingMatch[1];
	}

	return 'API Documentation';
}

/**
 * Escape YAML string value
 */
function escapeYamlString(str) {
	// Replace backslashes first, then quotes
	return str.replace(/\\/g, '\\\\').replace(/"/g, '\\"');
}

/**
 * Extract description from markdown content
 */
function extractDescription(content) {
	// For API docs, just use a simple generic description
	// The complex parsing was causing YAML issues
	return 'API documentation';
}

/**
 * Add frontmatter to a markdown file
 */
function addFrontmatter(content, title, description) {
	// Check if frontmatter already exists
	if (content.startsWith('---')) {
		return content;
	}

	// Escape the title and description for YAML
	const escapedTitle = escapeYamlString(title);
	const escapedDescription = escapeYamlString(description);

	const frontmatter = `---
title: "${escapedTitle}"
description: "${escapedDescription}"
---

`;

	return frontmatter + content;
}

/**
 * Process a single markdown file
 */
async function processFile(filePath) {
	try {
		const content = await readFile(filePath, 'utf-8');
		const title = extractTitle(content);
		const description = extractDescription(content);

		const updatedContent = addFrontmatter(content, title, description);

		if (updatedContent !== content) {
			await writeFile(filePath, updatedContent, 'utf-8');
			console.log(`✓ Updated: ${filePath}`);
		}
	} catch (error) {
		console.error(`✗ Error processing ${filePath}:`, error.message);
	}
}

/**
 * Recursively process all markdown files in a directory
 */
async function processDirectory(dirPath) {
	try {
		const entries = await readdir(dirPath, { withFileTypes: true });

		for (const entry of entries) {
			const fullPath = join(dirPath, entry.name);

			if (entry.isDirectory()) {
				await processDirectory(fullPath);
			} else if (entry.isFile() && entry.name.endsWith('.md')) {
				await processFile(fullPath);
			}
		}
	} catch (error) {
		console.error(`✗ Error processing directory ${dirPath}:`, error.message);
	}
}

/**
 * Main function
 */
async function main() {
	console.log('🔧 Fixing API documentation frontmatter...');

	try {
		await processDirectory(API_DOCS_DIR);
		console.log('✅ API documentation frontmatter fixed successfully!');
	} catch (error) {
		console.error('❌ Failed to fix API documentation:', error.message);
		process.exit(1);
	}
}

main();
