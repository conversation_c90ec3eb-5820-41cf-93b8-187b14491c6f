/**
 * Vite Configuration for CSS Optimization
 * This configuration focuses on optimal CSS output with Vanilla Extract
 */

import type { UserConfig } from 'vite';
import { defineConfig } from 'vite';
import { resolve } from 'path';
import { vanillaExtractPlugin } from '@vanilla-extract/vite-plugin';

export default defineConfig({
	plugins: [
		vanillaExtractPlugin({
			identifiers: process.env.NODE_ENV === 'production' ? 'short' : 'debug',
		}),
	],
	build: {
		lib: {
			entry: {
				// Main entry point
				index: resolve(__dirname, 'src/index.ts'),
				// Factory entry points for code splitting
				'factories-light': resolve(__dirname, 'src/factories-light.ts'),
				factories: resolve(__dirname, 'src/factories.ts'),
				'factories-heavy': resolve(__dirname, 'src/factories-heavy.ts'),
			},
			name: 'MissUIWeb',
			formats: ['es', 'cjs'],
			fileName: (format, entryName) => `${entryName}.${format}.js`,
		},
		rollupOptions: {
			external: [
				'react',
				'react-dom',
				'react/jsx-runtime',
				'@base-ui-components/react',
				// External heavy dependencies for Factory components
				'@lexical/code',
				'@lexical/history',
				'@lexical/html',
				'@lexical/link',
				'@lexical/list',
				'@lexical/markdown',
				'@lexical/plain-text',
				'@lexical/react',
				'@lexical/rich-text',
				'@lexical/selection',
				'@lexical/table',
				'@lexical/utils',
				'@vidstack/react',
				'lexical',
			],
			output: {
				globals: {
					'react': 'React',
					'react-dom': 'ReactDOM',
					'react/jsx-runtime': 'jsxRuntime',
					'@base-ui-components/react': 'BaseUI',
					'@lexical/code': 'LexicalCode',
					'@lexical/history': 'LexicalHistory',
					'@lexical/html': 'LexicalHTML',
					'@lexical/link': 'LexicalLink',
					'@lexical/list': 'LexicalList',
					'@lexical/markdown': 'LexicalMarkdown',
					'@lexical/plain-text': 'LexicalPlainText',
					'@lexical/react': 'LexicalReact',
					'@lexical/rich-text': 'LexicalRichText',
					'@lexical/selection': 'LexicalSelection',
					'@lexical/table': 'LexicalTable',
					'@lexical/utils': 'LexicalUtils',
					'@vidstack/react': 'VidstackReact',
					'lexical': 'Lexical',
				},
				exports: 'named',
				// Optimize output for better compression
				compact: true,
				// Enable advanced minification
				generatedCode: {
					constBindings: true,
					objectShorthand: true,
					arrowFunctions: true,
				},
				// Optimize for modern environments
				format: 'es',
				// Optimize asset handling
				assetFileNames: 'assets/[name]-[hash][extname]',
				chunkFileNames: 'chunks/[name]-[hash].js',
				entryFileNames: '[name].js',
			},
			// Enhanced tree-shaking configuration
			treeshake: {
				moduleSideEffects: (id) => {
					// Mark CSS files as having side effects
					if (id.includes('.css') || id.includes('.scss') || id.includes('.css.ts')) {
						return true;
					}
					// Mark specific modules as having side effects
					if (id.includes('polyfill') || id.includes('shim')) {
						return true;
					}
					// Mark theme files as having side effects (they register CSS variables)
					if (id.includes('theme') && (id.includes('lightTheme') || id.includes('darkTheme'))) {
						return true;
					}
					// Mark global styles as having side effects
					if (id.includes('globalStyles')) {
						return true;
					}
					return false;
				},
				propertyReadSideEffects: false,
				unknownGlobalSideEffects: false,
				// Enable pure annotations
				annotations: true,
				// Optimize function calls
				correctVarValueBeforeDeclaration: true,
				// Advanced tree-shaking options
				tryCatchDeoptimization: false,
			},
			// Enable advanced optimizations
			makeAbsoluteExternalsRelative: true,
			// Optimize module resolution
			preserveEntrySignatures: 'strict',
		},
		sourcemap: false,
		target: 'es2020',
		minify: 'esbuild',
		// Optimize CSS extraction with enhanced settings
		cssCodeSplit: true,
		cssMinify: true,
		// Enhanced build optimizations
		reportCompressedSize: true,
		chunkSizeWarningLimit: 500,
		// Optimize asset handling
		assetsInlineLimit: 4096,
		// CSS optimization specific settings
		cssTarget: 'es2020',
	},
	resolve: {
		alias: {
			'@': resolve(__dirname, './src'),
			'@/components': resolve(__dirname, './src/components'),
			'@/constants': resolve(__dirname, './src/constants'),
			'@/hooks': resolve(__dirname, './src/hooks'),
			'@/utils': resolve(__dirname, './src/utils'),
			'@/types': resolve(__dirname, './src/types'),
		},
	},
	esbuild: {
		target: 'es2020',
		// Enable tree-shaking for TypeScript
		treeShaking: true,
		// Optimize for production
		minifyIdentifiers: true,
		minifySyntax: true,
		minifyWhitespace: true,
		// Remove console logs in production
		drop: process.env.NODE_ENV === 'production' ? ['console', 'debugger'] : [],
		// Enable pure annotations for better tree-shaking
		pure: ['console.log', 'console.warn', 'console.error'],
		// CSS optimization
		legalComments: 'none',
	},
	// CSS-specific optimizations
	css: {
		devSourcemap: false,
		// Enhanced CSS processing
		preprocessorOptions: {
			// Add any CSS preprocessor options if needed
		},
		// CSS modules optimization
		modules: {
			localsConvention: 'camelCase',
			generateScopedName: process.env.NODE_ENV === 'production'
				? '[hash:base64:5]'
				: '[name]__[local]___[hash:base64:5]',
		},
		// PostCSS optimization
		postcss: {
			plugins: [
				// Add PostCSS plugins for additional CSS optimization if needed
			],
		},
	},
	// Optimize dependencies
	optimizeDeps: {
		include: [
			'react',
			'react-dom',
			'@base-ui-components/react',
		],
		exclude: [
			// Exclude heavy Factory dependencies from optimization
			'@lexical/code',
			'@lexical/history',
			'@lexical/html',
			'@lexical/link',
			'@lexical/list',
			'@lexical/markdown',
			'@lexical/plain-text',
			'@lexical/react',
			'@lexical/rich-text',
			'@lexical/selection',
			'@lexical/table',
			'@lexical/utils',
			'@vidstack/react',
			'lexical',
		],
	},
} satisfies UserConfig);
