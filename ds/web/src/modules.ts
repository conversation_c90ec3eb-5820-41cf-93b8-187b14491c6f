/**
 * Module Components Entry Point
 * Complex interactive patterns
 *
 * Usage: import { ModulePopover, ModuleTabs, ModuleSticky } from "@miss-ui/web/modules"
 */

// Module components from importer
export {
	ModuleTree,
	ModuleTabs,
	ModulePagination,
	ModuleDropdown,
} from './components/Module.importer';
export type {
	ModuleTreeProps,
	ModuleTreeItemData,
	ModuleTreeItemProps,
	ModuleTabsProps,
	ModuleTabItem,
	ModuleTabProps,
	ModuleTabPanelProps,
	ModulePaginationProps,
	ModulePaginationItem,
	ModulePaginationItemProps,
	ModuleDropdownProps,
	ModuleDropdownOption,
	ModuleDropdownOptionGroup,
} from './components/Module.importer';

// Module.Popover components
export {
	Popover as ModulePopover,
	PopoverTrigger as ModulePopoverTrigger,
	PopoverContent as ModulePopoverContent,
	Tooltip as ModuleTooltip,
} from './components/Module.Popover';
export type {
	PopoverProps as ModulePopoverProps,
	TooltipProps as ModuleTooltipProps,
	PopoverPlacement as ModulePopoverPlacement,
	PopoverSize as ModulePopoverSize,
	PopoverColor as ModulePopoverColor,
	PopoverVariant as ModulePopoverVariant,
	PopoverTriggerProps as ModulePopoverTriggerProps,
	PopoverContentProps as ModulePopoverContentProps,
	PopoverRootProps as ModulePopoverRootProps,
} from './components/Module.Popover';

// Module.Sticky components
export {
	Sticky as ModuleSticky,
} from './components/Module.Sticky';
export type {
	StickyProps as ModuleStickyProps,
	StickyState as ModuleStickyState,
} from './components/Module.Sticky';
