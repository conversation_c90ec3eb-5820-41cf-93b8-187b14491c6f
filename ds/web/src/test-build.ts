/**
 * Test build entry point for optimization testing
 * This file creates a minimal build to test optimization features
 */

// Simple exports to test tree-shaking
export const testFunction = () => {
	console.log('Test function for optimization');
};

export const unusedFunction = () => {
	console.log('This should be tree-shaken out');
};

// CSS-in-JS test
import { style } from '@vanilla-extract/css';

export const testStyles = style({
	color: 'red',
	backgroundColor: 'blue',
	padding: '10px',
});

export const unusedStyles = style({
	color: 'green',
	margin: '20px',
});

// Default export for main bundle
export default {
	testFunction,
	testStyles,
};
