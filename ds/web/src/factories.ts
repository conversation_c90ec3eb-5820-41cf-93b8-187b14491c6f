/**
 * Factory Components Entry Point
 * Separate entry for Factory components to enable code splitting
 * These components have medium-weight dependencies and can be loaded on demand
 * For heavy dependencies (Editor, Player), use factories-heavy.ts
 *
 * Bundle Size Target: <80KB gzipped
 * Dependencies: Medium-weight, suitable for on-demand loading
 * Tree-shaking: Optimized for selective imports
 */

// Layout Factory - Lightweight, can be included in main bundle
export {
	FactoryLayout,
	Layout as FactoryLayoutBase,
	LayoutHeader as FactoryLayoutHeader,
	LayoutMain as FactoryLayoutMain,
	LayoutSidebar as FactoryLayoutSidebar,
	LayoutContent as FactoryLayoutContent,
	LayoutFooter as FactoryLayoutFooter,
} from './components/Factory.Layout';
export type {
	LayoutProps as FactoryLayoutProps,
	LayoutHeaderProps as FactoryLayoutHeaderProps,
	LayoutMainProps as FactoryLayoutMainProps,
	LayoutSidebarProps as FactoryLayoutSidebarProps,
	LayoutContentProps as FactoryLayoutContentProps,
	LayoutFooterProps as FactoryLayoutFooterProps,
} from './components/Factory.Layout';

// Toast Factory - Lightweight, can be included in main bundle
export {
	Toast as FactoryToast,
	ToastProvider as FactoryToastProvider,
	ToastContainer as FactoryToastContainer,
	useToast as useFactoryToast,
} from './components/Factory.Toast';
export type {
	ToastProps as FactoryToastProps,
	ToastData as FactoryToastData,
	ToastType as FactoryToastType,
	ToastPosition as FactoryToastPosition,
	ToastProviderProps as FactoryToastProviderProps,
	ToastContextValue as FactoryToastContextValue,
	ToastContainerProps as FactoryToastContainerProps,
} from './components/Factory.Toast';

// Modal Factory - Medium weight, good candidate for code splitting
export {
	Modal as FactoryModal,
	ModalHeader as FactoryModalHeader,
	ModalBody as FactoryModalBody,
	ModalFooter as FactoryModalFooter,
	ModalBackdrop as FactoryModalBackdrop,
	ModalTrigger as FactoryModalTrigger,
	useModal as useFactoryModal,
	modalManager as factoryModalManager,
} from './components/Factory.Modal';
export type {
	ModalProps as FactoryModalProps,
	ModalHeaderProps as FactoryModalHeaderProps,
	ModalBodyProps as FactoryModalBodyProps,
	ModalFooterProps as FactoryModalFooterProps,
	ModalBackdropProps as FactoryModalBackdropProps,
	ModalTriggerProps as FactoryModalTriggerProps,
	ModalContextValue as FactoryModalContextValue,
	ModalManager as FactoryModalManager,
	ModalSize as FactoryModalSize,
	ModalPlacement as FactoryModalPlacement,
	ModalAnimation as FactoryModalAnimation,
	ModalBackdropBlur as FactoryModalBackdropBlur,
	ModalStackItem as FactoryModalStackItem,
} from './components/Factory.Modal';

// Form Factory - Medium weight
export {
	Form as FactoryForm,
	FormField as FactoryFormField,
	FormGroup as FactoryFormGroup,
	useForm as useFactoryForm,
	validateField as validateFactoryFormField,
} from './components/Factory.Form';
export type {
	FormProps as FactoryFormProps,
	FormFieldProps as FactoryFormFieldProps,
	FormGroupProps as FactoryFormGroupProps,
	FormFieldConfig as FactoryFormFieldConfig,
	FormGroupConfig as FactoryFormGroupConfig,
	FormValidationRule as FactoryFormValidationRule,
	FormValidationError as FactoryFormValidationError,
	FormSubmissionData as FactoryFormSubmissionData,
	FormFieldType as FactoryFormFieldType,
	FormFieldOption as FactoryFormFieldOption,
} from './components/Factory.Form';

// Search Factory - Medium weight
export {
	Search as FactorySearch,
	useSearchContext as useFactorySearchContext,
} from './components/Factory.Search';
export type {
	SearchProps as FactorySearchProps,
	SearchResult as FactorySearchResult,
	SearchSuggestion as FactorySearchSuggestion,
	SearchAPI as FactorySearchAPI,
	SearchConfig as FactorySearchConfig,
	SearchFilter as FactorySearchFilter,
	SearchSortOption as FactorySearchSortOption,
	SearchFunction as FactorySearchFunction,
	SuggestionFunction as FactorySuggestionFunction,
	FilterFunction as FactoryFilterFunction,
	SortFunction as FactorySortFunction,
	HighlightFunction as FactoryHighlightFunction,
	SearchContextValue as FactorySearchContextValue,
} from './components/Factory.Search';

// Select Factory - Medium weight
export {
	Select as FactorySelect,
} from './components/Factory.Select';
export type {
	SelectProps as FactorySelectProps,
	SelectOptionType as FactorySelectOption,
	SelectValue as FactorySelectValue,
	SelectMode as FactorySelectMode,
	SelectSize as FactorySelectSize,
	SelectPlacement as FactorySelectPlacement,
	SelectOptionGroup as FactorySelectOptionGroup,
	SelectFilterFunction as FactorySelectFilterFunction,
	SelectSortFunction as FactorySelectSortFunction,
	SelectVirtualConfig as FactorySelectVirtualConfig,
} from './components/Factory.Select';

// DataTable Factory - Heavy weight, should be code split
export {
	DataTable as FactoryDataTable,
	useDataTable as useFactoryDataTable,
} from './components/Factory.DataTable';
export type {
	DataTableProps as FactoryDataTableProps,
	DataTableColumn as FactoryDataTableColumn,
	DataTableRow as FactoryDataTableRow,
	DataTableAPI as FactoryDataTableAPI,
	DataTableState as FactoryDataTableState,
	DataTableSort as FactoryDataTableSort,
	DataTableFilter as FactoryDataTableFilter,
	DataTablePagination as FactoryDataTablePagination,
	DataTableSelection as FactoryDataTableSelection,
	DataTableVirtualization as FactoryDataTableVirtualization,
	DataTableContextValue as FactoryDataTableContextValue,
	DataTableFetchFunction as FactoryDataTableFetchFunction,
} from './components/Factory.DataTable';

// Stepper Factory - Medium weight
export {
	Stepper as FactoryStepper,
	StepHeader as FactoryStepHeader,
	StepContent as FactoryStepContent,
	StepProgress as FactoryStepProgress,
	StepNavigation as FactoryStepNavigation,
	useStepper as useFactoryStepper,
} from './components/Factory.Stepper';
export type {
	StepperProps as FactoryStepperProps,
	StepDefinition as FactoryStepDefinition,
	StepState as FactoryStepState,
	StepStatus as FactoryStepStatus,
	StepperOrientation as FactoryStepperOrientation,
	StepperVariant as FactoryStepperVariant,
	StepFlowType as FactoryStepFlowType,
	StepComponentProps as FactoryStepComponentProps,
	StepHeaderProps as FactoryStepHeaderProps,
	StepContentProps as FactoryStepContentProps,
	StepProgressProps as FactoryStepProgressProps,
	StepNavigationProps as FactoryStepNavigationProps,
	StepperContextValue as FactoryStepperContextValue,
	StepperManager as FactoryStepperManager,
} from './components/Factory.Stepper';

// Tabber Factory - Medium weight
export {
	Tabber as FactoryTabber,
	TabberTab as FactoryTabberTab,
	TabberPanel as FactoryTabberPanel,
	useTabber as useFactoryTabber,
} from './components/Factory.Tabber';
export type {
	TabberProps as FactoryTabberProps,
	TabberItem as FactoryTabberItem,
	TabberTabProps as FactoryTabberTabProps,
	TabberPanelProps as FactoryTabberPanelProps,
	TabberContextValue as FactoryTabberContextValue,
	TabberPersistence as FactoryTabberPersistence,
	TabberTemplate as FactoryTabberTemplate,
	TabberGroup as FactoryTabberGroup,
	TabberDragDrop as FactoryTabberDragDrop,
	TabberManager as FactoryTabberManager,
	TabberPersistedState as FactoryTabberPersistedState,
} from './components/Factory.Tabber';

// Kbar Factory - Medium weight
export {
	Kbar as FactoryKbar,
	KbarProvider as FactoryKbarProvider,
	KbarSearch as FactoryKbarSearch,
	KbarResults as FactoryKbarResults,
	KbarActionItem as FactoryKbarActionItem,
	useKbar as useFactoryKbar,
} from './components/Factory.Kbar';
export type {
	KbarProps as FactoryKbarProps,
	KbarProviderProps as FactoryKbarProviderProps,
	KbarSearchProps as FactoryKbarSearchProps,
	KbarResultsProps as FactoryKbarResultsProps,
	KbarActionItemProps as FactoryKbarActionItemProps,
	KbarAction as FactoryKbarAction,
	KbarConfig as FactoryKbarConfig,
	KbarState as FactoryKbarState,
	KbarShortcut as FactoryKbarShortcut,
	KbarSearchResult as FactoryKbarSearchResult,
	KbarContextValue as FactoryKbarContextValue,
	UseKbarReturn as FactoryUseKbarReturn,
	KbarCommandPriority as FactoryKbarCommandPriority,
	KbarActionType as FactoryKbarActionType,
} from './components/Factory.Kbar';
