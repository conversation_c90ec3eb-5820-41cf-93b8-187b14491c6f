/**
 * Core Components Entry Point
 * Document structure and basic layout components
 *
 * Usage: import { CoreDocument, CoreView, CoreText } from "@miss-ui/web/core"
 */

// Core.Document components
export * from './components/Core.Document/importer';

// Core.View components
export * from './components/Core.View/importer';

// Core.Text components (with explicit exports to avoid conflicts)
export { CoreText } from './components/Core.Text';
export type {
	TextProps,
	HeadingProps,
	LabelProps,
	AbbrProps,
	KbdProps,
	MarkProps,
	ParagraphProps,
	SubProps,
	SupProps,
	TimeProps,
	QuoteProps,
	BaseTextProps,
	TextFontWeight,
	textFontWeightTypes,
	TextColorVariant,
} from './components/Core.Text';
export type { LinkProps as TextLinkProps } from './components/Core.Text';
