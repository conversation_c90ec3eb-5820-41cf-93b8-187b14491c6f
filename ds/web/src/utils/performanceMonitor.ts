import type { ComponentType } from 'react';
import { createElement, useEffect, useRef, useState } from 'react';

/**
 * Performance metrics interface
 */
export interface PerformanceMetrics
{
	componentName: string;
	renderTime: number;
	mountTime: number;
	updateCount: number;
	lastRenderTimestamp: number;
	averageRenderTime: number;
	memoryUsage?: number;
}

/**
 * Performance monitoring configuration
 */
export interface PerformanceConfig
{
	enabled: boolean;
	sampleRate: number; // 0-1, percentage of renders to monitor
	logToConsole: boolean;
	trackMemory: boolean;
	onMetricsUpdate?: (metrics: PerformanceMetrics) => void;
}

/**
 * Global performance monitoring state
 */
class PerformanceMonitor
{
	private metrics = new Map<string, PerformanceMetrics>();
	private config: PerformanceConfig = {
		enabled: process.env.NODE_ENV === 'development',
		sampleRate: 0.1, // Monitor 10% of renders by default
		logToConsole: false,
		trackMemory: false,
	};

	/**
	 * Configure performance monitoring
	 */
	configure(config: Partial<PerformanceConfig>): void
	{
		this.config = { ...this.config, ...config };
	}

	/**
	 * Get current configuration
	 */
	getConfig(): PerformanceConfig
	{
		return { ...this.config };
	}

	/**
	 * Record render performance
	 */
	recordRender(componentName: string, renderTime: number): void
	{
		if (!this.config.enabled || Math.random() > this.config.sampleRate)
		{
			return;
		}

		const existing = this.metrics.get(componentName);
		const now = performance.now();

		const metrics: PerformanceMetrics = {
			componentName,
			renderTime,
			mountTime: existing?.mountTime ?? now,
			updateCount: (existing?.updateCount ?? 0) + 1,
			lastRenderTimestamp: now,
			averageRenderTime: existing
				? (existing.averageRenderTime * existing.updateCount + renderTime) / (existing.updateCount + 1)
				: renderTime,
			memoryUsage: this.config.trackMemory ? this.getMemoryUsage() : undefined,
		};

		this.metrics.set(componentName, metrics);

		if (this.config.logToConsole)
		{
			console.log(`[Performance] ${componentName}:`, {
				renderTime: `${renderTime.toFixed(2)}ms`,
				averageRenderTime: `${metrics.averageRenderTime.toFixed(2)}ms`,
				updateCount: metrics.updateCount,
			});
		}

		this.config.onMetricsUpdate?.(metrics);
	}

	/**
	 * Get metrics for a specific component
	 */
	getMetrics(componentName: string): PerformanceMetrics | undefined
	{
		return this.metrics.get(componentName);
	}

	/**
	 * Get all metrics
	 */
	getAllMetrics(): PerformanceMetrics[]
	{
		return Array.from(this.metrics.values());
	}

	/**
	 * Clear metrics
	 */
	clearMetrics(): void
	{
		this.metrics.clear();
	}

	/**
	 * Get memory usage (if supported)
	 */
	private getMemoryUsage(): number | undefined
	{
		if ('memory' in performance)
		{
			return (performance as any).memory.usedJSHeapSize;
		}
		return undefined;
	}

	/**
	 * Get performance report
	 */
	getReport(): {
		totalComponents: number;
		slowestComponent: PerformanceMetrics | null;
		averageRenderTime: number;
		totalRenders: number;
	}
	{
		const allMetrics = this.getAllMetrics();

		if (allMetrics.length === 0)
		{
			return {
				totalComponents: 0,
				slowestComponent: null,
				averageRenderTime: 0,
				totalRenders: 0,
			};
		}

		const slowestComponent = allMetrics.reduce((slowest, current) =>
			current.averageRenderTime > slowest.averageRenderTime ? current : slowest
		);

		const totalRenders = allMetrics.reduce((sum, metrics) => sum + metrics.updateCount, 0);
		const averageRenderTime = allMetrics.reduce((sum, metrics) =>
			sum + (metrics.averageRenderTime * metrics.updateCount), 0
		) / totalRenders;

		return {
			totalComponents: allMetrics.length,
			slowestComponent,
			averageRenderTime,
			totalRenders,
		};
	}
}

// Global instance
export const performanceMonitor = new PerformanceMonitor();

/**
 * Hook to monitor component performance
 */
export function usePerformanceMonitor(componentName: string)
{
	const renderStartTime = useRef<number>(0);
	const [renderCount, setRenderCount] = useState(0);

	// Record render start
	renderStartTime.current = performance.now();

	useEffect(() =>
	{
		// Record render end
		const renderTime = performance.now() - renderStartTime.current;
		performanceMonitor.recordRender(componentName, renderTime);
		setRenderCount(prev => prev + 1);
	});

	return {
		renderCount,
		metrics: performanceMonitor.getMetrics(componentName),
	};
}

/**
 * HOC to wrap components with performance monitoring
 */
export function withPerformanceMonitor<P extends object>(
	Component: ComponentType<P>,
	componentName?: string
)
{
	const WrappedComponent = (props: P) =>
	{
		const name = componentName || Component.displayName || Component.name || 'Unknown';
		usePerformanceMonitor(name);
		return createElement(Component, props);
	};

	WrappedComponent.displayName = `withPerformanceMonitor(${Component.displayName || Component.name})`;

	return WrappedComponent;
}

/**
 * Performance debugging utilities
 */
export const performanceDebug = {
	/**
	 * Log current performance report
	 */
	logReport(): void
	{
		const report = performanceMonitor.getReport();
		console.group('🚀 Performance Report');
		console.log('Total Components:', report.totalComponents);
		console.log('Total Renders:', report.totalRenders);
		console.log('Average Render Time:', `${report.averageRenderTime.toFixed(2)}ms`);

		if (report.slowestComponent)
		{
			console.log('Slowest Component:', {
				name: report.slowestComponent.componentName,
				averageTime: `${report.slowestComponent.averageRenderTime.toFixed(2)}ms`,
				renders: report.slowestComponent.updateCount,
			});
		}

		console.groupEnd();
	},

	/**
	 * Start performance monitoring with console logging
	 */
	startMonitoring(options: Partial<PerformanceConfig> = {}): void
	{
		performanceMonitor.configure({
			enabled: true,
			logToConsole: true,
			sampleRate: 1, // Monitor all renders in debug mode
			...options,
		});
		console.log('🚀 Performance monitoring started');
	},

	/**
	 * Stop performance monitoring
	 */
	stopMonitoring(): void
	{
		performanceMonitor.configure({ enabled: false });
		console.log('🚀 Performance monitoring stopped');
	},

	/**
	 * Clear all metrics
	 */
	clearMetrics(): void
	{
		performanceMonitor.clearMetrics();
		console.log('🚀 Performance metrics cleared');
	},
};

// Make debug utilities available globally in development
if (process.env.NODE_ENV === 'development')
{
	(window as any).__MISS_UI_PERFORMANCE__ = performanceDebug;
}
