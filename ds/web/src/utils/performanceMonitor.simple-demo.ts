/**
 * Simple Performance Monitor Demo
 * Demonstrates core functionality without dependencies
 */

import { performanceDebug, performanceMonitor } from "./performanceMonitor";

// Simple demo function that doesn't require React or DOM
export function runSimplePerformanceDemo(): void {
	console.log("🚀 Simple Performance Monitor Demo");
	console.log("==================================");

	// Configure performance monitoring
	performanceMonitor.configure({
		enabled: true,
		sampleRate: 1,
		logToConsole: true,
		trackMemory: false, // Disable memory tracking to avoid browser dependencies
	});

	console.log("\n📊 Recording sample performance metrics...");

	// Simulate component renders with different performance characteristics
	performanceMonitor.recordRender("FastComponent", 5.2);
	performanceMonitor.recordRender("FastComponent", 4.8);
	performanceMonitor.recordRender("FastComponent", 6.1);

	performanceMonitor.recordRender("MediumComponent", 12.5);
	performanceMonitor.recordRender("MediumComponent", 15.2);
	performanceMonitor.recordRender("MediumComponent", 11.8);

	performanceMonitor.recordRender("SlowComponent", 25.7);
	performanceMonitor.recordRender("SlowComponent", 28.3);
	performanceMonitor.recordRender("SlowComponent", 31.1);

	// Get individual metrics
	console.log("\n📈 Component Performance Metrics:");
	const fastMetrics = performanceMonitor.getMetrics("FastComponent");
	const mediumMetrics = performanceMonitor.getMetrics("MediumComponent");
	const slowMetrics = performanceMonitor.getMetrics("SlowComponent");

	console.log("FastComponent:", {
		averageRenderTime: `${fastMetrics?.averageRenderTime.toFixed(2)}ms`,
		updateCount: fastMetrics?.updateCount,
		status:
			fastMetrics && fastMetrics.averageRenderTime < 10
				? "🟢 Good"
				: "🟡 Moderate",
	});

	console.log("MediumComponent:", {
		averageRenderTime: `${mediumMetrics?.averageRenderTime.toFixed(2)}ms`,
		updateCount: mediumMetrics?.updateCount,
		status:
			mediumMetrics && mediumMetrics.averageRenderTime < 20
				? "🟡 Moderate"
				: "🔴 Slow",
	});

	console.log("SlowComponent:", {
		averageRenderTime: `${slowMetrics?.averageRenderTime.toFixed(2)}ms`,
		updateCount: slowMetrics?.updateCount,
		status:
			slowMetrics && slowMetrics.averageRenderTime > 25
				? "🔴 Slow"
				: "🟡 Moderate",
	});

	// Generate comprehensive performance report
	console.log("\n📋 Performance Report:");
	const report = performanceMonitor.getReport();
	console.log("Total Components:", report.totalComponents);
	console.log("Total Renders:", report.totalRenders);
	console.log(
		"Average Render Time:",
		`${report.averageRenderTime.toFixed(2)}ms`
	);
	console.log("Slowest Component:", report.slowestComponent?.componentName);
	console.log(
		"Slowest Average Time:",
		`${report.slowestComponent?.averageRenderTime.toFixed(2)}ms`
	);

	// Test performance thresholds
	console.log("\n⚠️  Performance Analysis:");
	const allMetrics = performanceMonitor.getAllMetrics();
	const slowComponents = allMetrics.filter((m) => m.averageRenderTime > 20);
	const fastComponents = allMetrics.filter((m) => m.averageRenderTime < 10);

	console.log(`Fast Components (< 10ms): ${fastComponents.length}`);
	console.log(`Slow Components (> 20ms): ${slowComponents.length}`);

	if (slowComponents.length > 0) {
		console.log("🔴 Components needing optimization:");
		slowComponents.forEach((component) => {
			console.log(
				`  - ${component.componentName}: ${component.averageRenderTime.toFixed(
					2
				)}ms`
			);
		});
	}

	// Test debug utilities
	console.log("\n🔧 Testing Debug Utilities:");
	performanceDebug.logReport();

	console.log("\n✅ Simple Performance Monitor Demo Complete");
	console.log("==========================================");

	// Return summary for programmatic use
	return {
		totalComponents: report.totalComponents,
		totalRenders: report.totalRenders,
		averageRenderTime: report.averageRenderTime,
		slowComponents: slowComponents.length,
		fastComponents: fastComponents.length,
		success: true,
	};
}

// Export for use in other contexts
export const performanceMonitorDemo = {
	run: runSimplePerformanceDemo,
	monitor: performanceMonitor,
	debug: performanceDebug,
};

// Run demo if this file is executed directly
if (typeof require !== "undefined" && require.main === module) {
	runSimplePerformanceDemo();
}
