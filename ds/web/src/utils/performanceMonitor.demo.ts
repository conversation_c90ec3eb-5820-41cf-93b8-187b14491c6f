/**
 * Performance Monitor Demo
 * Demonstrates the performance monitoring functionality
 */

import { performanceMonitor, performanceDebug } from './performanceMonitor';

// Demo function to test performance monitoring
export function runPerformanceDemo(): void
{
	console.log('🚀 Starting Performance Monitor Demo');
	console.log('=====================================');

	// Configure performance monitoring
	performanceMonitor.configure({
		enabled: true,
		sampleRate: 1,
		logToConsole: true,
		trackMemory: true,
	});

	// Simulate component renders
	console.log('\n📊 Simulating component renders...');

	// Fast component
	performanceMonitor.recordRender('FastComponent', 5.2);
	performanceMonitor.recordRender('FastComponent', 4.8);
	performanceMonitor.recordRender('FastComponent', 6.1);

	// Medium component
	performanceMonitor.recordRender('MediumComponent', 12.5);
	performanceMonitor.recordRender('MediumComponent', 15.2);
	performanceMonitor.recordRender('MediumComponent', 11.8);

	// Slow component
	performanceMonitor.recordRender('SlowComponent', 25.7);
	performanceMonitor.recordRender('SlowComponent', 28.3);

	// Get individual metrics
	console.log('\n📈 Individual Component Metrics:');
	const fastMetrics = performanceMonitor.getMetrics('FastComponent');
	const mediumMetrics = performanceMonitor.getMetrics('MediumComponent');
	const slowMetrics = performanceMonitor.getMetrics('SlowComponent');

	console.log('FastComponent:', {
		averageRenderTime: fastMetrics?.averageRenderTime.toFixed(2) + 'ms',
		updateCount: fastMetrics?.updateCount,
	});

	console.log('MediumComponent:', {
		averageRenderTime: mediumMetrics?.averageRenderTime.toFixed(2) + 'ms',
		updateCount: mediumMetrics?.updateCount,
	});

	console.log('SlowComponent:', {
		averageRenderTime: slowMetrics?.averageRenderTime.toFixed(2) + 'ms',
		updateCount: slowMetrics?.updateCount,
	});

	// Generate performance report
	console.log('\n📋 Performance Report:');
	const report = performanceMonitor.getReport();
	console.log('Total Components:', report.totalComponents);
	console.log('Total Renders:', report.totalRenders);
	console.log('Average Render Time:', report.averageRenderTime.toFixed(2) + 'ms');
	console.log('Slowest Component:', report.slowestComponent?.componentName);
	console.log('Slowest Average Time:', report.slowestComponent?.averageRenderTime.toFixed(2) + 'ms');

	// Test debug utilities
	console.log('\n🔧 Testing Debug Utilities:');
	performanceDebug.logReport();

	console.log('\n✅ Performance Monitor Demo Complete');
	console.log('=====================================');
}

// Run demo if called directly
if (typeof window === 'undefined')
{
	runPerformanceDemo();
}
