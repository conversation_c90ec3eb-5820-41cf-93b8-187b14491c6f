/**
 * CSS Optimization utilities for Vanilla Extract
 * Provides utilities for optimizing CSS output and reducing bundle size
 */

import type { StyleRule } from '@vanilla-extract/css';

/**
 * Utility to create optimized CSS rules that can be tree-shaken
 * Only includes styles that are actually used
 */
export const createOptimizedStyles = (styles: Record<string, StyleRule>) => {
	// Filter out unused styles in production
	if (process.env.NODE_ENV === 'production') {
		const optimizedStyles: Record<string, StyleRule> = {};

		// Only include styles that are referenced
		Object.entries(styles).forEach(([key, value]) => {
			if (value && typeof value === 'object') {
				optimizedStyles[key] = value;
			}
		});

		return optimizedStyles;
	}

	return styles;
};

/**
 * Utility to merge CSS classes efficiently
 * Reduces duplicate class names and optimizes output
 */
export const mergeClasses = (...classes: (string | undefined | null | false)[]): string => {
	return classes
		.filter(Boolean)
		.join(' ')
		.trim();
};

/**
 * Utility to conditionally apply CSS classes
 * Helps with tree-shaking by only including necessary classes
 */
export const conditionalClass = (
	condition: boolean,
	trueClass: string,
	falseClass?: string,
): string | undefined => {
	if (condition) {
		return trueClass;
	}
	return falseClass;
};

/**
 * CSS-in-JS optimization helper
 * Removes unused CSS properties in production builds
 */
export const optimizeStyleRule = (rule: StyleRule): StyleRule => {
	if (process.env.NODE_ENV === 'production') {
		// Remove development-only styles
		const optimizedRule = { ...rule };

		// Remove debug styles
		delete optimizedRule.outline;
		delete optimizedRule.border;

		return optimizedRule;
	}

	return rule;
};

/**
 * Media query optimization
 * Combines similar media queries to reduce CSS output
 */
export const optimizeMediaQueries = (
	mediaQueries: Record<string, StyleRule>,
): Record<string, StyleRule> => {
	// Group similar media queries
	const optimized: Record<string, StyleRule> = {};

	Object.entries(mediaQueries).forEach(([query, styles]) => {
		// Combine similar breakpoints
		const normalizedQuery = query.replace(/\s+/g, ' ').trim();

		if (optimized[normalizedQuery]) {
			optimized[normalizedQuery] = {
				...optimized[normalizedQuery],
				...styles,
			};
		} else {
			optimized[normalizedQuery] = styles;
		}
	});

	return optimized;
};

/**
 * Theme-aware CSS optimization
 * Optimizes theme-based styles for better performance
 */
export const optimizeThemeStyles = (
	lightStyles: StyleRule,
	darkStyles: StyleRule,
): { light: StyleRule; dark: StyleRule } => {
	// Extract common styles
	const commonStyles: StyleRule = {};
	const lightOnlyStyles: StyleRule = {};
	const darkOnlyStyles: StyleRule = {};

	// Find common properties
	Object.keys(lightStyles).forEach((key) => {
		if (key in darkStyles && lightStyles[key] === darkStyles[key]) {
			commonStyles[key] = lightStyles[key];
		} else {
			lightOnlyStyles[key] = lightStyles[key];
		}
	});

	// Add dark-only styles
	Object.keys(darkStyles).forEach((key) => {
		if (!(key in lightStyles)) {
			darkOnlyStyles[key] = darkStyles[key];
		}
	});

	return {
		light: { ...commonStyles, ...lightOnlyStyles },
		dark: { ...commonStyles, ...darkOnlyStyles },
	};
};

/**
 * CSS custom properties optimization
 * Optimizes CSS custom properties for better performance
 */
export const optimizeCSSVariables = (
	variables: Record<string, string>,
): Record<string, string> => {
	const optimized: Record<string, string> = {};

	// Remove unused variables in production
	if (process.env.NODE_ENV === 'production') {
		Object.entries(variables).forEach(([key, value]) => {
			// Only include variables that are actually used
			if (value && value.trim() !== '') {
				optimized[key] = value;
			}
		});
	} else {
		return variables;
	}

	return optimized;
};

/**
 * Animation optimization
 * Optimizes CSS animations for better performance
 */
export const optimizeAnimations = (
	animations: Record<string, StyleRule>,
): Record<string, StyleRule> => {
	const optimized: Record<string, StyleRule> = {};

	Object.entries(animations).forEach(([key, animation]) => {
		// Optimize animation properties
		const optimizedAnimation = { ...animation };

		// Use transform instead of changing layout properties
		if (optimizedAnimation.left || optimizedAnimation.top) {
			const translateX = optimizedAnimation.left || '0';
			const translateY = optimizedAnimation.top || '0';

			optimizedAnimation.transform = `translate(${translateX}, ${translateY})`;
			delete optimizedAnimation.left;
			delete optimizedAnimation.top;
		}

		// Add will-change for better performance
		if (optimizedAnimation.transform || optimizedAnimation.opacity) {
			optimizedAnimation.willChange = 'transform, opacity';
		}

		optimized[key] = optimizedAnimation;
	});

	return optimized;
};

/**
 * Bundle size optimization utilities
 */
export const bundleOptimization = {
	/**
	 * Mark styles as side-effect free for better tree-shaking
	 */
	markAsSideEffectFree: (styles: Record<string, any>) => {
		// Add metadata for bundlers
		if (process.env.NODE_ENV === 'production') {
			Object.defineProperty(styles, '__esModule', { value: true });
			Object.defineProperty(styles, 'sideEffects', { value: false });
		}
		return styles;
	},

	/**
	 * Create lazy-loaded styles for better code splitting
	 */
	createLazyStyles: (styleFactory: () => Record<string, StyleRule>) => {
		let cachedStyles: Record<string, StyleRule> | null = null;

		return () => {
			if (!cachedStyles) {
				cachedStyles = styleFactory();
			}
			return cachedStyles;
		};
	},

	/**
	 * Optimize component styles for tree-shaking
	 */
	optimizeComponentStyles: (
		baseStyles: StyleRule,
		variantStyles: Record<string, StyleRule>,
	) => {
		return {
			base: optimizeStyleRule(baseStyles),
			variants: createOptimizedStyles(variantStyles),
		};
	},

	/**
	 * Create critical CSS extraction helper
	 */
	extractCriticalCSS: (
		componentStyles: Record<string, StyleRule>,
		criticalComponents: string[],
	) => {
		const critical: Record<string, StyleRule> = {};
		const nonCritical: Record<string, StyleRule> = {};

		Object.entries(componentStyles).forEach(([key, styles]) => {
			if (criticalComponents.some(comp => key.includes(comp))) {
				critical[key] = styles;
			} else {
				nonCritical[key] = styles;
			}
		});

		return { critical, nonCritical };
	},

	/**
	 * Optimize CSS for production builds
	 */
	optimizeForProduction: (styles: Record<string, StyleRule>) => {
		if (process.env.NODE_ENV !== 'production') {
			return styles;
		}

		const optimized: Record<string, StyleRule> = {};

		Object.entries(styles).forEach(([key, rule]) => {
			// Remove development-only styles
			const cleanRule = { ...rule };

			// Remove debug properties
			delete cleanRule.outline;
			delete cleanRule.border;

			// Optimize colors (convert to shorter formats)
			Object.keys(cleanRule).forEach(prop => {
				const value = cleanRule[prop];
				if (typeof value === 'string') {
					// Convert hex colors to shorter format
					cleanRule[prop] = value.replace(/#([0-9a-f])\1([0-9a-f])\2([0-9a-f])\3/gi, '#$1$2$3');
				}
			});

			optimized[key] = cleanRule;
		});

		return optimized;
	},

	/**
	 * Create CSS modules with automatic purging
	 */
	createPurgedStyles: (
		allStyles: Record<string, StyleRule>,
		usedClasses: string[],
	) => {
		const purged: Record<string, StyleRule> = {};

		usedClasses.forEach(className => {
			if (allStyles[className]) {
				purged[className] = allStyles[className];
			}
		});

		return purged;
	},

	/**
	 * Optimize CSS custom properties for better compression
	 */
	optimizeCSSCustomProperties: (
		properties: Record<string, string>,
	) => {
		const optimized: Record<string, string> = {};

		Object.entries(properties).forEach(([key, value]) => {
			// Shorten variable names in production
			if (process.env.NODE_ENV === 'production') {
				const shortKey = key.replace(/--([a-z]+)-([a-z]+)-([a-z]+)/, '--$1-$2');
				optimized[shortKey] = value;
			} else {
				optimized[key] = value;
			}
		});

		return optimized;
	},

	/**
	 * Create atomic CSS classes for better reusability
	 */
	createAtomicClasses: (
		styles: Record<string, StyleRule>,
	) => {
		const atomic: Record<string, StyleRule> = {};
		const composite: Record<string, string[]> = {};

		Object.entries(styles).forEach(([className, rule]) => {
			const atomicClasses: string[] = [];

			// Extract common properties into atomic classes
			Object.entries(rule).forEach(([prop, value]) => {
				const atomicClassName = `${prop}-${String(value).replace(/[^a-zA-Z0-9]/g, '')}`;

				if (!atomic[atomicClassName]) {
					atomic[atomicClassName] = { [prop]: value };
				}

				atomicClasses.push(atomicClassName);
			});

			composite[className] = atomicClasses;
		});

		return { atomic, composite };
	},

	/**
	 * Advanced CSS optimization for production builds
	 */
	optimizeForTreeShaking: (
		componentStyles: Record<string, StyleRule>,
		usedComponents: string[],
	) => {
		if (process.env.NODE_ENV !== 'production') {
			return componentStyles;
		}

		const optimized: Record<string, StyleRule> = {};

		// Only include styles for components that are actually used
		usedComponents.forEach(component => {
			Object.keys(componentStyles).forEach(styleKey => {
				if (styleKey.includes(component)) {
					optimized[styleKey] = componentStyles[styleKey];
				}
			});
		});

		return optimized;
	},

	/**
	 * Optimize CSS for different bundle targets with enhanced compression
	 */
	optimizeForBundleTarget: (
		styles: Record<string, StyleRule>,
		target: 'main' | 'factories' | 'factories-heavy' | 'components',
	) => {
		const optimized: Record<string, StyleRule> = {};

		Object.entries(styles).forEach(([key, rule]) => {
			const optimizedRule = { ...rule };

			switch (target) {
				case 'main':
					// Critical path optimization - minimal styles only
					if (optimizedRule.animation || optimizedRule.transition) {
						// Reduce animation complexity for main bundle
						if (optimizedRule.transition) {
							optimizedRule.transition = 'all 0.2s ease';
						}
					}
					break;

				case 'factories':
					// Medium optimization - balance features and size
					break;

				case 'factories-heavy':
					// Full features - size is less critical
					break;

				case 'components':
					// Component-specific optimizations
					if (optimizedRule.boxShadow) {
						// Simplify shadows for better performance
						optimizedRule.boxShadow = optimizedRule.boxShadow.toString().replace(/,\s*[^,]*inset[^,]*/g, '');
					}
					break;
			}

			optimized[key] = optimizedRule;
		});

		return optimized;
	},

	/**
	 * Create CSS bundle with size monitoring and optimization
	 */
	createOptimizedBundle: (
		allStyles: Record<string, StyleRule>,
		bundleTarget: string,
	) => {
		const startTime = performance.now();
		const originalSize = JSON.stringify(allStyles).length;

		// Apply optimizations based on bundle target
		let optimized = allStyles;

		// Remove unused vendor prefixes
		optimized = Object.fromEntries(
			Object.entries(optimized).map(([key, rule]) => {
				const cleanRule = { ...rule };

				// Remove unnecessary vendor prefixes for modern browsers
				Object.keys(cleanRule).forEach(prop => {
					if (prop.startsWith('-webkit-') && !prop.includes('appearance')) {
						delete cleanRule[prop];
					}
					if (prop.startsWith('-moz-') && !prop.includes('appearance')) {
						delete cleanRule[prop];
					}
				});

				return [key, cleanRule];
			})
		);

		// Optimize color values
		optimized = Object.fromEntries(
			Object.entries(optimized).map(([key, rule]) => {
				const cleanRule = { ...rule };

				Object.keys(cleanRule).forEach(prop => {
					const value = cleanRule[prop];
					if (typeof value === 'string') {
						// Convert rgba to hex when alpha is 1
						cleanRule[prop] = value.replace(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*1\)/g, (match, r, g, b) => {
							const hex = ((1 << 24) + (parseInt(r) << 16) + (parseInt(g) << 8) + parseInt(b)).toString(16).slice(1);
							return `#${hex}`;
						});

						// Shorten hex colors
						cleanRule[prop] = cleanRule[prop].replace(/#([0-9a-f])\1([0-9a-f])\2([0-9a-f])\3/gi, '#$1$2$3');
					}
				});

				return [key, cleanRule];
			})
		);

		const optimizedSize = JSON.stringify(optimized).length;
		const endTime = performance.now();

		if (process.env.NODE_ENV === 'development') {
			console.log(`CSS Bundle Optimization (${bundleTarget}):`);
			console.log(`  Original: ${(originalSize / 1024).toFixed(2)} KB`);
			console.log(`  Optimized: ${(optimizedSize / 1024).toFixed(2)} KB`);
			console.log(`  Savings: ${(((originalSize - optimizedSize) / originalSize) * 100).toFixed(1)}%`);
			console.log(`  Time: ${(endTime - startTime).toFixed(2)}ms`);
		}

		return optimized;
	},

	/**
	 * CSS bundle splitting for better caching
	 */
	splitCSSByComponent: (
		allStyles: Record<string, StyleRule>,
	) => {
		const splits = {
			core: {} as Record<string, StyleRule>,
			elements: {} as Record<string, StyleRule>,
			collections: {} as Record<string, StyleRule>,
			modules: {} as Record<string, StyleRule>,
			factories: {} as Record<string, StyleRule>,
			base: {} as Record<string, StyleRule>,
			utilities: {} as Record<string, StyleRule>,
		};

		Object.entries(allStyles).forEach(([key, styles]) => {
			if (key.includes('Core.')) {
				splits.core[key] = styles;
			} else if (key.includes('Element.')) {
				splits.elements[key] = styles;
			} else if (key.includes('Collection.')) {
				splits.collections[key] = styles;
			} else if (key.includes('Module.')) {
				splits.modules[key] = styles;
			} else if (key.includes('Factory.')) {
				splits.factories[key] = styles;
			} else if (key.includes('Base.')) {
				splits.base[key] = styles;
			} else {
				splits.utilities[key] = styles;
			}
		});

		return splits;
	},

	/**
	 * Generate critical CSS for above-the-fold content
	 */
	generateCriticalCSS: (
		allStyles: Record<string, StyleRule>,
		criticalComponents: string[],
	) => {
		const critical: Record<string, StyleRule> = {};
		const deferred: Record<string, StyleRule> = {};

		Object.entries(allStyles).forEach(([key, styles]) => {
			const isCritical = criticalComponents.some(comp =>
				key.toLowerCase().includes(comp.toLowerCase())
			);

			if (isCritical) {
				critical[key] = styles;
			} else {
				deferred[key] = styles;
			}
		});

		return { critical, deferred };
	},

	/**
	 * Optimize CSS for different build targets
	 */
	optimizeForTarget: (
		styles: Record<string, StyleRule>,
		target: 'modern' | 'legacy' | 'ssr',
	) => {
		const optimized: Record<string, StyleRule> = {};

		Object.entries(styles).forEach(([key, rule]) => {
			const optimizedRule = { ...rule };

			switch (target) {
				case 'modern':
					// Use modern CSS features
					if (optimizedRule.display === 'flex') {
						optimizedRule.display = 'flex';
						// Keep modern flexbox properties
					}
					break;

				case 'legacy':
					// Add fallbacks for older browsers
					if (optimizedRule.display === 'grid') {
						optimizedRule.display = 'block'; // Fallback
					}
					break;

				case 'ssr':
					// Remove client-only styles
					delete optimizedRule.transition;
					delete optimizedRule.animation;
					break;
			}

			optimized[key] = optimizedRule;
		});

		return optimized;
	},

	/**
	 * Monitor CSS bundle size and performance
	 */
	createCSSMonitor: () => {
		const monitor = {
			totalStyles: 0,
			totalSize: 0,
			componentBreakdown: {} as Record<string, number>,
			optimizationHistory: [] as Array<{
				timestamp: number;
				totalSize: number;
				optimizationLevel: number;
			}>,

			track: (componentName: string, styles: Record<string, StyleRule>) => {
				const styleCount = Object.keys(styles).length;
				const estimatedSize = JSON.stringify(styles).length;

				monitor.totalStyles += styleCount;
				monitor.totalSize += estimatedSize;
				monitor.componentBreakdown[componentName] = estimatedSize;
			},

			optimize: (styles: Record<string, StyleRule>) => {
				const originalSize = JSON.stringify(styles).length;
				const optimized = bundleOptimization.optimizeForProduction(styles);
				const optimizedSize = JSON.stringify(optimized).length;
				const optimizationLevel = ((originalSize - optimizedSize) / originalSize) * 100;

				monitor.optimizationHistory.push({
					timestamp: Date.now(),
					totalSize: optimizedSize,
					optimizationLevel,
				});

				return optimized;
			},

			report: () => {
				console.log('CSS Bundle Monitor Report:');
				console.log(`Total styles: ${monitor.totalStyles}`);
				console.log(`Estimated size: ${(monitor.totalSize / 1024).toFixed(2)} KB`);

				const sorted = Object.entries(monitor.componentBreakdown)
					.sort(([, a], [, b]) => b - a)
					.slice(0, 10);

				console.log('Largest components:');
				sorted.forEach(([name, size]) => {
					console.log(`  ${name}: ${(size / 1024).toFixed(2)} KB`);
				});

				if (monitor.optimizationHistory.length > 0) {
					const latest = monitor.optimizationHistory[monitor.optimizationHistory.length - 1];
					console.log(`Latest optimization: ${latest.optimizationLevel.toFixed(1)}% reduction`);
				}
			},

			getMetrics: () => ({
				totalStyles: monitor.totalStyles,
				totalSize: monitor.totalSize,
				componentBreakdown: monitor.componentBreakdown,
				optimizationHistory: monitor.optimizationHistory,
			}),

			exportReport: () => {
				const report = {
					timestamp: new Date().toISOString(),
					metrics: monitor.getMetrics(),
					recommendations: [] as string[],
				};

				// Generate recommendations based on metrics
				if (monitor.totalSize > 100 * 1024) { // 100KB
					report.recommendations.push('Consider splitting CSS into smaller chunks');
				}

				const largestComponents = Object.entries(monitor.componentBreakdown)
					.sort(([, a], [, b]) => b - a)
					.slice(0, 3);

				largestComponents.forEach(([name, size]) => {
					if (size > 20 * 1024) { // 20KB
						report.recommendations.push(`Optimize ${name} component styles (${(size / 1024).toFixed(2)} KB)`);
					}
				});

				return report;
			},
		};

		return monitor;
	},

	/**
	 * Advanced CSS tree-shaking for production builds
	 */
	enableAdvancedTreeShaking: (
		allStyles: Record<string, StyleRule>,
		usedSelectors: string[],
	) => {
		if (process.env.NODE_ENV !== 'production') {
			return allStyles;
		}

		const treeShaken: Record<string, StyleRule> = {};
		const selectorUsageMap = new Set(usedSelectors);

		// Only include styles that are actually used
		Object.entries(allStyles).forEach(([selector, rule]) => {
			if (selectorUsageMap.has(selector) || selector.includes('base') || selector.includes('reset')) {
				treeShaken[selector] = rule;
			}
		});

		// Log tree-shaking results in development
		if (process.env.NODE_ENV === 'development') {
			const originalCount = Object.keys(allStyles).length;
			const treeShakeCount = Object.keys(treeShaken).length;
			const reduction = ((originalCount - treeShakeCount) / originalCount) * 100;

			console.log(`CSS Tree-shaking: ${reduction.toFixed(1)}% reduction (${originalCount} → ${treeShakeCount} selectors)`);
		}

		return treeShaken;
	},

	/**
	 * CSS performance optimization with metrics
	 */
	optimizeForPerformance: (
		styles: Record<string, StyleRule>,
		performanceTarget: 'fast' | 'balanced' | 'quality' = 'balanced',
	) => {
		const optimized: Record<string, StyleRule> = {};

		Object.entries(styles).forEach(([key, rule]) => {
			const optimizedRule = { ...rule };

			switch (performanceTarget) {
				case 'fast':
					// Aggressive optimizations for speed
					delete optimizedRule.boxShadow;
					delete optimizedRule.textShadow;
					delete optimizedRule.filter;
					if (optimizedRule.borderRadius) {
						optimizedRule.borderRadius = '4px'; // Standardize
					}
					break;

				case 'balanced':
					// Balanced optimizations
					if (optimizedRule.boxShadow && typeof optimizedRule.boxShadow === 'string') {
						// Simplify complex shadows
						optimizedRule.boxShadow = optimizedRule.boxShadow.replace(/,\s*[^,]*inset[^,]*/g, '');
					}
					break;

				case 'quality':
					// Minimal optimizations, preserve quality
					break;
			}

			// Common optimizations for all targets
			if (optimizedRule.transition && typeof optimizedRule.transition === 'string') {
				// Optimize transition properties
				optimizedRule.transition = optimizedRule.transition.replace(/all/g, 'opacity, transform');
			}

			optimized[key] = optimizedRule;
		});

		return optimized;
	},

	/**
	 * CSS bundle size analyzer with detailed metrics
	 */
	analyzeBundleSize: (styles: Record<string, StyleRule>) => {
		const analysis = {
			totalSelectors: Object.keys(styles).length,
			totalProperties: 0,
			estimatedSize: 0,
			compressionPotential: 0,
			duplicateProperties: 0,
			optimizationOpportunities: [] as string[],
		};

		const propertyCount = new Map<string, number>();
		const valueCount = new Map<string, number>();

		Object.entries(styles).forEach(([selector, rule]) => {
			Object.entries(rule).forEach(([property, value]) => {
				analysis.totalProperties++;

				// Count property usage
				propertyCount.set(property, (propertyCount.get(property) || 0) + 1);

				// Count value usage
				const valueStr = String(value);
				valueCount.set(valueStr, (valueCount.get(valueStr) || 0) + 1);
			});
		});

		// Calculate estimated size
		analysis.estimatedSize = JSON.stringify(styles).length;

		// Find duplicate properties
		propertyCount.forEach((count, property) => {
			if (count > 10) {
				analysis.duplicateProperties++;
				analysis.optimizationOpportunities.push(`Consider extracting ${property} to CSS custom properties`);
			}
		});

		// Find duplicate values
		valueCount.forEach((count, value) => {
			if (count > 5 && value.length > 10) {
				analysis.optimizationOpportunities.push(`Value "${value}" is used ${count} times - consider tokenization`);
			}
		});

		// Estimate compression potential
		const uniqueValues = valueCount.size;
		const totalValues = Array.from(valueCount.values()).reduce((sum, count) => sum + count, 0);
		analysis.compressionPotential = ((totalValues - uniqueValues) / totalValues) * 100;

		return analysis;
	},
};

export type {
	StyleRule,
};
