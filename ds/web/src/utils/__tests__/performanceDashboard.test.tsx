/**
 * Performance Dashboard Tests
 * Test suite for the real-time performance dashboard functionality
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
	PerformanceDashboard,
	initializePerformanceDashboard,
	getPerformanceDashboard,
	togglePerformanceDashboard,
} from '../performanceDashboard';
import { performanceMonitor } from '../performanceMonitor';
import type { DashboardConfig } from '../performanceDashboard';

// Mock DOM methods
const mockCreateElement = vi.fn();
const mockAppendChild = vi.fn();
const mockRemoveChild = vi.fn();
const mockAddEventListener = vi.fn();
const mockRemoveEventListener = vi.fn();
const mockMatchMedia = vi.fn();

// Mock document and window
Object.defineProperty(global, 'document', {
	value: {
		createElement: mockCreateElement,
		body: {
			appendChild: mockAppendChild,
			removeChild: mockRemoveChild,
		},
		addEventListener: mockAddEventListener,
		removeEventListener: mockRemoveEventListener,
		readyState: 'complete',
	},
	writable: true,
});

Object.defineProperty(global, 'window', {
	value: {
		matchMedia: mockMatchMedia,
	},
	writable: true,
});

// Mock performance
Object.defineProperty(global, 'performance', {
	value: {
		now: vi.fn().mockReturnValue(100),
		memory: {
			usedJSHeapSize: 1024 * 1024,
		},
	},
	writable: true,
});

// Mock console methods
const mockConsoleLog = vi.spyOn(console, 'log').mockImplementation(() => {});

describe('PerformanceDashboard', () =>
{
	let mockElement: HTMLElement;

	beforeEach(() =>
	{
		// Reset mocks
		vi.clearAllMocks();

		// Mock DOM element
		mockElement = {
			id: '',
			style: { cssText: '', display: '' },
			innerHTML: '',
			classList: {
				add: vi.fn(),
				remove: vi.fn(),
			},
			setAttribute: vi.fn(),
			remove: vi.fn(),
			offsetHeight: 100,
		} as any;

		mockCreateElement.mockReturnValue(mockElement);
		mockMatchMedia.mockReturnValue({
			matches: false,
			addEventListener: vi.fn(),
			removeEventListener: vi.fn(),
		});

		// Clear performance monitor
		performanceMonitor.clearMetrics();
	});

	afterEach(() =>
	{
		// Clean up any dashboard instances
		const dashboard = getPerformanceDashboard();
		if (dashboard)
		{
			dashboard.destroy();
		}
	});

	describe('Initialization', () =>
	{
		it('should create dashboard with default configuration', () =>
		{
			const dashboard = new PerformanceDashboard();

			expect(mockCreateElement).toHaveBeenCalledWith('div');
			expect(mockAppendChild).toHaveBeenCalledWith(mockElement);
			expect(mockElement.id).toBe('miss-ui-performance-dashboard');
		});

		it('should create dashboard with custom configuration', () =>
		{
			const config: Partial<DashboardConfig> = {
				position: 'bottom-left',
				keyboardShortcut: 'ctrl+shift+d',
				thresholds: {
					warning: 20,
					critical: 40,
				},
			};

			const dashboard = new PerformanceDashboard(config);

			expect(mockCreateElement).toHaveBeenCalled();
			expect(mockAddEventListener).toHaveBeenCalledWith('keydown', expect.any(Function));
		});

		it('should not initialize in non-development environment', () =>
		{
			const originalEnv = process.env.NODE_ENV;
			process.env.NODE_ENV = 'production';

			const dashboard = new PerformanceDashboard();

			expect(mockCreateElement).not.toHaveBeenCalled();

			process.env.NODE_ENV = originalEnv;
		});

		it('should not initialize without window object', () =>
		{
			const originalWindow = global.window;
			delete (global as any).window;

			const dashboard = new PerformanceDashboard({ enabled: true });

			expect(mockCreateElement).not.toHaveBeenCalled();

			global.window = originalWindow;
		});
	});

	describe('Keyboard Shortcuts', () =>
	{
		it('should set up keyboard event listener', () =>
		{
			new PerformanceDashboard();

			expect(mockAddEventListener).toHaveBeenCalledWith('keydown', expect.any(Function));
		});

		it('should toggle dashboard on correct keyboard shortcut', () =>
		{
			const dashboard = new PerformanceDashboard();
			const keyboardHandler = mockAddEventListener.mock.calls[0][1];

			// Simulate Ctrl+Shift+P
			const event = {
				ctrlKey: true,
				shiftKey: true,
				key: 'p',
				preventDefault: vi.fn(),
			};

			keyboardHandler(event);

			expect(event.preventDefault).toHaveBeenCalled();
			expect(mockElement.style.display).toBe('block');
		});

		it('should not toggle on incorrect keyboard shortcut', () =>
		{
			const dashboard = new PerformanceDashboard();
			const keyboardHandler = mockAddEventListener.mock.calls[0][1];

			// Simulate wrong key combination
			const event = {
				ctrlKey: true,
				shiftKey: false,
				key: 'p',
				preventDefault: vi.fn(),
			};

			keyboardHandler(event);

			expect(event.preventDefault).not.toHaveBeenCalled();
			expect(mockElement.style.display).toBe('none');
		});
	});

	describe('Performance Monitoring Integration', () =>
	{
		it('should configure performance monitor on initialization', () =>
		{
			const configureSpy = vi.spyOn(performanceMonitor, 'configure');

			new PerformanceDashboard();

			expect(configureSpy).toHaveBeenCalledWith({
				enabled: true,
				sampleRate: 1,
				trackMemory: true,
				onMetricsUpdate: expect.any(Function),
			});
		});

		it('should update metrics when performance monitor reports', () =>
		{
			const dashboard = new PerformanceDashboard();
			const configureSpy = vi.spyOn(performanceMonitor, 'configure');
			const onMetricsUpdate = configureSpy.mock.calls[0][0].onMetricsUpdate;

			const mockMetrics = {
				componentName: 'TestComponent',
				renderTime: 25,
				updateCount: 1,
				averageRenderTime: 25,
				lastRenderTimestamp: 100,
				mountTime: 50,
				memoryUsage: 1024 * 1024,
			};

			onMetricsUpdate(mockMetrics);

			const state = dashboard.getState();
			expect(state.metrics).toHaveLength(1);
			expect(state.metrics[0]).toEqual(mockMetrics);
		});

		it('should generate performance warnings for slow renders', () =>
		{
			const dashboard = new PerformanceDashboard({
				thresholds: { warning: 15, critical: 30 },
			});
			const configureSpy = vi.spyOn(performanceMonitor, 'configure');
			const onMetricsUpdate = configureSpy.mock.calls[0][0].onMetricsUpdate;

			// Critical render time
			onMetricsUpdate({
				componentName: 'SlowComponent',
				renderTime: 35,
				updateCount: 1,
				averageRenderTime: 35,
				lastRenderTimestamp: 100,
				mountTime: 50,
			});

			const state = dashboard.getState();
			expect(state.warnings).toHaveLength(1);
			expect(state.warnings[0]).toContain('🔴 SlowComponent: Critical render time');
		});

		it('should limit number of stored metrics', () =>
		{
			const dashboard = new PerformanceDashboard({ maxEntries: 3 });
			const configureSpy = vi.spyOn(performanceMonitor, 'configure');
			const onMetricsUpdate = configureSpy.mock.calls[0][0].onMetricsUpdate;

			// Add more metrics than the limit
			for (let i = 0; i < 5; i++)
			{
				onMetricsUpdate({
					componentName: `Component${i}`,
					renderTime: 10,
					updateCount: 1,
					averageRenderTime: 10,
					lastRenderTimestamp: 100 + i,
					mountTime: 50,
				});
			}

			const state = dashboard.getState();
			expect(state.metrics).toHaveLength(3);
			expect(state.metrics[0].componentName).toBe('Component4'); // Most recent
		});
	});

	describe('Dashboard Visibility', () =>
	{
		it('should start hidden by default', () =>
		{
			const dashboard = new PerformanceDashboard();

			expect(mockElement.style.display).toBe('none');
			expect(dashboard.getState().visible).toBe(false);
		});

		it('should toggle visibility', () =>
		{
			const dashboard = new PerformanceDashboard();

			dashboard.toggle();
			expect(mockElement.style.display).toBe('block');
			expect(dashboard.getState().visible).toBe(true);

			dashboard.toggle();
			expect(mockElement.style.display).toBe('none');
			expect(dashboard.getState().visible).toBe(false);
		});

		it('should show dashboard', () =>
		{
			const dashboard = new PerformanceDashboard();

			dashboard.show();
			expect(mockElement.style.display).toBe('block');
			expect(dashboard.getState().visible).toBe(true);
		});

		it('should hide dashboard', () =>
		{
			const dashboard = new PerformanceDashboard();
			dashboard.show();

			dashboard.hide();
			expect(mockElement.style.display).toBe('none');
			expect(dashboard.getState().visible).toBe(false);
		});
	});

	describe('Dashboard Content', () =>
	{
		it('should update content with performance data', () =>
		{
			const dashboard = new PerformanceDashboard();

			// Add some test data
			performanceMonitor.recordRender('TestComponent', 15);
			performanceMonitor.recordRender('SlowComponent', 35);

			dashboard.show();

			expect(mockElement.innerHTML).toContain('Performance Monitor');
			expect(mockElement.innerHTML).toContain('Components:');
			expect(mockElement.innerHTML).toContain('Total Renders:');
		});

		it('should show memory usage when available', () =>
		{
			const dashboard = new PerformanceDashboard();
			const configureSpy = vi.spyOn(performanceMonitor, 'configure');
			const onMetricsUpdate = configureSpy.mock.calls[0][0].onMetricsUpdate;

			onMetricsUpdate({
				componentName: 'TestComponent',
				renderTime: 10,
				updateCount: 1,
				averageRenderTime: 10,
				lastRenderTimestamp: 100,
				mountTime: 50,
				memoryUsage: 2 * 1024 * 1024, // 2MB
			});

			dashboard.show();

			expect(mockElement.innerHTML).toContain('Memory Usage:');
			expect(mockElement.innerHTML).toContain('2.0 MB');
		});

		it('should display performance warnings', () =>
		{
			const dashboard = new PerformanceDashboard({
				thresholds: { warning: 10, critical: 20 },
			});
			const configureSpy = vi.spyOn(performanceMonitor, 'configure');
			const onMetricsUpdate = configureSpy.mock.calls[0][0].onMetricsUpdate;

			onMetricsUpdate({
				componentName: 'SlowComponent',
				renderTime: 25,
				updateCount: 1,
				averageRenderTime: 25,
				lastRenderTimestamp: 100,
				mountTime: 50,
			});

			dashboard.show();

			expect(mockElement.innerHTML).toContain('Performance Warnings');
			expect(mockElement.innerHTML).toContain('SlowComponent');
		});
	});

	describe('Configuration Updates', () =>
	{
		it('should update configuration dynamically', () =>
		{
			const dashboard = new PerformanceDashboard();

			dashboard.configure({
				position: 'bottom-left',
				thresholds: { warning: 20, critical: 40 },
			});

			expect(mockElement.style.cssText).toContain('bottom: 20px; left: 20px;');
		});
	});

	describe('Cleanup', () =>
	{
		it('should clean up resources on destroy', () =>
		{
			const dashboard = new PerformanceDashboard();

			dashboard.destroy();

			expect(mockRemoveEventListener).toHaveBeenCalledWith('keydown', expect.any(Function));
			expect(mockElement.remove).toHaveBeenCalled();
		});

		it('should clear metrics and warnings', () =>
		{
			const dashboard = new PerformanceDashboard();

			// Add some data
			performanceMonitor.recordRender('TestComponent', 15);

			dashboard.clear();

			const state = dashboard.getState();
			expect(state.metrics).toHaveLength(0);
			expect(state.warnings).toHaveLength(0);
		});
	});
});

describe('Global Dashboard Functions', () =>
{
	beforeEach(() =>
	{
		vi.clearAllMocks();
		mockCreateElement.mockReturnValue({
			id: '',
			style: { cssText: '', display: '' },
			innerHTML: '',
			classList: { add: vi.fn(), remove: vi.fn() },
			setAttribute: vi.fn(),
			remove: vi.fn(),
		} as any);
	});

	describe('initializePerformanceDashboard', () =>
	{
		it('should create and return dashboard instance', () =>
		{
			const dashboard = initializePerformanceDashboard();

			expect(dashboard).toBeInstanceOf(PerformanceDashboard);
			expect(getPerformanceDashboard()).toBe(dashboard);
		});

		it('should destroy existing instance before creating new one', () =>
		{
			const firstDashboard = initializePerformanceDashboard();
			const destroySpy = vi.spyOn(firstDashboard, 'destroy');

			const secondDashboard = initializePerformanceDashboard();

			expect(destroySpy).toHaveBeenCalled();
			expect(getPerformanceDashboard()).toBe(secondDashboard);
		});
	});

	describe('togglePerformanceDashboard', () =>
	{
		it('should toggle existing dashboard', () =>
		{
			const dashboard = initializePerformanceDashboard();
			const toggleSpy = vi.spyOn(dashboard, 'toggle');

			togglePerformanceDashboard();

			expect(toggleSpy).toHaveBeenCalled();
		});

		it('should create dashboard if none exists in development', () =>
		{
			const originalEnv = process.env.NODE_ENV;
			process.env.NODE_ENV = 'development';

			togglePerformanceDashboard();

			expect(getPerformanceDashboard()).toBeInstanceOf(PerformanceDashboard);

			process.env.NODE_ENV = originalEnv;
		});

		it('should not create dashboard in production', () =>
		{
			const originalEnv = process.env.NODE_ENV;
			process.env.NODE_ENV = 'production';

			togglePerformanceDashboard();

			expect(getPerformanceDashboard()).toBeNull();

			process.env.NODE_ENV = originalEnv;
		});
	});
});

describe('Performance Color Coding', () =>
{
	it('should return correct colors for performance values', () =>
	{
		const dashboard = new PerformanceDashboard({
			thresholds: { warning: 16, critical: 33 },
		});

		// Access private method through type assertion for testing
		const getColor = (dashboard as any).getPerformanceColor.bind(dashboard);

		expect(getColor(10)).toBe('#4ecdc4'); // Green - good performance
		expect(getColor(20)).toBe('#ffa500'); // Orange - warning
		expect(getColor(40)).toBe('#ff6b6b'); // Red - critical
	});
});

describe('Memory Usage Formatting', () =>
{
	it('should format memory usage correctly', () =>
	{
		const dashboard = new PerformanceDashboard();

		// Access private method through type assertion for testing
		const formatMemory = (dashboard as any).formatMemoryUsage.bind(dashboard);

		expect(formatMemory(1024 * 1024)).toBe('1.0 MB');
		expect(formatMemory(2.5 * 1024 * 1024)).toBe('2.5 MB');
		expect(formatMemory(512 * 1024)).toBe('0.5 MB');
	});
});

describe('Error Handling', () =>
{
	it('should handle missing DOM gracefully', () =>
	{
		const originalDocument = global.document;
		delete (global as any).document;

		expect(() =>
		{
			new PerformanceDashboard({ enabled: true });
		}).not.toThrow();

		global.document = originalDocument;
	});

	it('should handle DOM manipulation errors gracefully', () =>
	{
		mockCreateElement.mockImplementation(() =>
		{
			throw new Error('DOM error');
		});

		expect(() =>
		{
			new PerformanceDashboard();
		}).not.toThrow();
	});

	it('should handle update timer errors gracefully', () =>
	{
		const dashboard = new PerformanceDashboard();

		// Mock element to throw error on innerHTML access
		const faultyElement = {
			style: { cssText: '', display: '' },
			get innerHTML() { throw new Error('DOM error'); },
			set innerHTML(value) { throw new Error('DOM error'); },
		};

		(dashboard as any).element = faultyElement;

		expect(() =>
		{
			dashboard.show();
		}).not.toThrow();
	});
});
