/**
 * Performance Monitor Tests
 * Comprehensive test suite for performance monitoring functionality
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, act } from '@testing-library/react';
import { createElement } from 'react';
import {
	performanceMonitor,
	usePerformanceMonitor,
	withPerformanceMonitor,
	performanceDebug,
} from '../performanceMonitor';
import type { PerformanceMetrics, PerformanceConfig } from '../performanceMonitor';

// Mock performance.now for consistent testing
const mockPerformanceNow = vi.fn();
Object.defineProperty(global, 'performance', {
	value: {
		now: mockPerformanceNow,
		memory: {
			usedJSHeapSize: 1024 * 1024, // 1MB
		},
	},
	writable: true,
});

// Mock console methods
const mockConsoleLog = vi.spyOn(console, 'log').mockImplementation(() => {});
const mockConsoleGroup = vi.spyOn(console, 'group').mockImplementation(() => {});
const mockConsoleGroupEnd = vi.spyOn(console, 'groupEnd').mockImplementation(() => {});

describe('PerformanceMonitor', () =>
{
	beforeEach(() =>
	{
		// Reset performance monitor state
		performanceMonitor.clearMetrics();
		performanceMonitor.configure({
			enabled: true,
			sampleRate: 1,
			logToConsole: false,
			trackMemory: false,
		});

		// Reset mock timers
		mockPerformanceNow.mockReturnValue(0);
		vi.clearAllMocks();
	});

	afterEach(() =>
	{
		performanceMonitor.clearMetrics();
	});

	describe('Configuration', () =>
	{
		it('should configure performance monitoring settings', () =>
		{
			const config: Partial<PerformanceConfig> = {
				enabled: false,
				sampleRate: 0.5,
				logToConsole: true,
				trackMemory: true,
			};

			performanceMonitor.configure(config);
			const currentConfig = performanceMonitor.getConfig();

			expect(currentConfig.enabled).toBe(false);
			expect(currentConfig.sampleRate).toBe(0.5);
			expect(currentConfig.logToConsole).toBe(true);
			expect(currentConfig.trackMemory).toBe(true);
		});

		it('should merge configuration with existing settings', () =>
		{
			performanceMonitor.configure({ enabled: true, sampleRate: 0.8 });
			performanceMonitor.configure({ logToConsole: true });

			const config = performanceMonitor.getConfig();
			expect(config.enabled).toBe(true);
			expect(config.sampleRate).toBe(0.8);
			expect(config.logToConsole).toBe(true);
		});
	});

	describe('Metrics Recording', () =>
	{
		it('should record render performance metrics', () =>
		{
			mockPerformanceNow.mockReturnValue(100);

			performanceMonitor.recordRender('TestComponent', 15.5);

			const metrics = performanceMonitor.getMetrics('TestComponent');
			expect(metrics).toBeDefined();
			expect(metrics?.componentName).toBe('TestComponent');
			expect(metrics?.renderTime).toBe(15.5);
			expect(metrics?.updateCount).toBe(1);
			expect(metrics?.averageRenderTime).toBe(15.5);
		});

		it('should calculate average render time correctly', () =>
		{
			performanceMonitor.recordRender('TestComponent', 10);
			performanceMonitor.recordRender('TestComponent', 20);
			performanceMonitor.recordRender('TestComponent', 30);

			const metrics = performanceMonitor.getMetrics('TestComponent');
			expect(metrics?.updateCount).toBe(3);
			expect(metrics?.averageRenderTime).toBe(20);
		});

		it('should track memory usage when enabled', () =>
		{
			performanceMonitor.configure({ trackMemory: true });
			performanceMonitor.recordRender('TestComponent', 10);

			const metrics = performanceMonitor.getMetrics('TestComponent');
			expect(metrics?.memoryUsage).toBe(1024 * 1024);
		});

		it('should not track memory usage when disabled', () =>
		{
			performanceMonitor.configure({ trackMemory: false });
			performanceMonitor.recordRender('TestComponent', 10);

			const metrics = performanceMonitor.getMetrics('TestComponent');
			expect(metrics?.memoryUsage).toBeUndefined();
		});

		it('should respect sample rate', () =>
		{
			performanceMonitor.configure({ sampleRate: 0 });
			performanceMonitor.recordRender('TestComponent', 10);

			const metrics = performanceMonitor.getMetrics('TestComponent');
			expect(metrics).toBeUndefined();
		});

		it('should log to console when enabled', () =>
		{
			performanceMonitor.configure({ logToConsole: true });
			performanceMonitor.recordRender('TestComponent', 15.5);

			expect(mockConsoleLog).toHaveBeenCalledWith(
				'[Performance] TestComponent:',
				expect.objectContaining({
					renderTime: '15.50ms',
					averageRenderTime: '15.50ms',
					updateCount: 1,
				})
			);
		});

		it('should call onMetricsUpdate callback', () =>
		{
			const callback = vi.fn();
			performanceMonitor.configure({ onMetricsUpdate: callback });
			performanceMonitor.recordRender('TestComponent', 10);

			expect(callback).toHaveBeenCalledWith(
				expect.objectContaining({
					componentName: 'TestComponent',
					renderTime: 10,
				})
			);
		});
	});

	describe('Metrics Retrieval', () =>
	{
		beforeEach(() =>
		{
			performanceMonitor.recordRender('Component1', 10);
			performanceMonitor.recordRender('Component2', 20);
			performanceMonitor.recordRender('Component1', 15);
		});

		it('should get metrics for specific component', () =>
		{
			const metrics = performanceMonitor.getMetrics('Component1');
			expect(metrics?.componentName).toBe('Component1');
			expect(metrics?.updateCount).toBe(2);
			expect(metrics?.averageRenderTime).toBe(12.5);
		});

		it('should return undefined for non-existent component', () =>
		{
			const metrics = performanceMonitor.getMetrics('NonExistent');
			expect(metrics).toBeUndefined();
		});

		it('should get all metrics', () =>
		{
			const allMetrics = performanceMonitor.getAllMetrics();
			expect(allMetrics).toHaveLength(2);
			expect(allMetrics.map(m => m.componentName)).toContain('Component1');
			expect(allMetrics.map(m => m.componentName)).toContain('Component2');
		});

		it('should clear all metrics', () =>
		{
			performanceMonitor.clearMetrics();
			const allMetrics = performanceMonitor.getAllMetrics();
			expect(allMetrics).toHaveLength(0);
		});
	});

	describe('Performance Report', () =>
	{
		beforeEach(() =>
		{
			performanceMonitor.recordRender('FastComponent', 5);
			performanceMonitor.recordRender('SlowComponent', 25);
			performanceMonitor.recordRender('SlowComponent', 35);
			performanceMonitor.recordRender('MediumComponent', 15);
		});

		it('should generate comprehensive performance report', () =>
		{
			const report = performanceMonitor.getReport();

			expect(report.totalComponents).toBe(3);
			expect(report.totalRenders).toBe(4);
			expect(report.slowestComponent?.componentName).toBe('SlowComponent');
			expect(report.slowestComponent?.averageRenderTime).toBe(30);
			expect(report.averageRenderTime).toBe(20); // (5 + 25 + 35 + 15) / 4
		});

		it('should handle empty metrics', () =>
		{
			performanceMonitor.clearMetrics();
			const report = performanceMonitor.getReport();

			expect(report.totalComponents).toBe(0);
			expect(report.totalRenders).toBe(0);
			expect(report.slowestComponent).toBeNull();
			expect(report.averageRenderTime).toBe(0);
		});
	});
});

describe('usePerformanceMonitor Hook', () =>
{
	let TestComponent: React.FC;

	beforeEach(() =>
	{
		performanceMonitor.clearMetrics();
		mockPerformanceNow.mockReturnValue(0);

		TestComponent = () =>
		{
			const { renderCount, metrics } = usePerformanceMonitor('TestComponent');
			return (
				<div data-testid="test-component">
					<span data-testid="render-count">{renderCount}</span>
					<span data-testid="avg-time">{metrics?.averageRenderTime?.toFixed(2) || '0'}</span>
				</div>
			);
		};
	});

	it('should track component renders', () =>
	{
		const { getByTestId, rerender } = render(<TestComponent />);

		expect(getByTestId('render-count')).toHaveTextContent('1');

		rerender(<TestComponent />);
		expect(getByTestId('render-count')).toHaveTextContent('2');
	});

	it('should provide metrics data', () =>
	{
		mockPerformanceNow
			.mockReturnValueOnce(0)  // Start time
			.mockReturnValueOnce(10); // End time

		const { getByTestId } = render(<TestComponent />);

		// Wait for useEffect to run
		act(() =>
		{
			// Trigger a re-render to see updated metrics
		});

		const metrics = performanceMonitor.getMetrics('TestComponent');
		expect(metrics).toBeDefined();
		expect(metrics?.componentName).toBe('TestComponent');
	});
});

describe('withPerformanceMonitor HOC', () =>
{
	let BaseComponent: React.FC<{ value: string }>;
	let WrappedComponent: React.FC<{ value: string }>;

	beforeEach(() =>
	{
		performanceMonitor.clearMetrics();
		mockPerformanceNow.mockReturnValue(0);

		BaseComponent = ({ value }) => <div data-testid="base-component">{value}</div>;
		WrappedComponent = withPerformanceMonitor(BaseComponent, 'WrappedComponent');
	});

	it('should wrap component with performance monitoring', () =>
	{
		const { getByTestId } = render(<WrappedComponent value="test" />);
		expect(getByTestId('base-component')).toHaveTextContent('test');

		const metrics = performanceMonitor.getMetrics('WrappedComponent');
		expect(metrics).toBeDefined();
		expect(metrics?.componentName).toBe('WrappedComponent');
	});

	it('should use component name when no custom name provided', () =>
	{
		const AutoNamedComponent = withPerformanceMonitor(BaseComponent);
		render(<AutoNamedComponent value="test" />);

		const metrics = performanceMonitor.getMetrics('BaseComponent');
		expect(metrics).toBeDefined();
	});

	it('should set correct display name', () =>
	{
		expect(WrappedComponent.displayName).toBe('withPerformanceMonitor(BaseComponent)');
	});
});

describe('Performance Debug Utilities', () =>
{
	beforeEach(() =>
	{
		performanceMonitor.clearMetrics();
		performanceMonitor.recordRender('TestComponent', 15);
		performanceMonitor.recordRender('SlowComponent', 35);
	});

	it('should log performance report', () =>
	{
		performanceDebug.logReport();

		expect(mockConsoleGroup).toHaveBeenCalledWith('🚀 Performance Report');
		expect(mockConsoleLog).toHaveBeenCalledWith('Total Components:', 2);
		expect(mockConsoleLog).toHaveBeenCalledWith('Total Renders:', 2);
		expect(mockConsoleGroupEnd).toHaveBeenCalled();
	});

	it('should start monitoring with debug options', () =>
	{
		performanceDebug.startMonitoring({ sampleRate: 0.5 });

		const config = performanceMonitor.getConfig();
		expect(config.enabled).toBe(true);
		expect(config.logToConsole).toBe(true);
		expect(config.sampleRate).toBe(0.5);
	});

	it('should stop monitoring', () =>
	{
		performanceDebug.stopMonitoring();

		const config = performanceMonitor.getConfig();
		expect(config.enabled).toBe(false);
	});

	it('should clear metrics', () =>
	{
		performanceDebug.clearMetrics();

		const allMetrics = performanceMonitor.getAllMetrics();
		expect(allMetrics).toHaveLength(0);
	});
});

describe('Error Handling', () =>
{
	it('should handle invalid component names gracefully', () =>
	{
		expect(() =>
		{
			performanceMonitor.recordRender('', 10);
		}).not.toThrow();

		expect(() =>
		{
			performanceMonitor.getMetrics('');
		}).not.toThrow();
	});

	it('should handle negative render times', () =>
	{
		performanceMonitor.recordRender('TestComponent', -5);
		const metrics = performanceMonitor.getMetrics('TestComponent');
		expect(metrics?.renderTime).toBe(-5);
	});

	it('should handle missing performance.memory gracefully', () =>
	{
		// Mock performance without memory property
		Object.defineProperty(global, 'performance', {
			value: { now: mockPerformanceNow },
			writable: true,
		});

		performanceMonitor.configure({ trackMemory: true });
		performanceMonitor.recordRender('TestComponent', 10);

		const metrics = performanceMonitor.getMetrics('TestComponent');
		expect(metrics?.memoryUsage).toBeUndefined();
	});

	it('should handle callback errors gracefully', () =>
	{
		const faultyCallback = vi.fn().mockImplementation(() =>
		{
			throw new Error('Callback error');
		});

		performanceMonitor.configure({ onMetricsUpdate: faultyCallback });

		expect(() =>
		{
			performanceMonitor.recordRender('TestComponent', 10);
		}).not.toThrow();

		expect(faultyCallback).toHaveBeenCalled();
	});
});

describe('Integration Tests', () =>
{
	it('should work with multiple components simultaneously', () =>
	{
		const Component1 = () =>
		{
			usePerformanceMonitor('Component1');
			return <div>Component 1</div>;
		};

		const Component2 = () =>
		{
			usePerformanceMonitor('Component2');
			return <div>Component 2</div>;
		};

		render(
			<div>
				<Component1 />
				<Component2 />
			</div>
		);

		const metrics1 = performanceMonitor.getMetrics('Component1');
		const metrics2 = performanceMonitor.getMetrics('Component2');

		expect(metrics1).toBeDefined();
		expect(metrics2).toBeDefined();
		expect(metrics1?.componentName).toBe('Component1');
		expect(metrics2?.componentName).toBe('Component2');
	});

	it('should maintain separate metrics for different components', () =>
	{
		performanceMonitor.recordRender('Component1', 10);
		performanceMonitor.recordRender('Component2', 20);
		performanceMonitor.recordRender('Component1', 30);

		const metrics1 = performanceMonitor.getMetrics('Component1');
		const metrics2 = performanceMonitor.getMetrics('Component2');

		expect(metrics1?.updateCount).toBe(2);
		expect(metrics1?.averageRenderTime).toBe(20);
		expect(metrics2?.updateCount).toBe(1);
		expect(metrics2?.averageRenderTime).toBe(20);
	});
});

describe('Performance Impact', () =>
{
	it('should have minimal overhead when disabled', () =>
	{
		performanceMonitor.configure({ enabled: false });

		const startTime = performance.now();
		for (let i = 0; i < 10; i++)
		{
			performanceMonitor.recordRender(`Component${i}`, 10);
		}
		const endTime = performance.now();

		// Should complete very quickly when disabled
		expect(endTime - startTime).toBeLessThan(100);
		expect(performanceMonitor.getAllMetrics()).toHaveLength(0);
	});

	it('should respect sample rate for performance', () =>
	{
		performanceMonitor.configure({ sampleRate: 0.1 });

		// Record some renders
		for (let i = 0; i < 20; i++)
		{
			performanceMonitor.recordRender(`Component${i}`, 10);
		}

		// Should have fewer metrics due to sampling
		const allMetrics = performanceMonitor.getAllMetrics();
		expect(allMetrics.length).toBeLessThan(20);
	});
});
