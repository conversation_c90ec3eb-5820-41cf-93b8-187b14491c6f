/**
 * Real-time Performance Dashboard for Miss UI Web
 * Provides visual performance monitoring during development
 */

import type { PerformanceMetrics } from "./performanceMonitor";
import { performanceMonitor } from "./performanceMonitor";

interface DashboardConfig {
	enabled: boolean;
	position: "top-left" | "top-right" | "bottom-left" | "bottom-right";
	keyboardShortcut: string;
	thresholds: {
		warning: number; // ms
		critical: number; // ms
	};
	maxEntries: number;
	updateInterval: number; // ms
}

interface DashboardState {
	visible: boolean;
	metrics: PerformanceMetrics[];
	warnings: string[];
	memoryUsage?: number;
}

/**
 * Performance Dashboard class for real-time monitoring
 */
class PerformanceDashboard {
	private config: DashboardConfig;

	private state: DashboardState;

	private element: HTMLElement | null = null;

	private updateTimer: NodeJS.Timeout | null = null;

	private keyboardListener: ((event: KeyboardEvent) => void) | null = null;

	constructor(config: Partial<DashboardConfig> = {}) {
		this.config = {
			enabled: process.env.NODE_ENV === "development",
			position: "top-right",
			keyboardShortcut: "ctrl+shift+p",
			thresholds: {
				warning: 16, // 16ms = 60fps threshold
				critical: 33, // 33ms = 30fps threshold
			},
			maxEntries: 50,
			updateInterval: 1000,
			...config,
		};

		this.state = {
			visible: false,
			metrics: [],
			warnings: [],
		};

		if (this.config.enabled && typeof window !== "undefined") {
			this.initialize();
		}
	}

	/**
	 * Initialize the dashboard
	 */
	private initialize(): void {
		this.setupKeyboardShortcut();
		this.setupPerformanceMonitoring();
		this.createDashboardElement();
		this.startUpdateLoop();
	}

	/**
	 * Set up keyboard shortcut to toggle dashboard
	 */
	private setupKeyboardShortcut(): void {
		this.keyboardListener = (event: KeyboardEvent) => {
			const shortcut = this.config.keyboardShortcut.toLowerCase();
			const keys = shortcut.split("+");

			let matches = true;

			if (keys.includes("ctrl") && !event.ctrlKey) matches = false;
			if (keys.includes("shift") && !event.shiftKey) matches = false;
			if (keys.includes("alt") && !event.altKey) matches = false;
			if (keys.includes("meta") && !event.metaKey) matches = false;

			const keyName = keys[keys.length - 1];
			if (event.key.toLowerCase() !== keyName) matches = false;

			if (matches) {
				event.preventDefault();
				this.toggle();
			}
		};

		document.addEventListener("keydown", this.keyboardListener);
	}

	/**
	 * Set up performance monitoring integration
	 */
	private setupPerformanceMonitoring(): void {
		performanceMonitor.configure({
			enabled: true,
			sampleRate: 1, // Monitor all renders in development
			trackMemory: true,
			onMetricsUpdate: (metrics) => {
				this.updateMetrics(metrics);
			},
		});
	}

	/**
	 * Update metrics and check for performance issues
	 */
	private updateMetrics(metrics: PerformanceMetrics): void {
		// Add to metrics list
		this.state.metrics.unshift(metrics);

		// Keep only recent entries
		if (this.state.metrics.length > this.config.maxEntries) {
			this.state.metrics = this.state.metrics.slice(0, this.config.maxEntries);
		}

		// Check for performance warnings
		this.checkPerformanceWarnings(metrics);

		// Update memory usage
		this.state.memoryUsage = metrics.memoryUsage;
	}

	/**
	 * Check for performance warnings
	 */
	private checkPerformanceWarnings(metrics: PerformanceMetrics): void {
		const warnings: string[] = [];

		if (metrics.renderTime > this.config.thresholds.critical) {
			warnings.push(
				`🔴 ${
					metrics.componentName
				}: Critical render time (${metrics.renderTime.toFixed(2)}ms)`
			);
		} else if (metrics.renderTime > this.config.thresholds.warning) {
			warnings.push(
				`🟡 ${
					metrics.componentName
				}: Slow render time (${metrics.renderTime.toFixed(2)}ms)`
			);
		}

		if (metrics.updateCount > 100) {
			warnings.push(
				`⚠️ ${metrics.componentName}: High render count (${metrics.updateCount})`
			);
		}

		// Add warnings to state
		warnings.forEach((warning) => {
			if (!this.state.warnings.includes(warning)) {
				this.state.warnings.unshift(warning);
			}
		});

		// Keep only recent warnings
		if (this.state.warnings.length > 10) {
			this.state.warnings = this.state.warnings.slice(0, 10);
		}
	}

	/**
	 * Create dashboard DOM element
	 */
	private createDashboardElement(): void {
		this.element = document.createElement("div");
		this.element.id = "miss-ui-performance-dashboard";
		this.element.style.cssText = this.getDashboardStyles();

		document.body.appendChild(this.element);
		this.updateDashboardContent();
	}

	/**
	 * Get dashboard CSS styles
	 */
	private getDashboardStyles(): string {
		const position = this.getPositionStyles();

		return `
			position: fixed;
			${position}
			width: 400px;
			max-height: 600px;
			background: rgba(0, 0, 0, 0.9);
			color: white;
			font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
			font-size: 12px;
			line-height: 1.4;
			padding: 16px;
			border-radius: 8px;
			box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
			backdrop-filter: blur(10px);
			z-index: 999999;
			overflow-y: auto;
			display: ${this.state.visible ? "block" : "none"};
			transition: opacity 0.2s ease;
		`;
	}

	/**
	 * Get position styles based on config
	 */
	private getPositionStyles(): string {
		switch (this.config.position) {
			case "top-left":
				return "top: 20px; left: 20px;";
			case "top-right":
				return "top: 20px; right: 20px;";
			case "bottom-left":
				return "bottom: 20px; left: 20px;";
			case "bottom-right":
				return "bottom: 20px; right: 20px;";
			default:
				return "top: 20px; right: 20px;";
		}
	}

	/**
	 * Update dashboard content
	 */
	private updateDashboardContent(): void {
		if (!this.element) return;

		const report = performanceMonitor.getReport();
		const recentMetrics = this.state.metrics.slice(0, 10);

		this.element.innerHTML = `
			<div style="border-bottom: 1px solid #333; padding-bottom: 12px; margin-bottom: 12px;">
				<h3 style="margin: 0 0 8px 0; color: #00d4ff;">🚀 Performance Monitor</h3>
				<div style="font-size: 10px; color: #888;">
					Press ${this.config.keyboardShortcut.toUpperCase()} to toggle
				</div>
			</div>

			<div style="margin-bottom: 16px;">
				<div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
					<span>Components:</span>
					<span style="color: #00d4ff;">${report.totalComponents}</span>
				</div>
				<div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
					<span>Total Renders:</span>
					<span style="color: #00d4ff;">${report.totalRenders}</span>
				</div>
				<div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
					<span>Avg Render Time:</span>
					<span style="color: ${this.getPerformanceColor(report.averageRenderTime)};">
						${report.averageRenderTime.toFixed(2)}ms
					</span>
				</div>
				${
					this.state.memoryUsage
						? `
					<div style="display: flex; justify-content: space-between;">
						<span>Memory Usage:</span>
						<span style="color: #00d4ff;">${this.formatMemoryUsage(
							this.state.memoryUsage
						)}</span>
					</div>
				`
						: ""
				}
			</div>

			${
				this.state.warnings.length > 0
					? `
				<div style="margin-bottom: 16px;">
					<h4 style="margin: 0 0 8px 0; color: #ff6b6b;">⚠️ Performance Warnings</h4>
					<div style="max-height: 120px; overflow-y: auto;">
						${this.state.warnings
							.map(
								(warning) => `
							<div style="margin-bottom: 4px; padding: 4px; background: rgba(255, 107, 107, 0.1); border-radius: 4px; font-size: 11px;">
								${warning}
							</div>
						`
							)
							.join("")}
					</div>
				</div>
			`
					: ""
			}

			${
				report.slowestComponent
					? `
				<div style="margin-bottom: 16px;">
					<h4 style="margin: 0 0 8px 0; color: #ffa500;">🐌 Slowest Component</h4>
					<div style="background: rgba(255, 165, 0, 0.1); padding: 8px; border-radius: 4px;">
						<div style="font-weight: bold;">${report.slowestComponent.componentName}</div>
						<div style="font-size: 11px; color: #ccc;">
							Avg: ${report.slowestComponent.averageRenderTime.toFixed(2)}ms |
							Renders: ${report.slowestComponent.updateCount}
						</div>
					</div>
				</div>
			`
					: ""
			}

			${
				recentMetrics.length > 0
					? `
				<div>
					<h4 style="margin: 0 0 8px 0; color: #4ecdc4;">📊 Recent Activity</h4>
					<div style="max-height: 200px; overflow-y: auto;">
						${recentMetrics
							.map(
								(metric) => `
							<div style="display: flex; justify-content: space-between; margin-bottom: 2px; padding: 2px 0; border-bottom: 1px solid #222;">
								<span style="flex: 1; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
									${metric.componentName}
								</span>
								<span style="color: ${this.getPerformanceColor(
									metric.renderTime
								)}; margin-left: 8px;">
									${metric.renderTime.toFixed(1)}ms
								</span>
							</div>
						`
							)
							.join("")}
					</div>
				</div>
			`
					: ""
			}

			<div style="margin-top: 16px; padding-top: 12px; border-top: 1px solid #333; font-size: 10px; color: #666;">
				<div>Thresholds: Warning ${this.config.thresholds.warning}ms | Critical ${
			this.config.thresholds.critical
		}ms</div>
				<div>Sample Rate: ${(performanceMonitor.getConfig().sampleRate * 100).toFixed(
					0
				)}%</div>
			</div>
		`;
	}

	/**
	 * Get color based on performance value
	 */
	private getPerformanceColor(value: number): string {
		if (value > this.config.thresholds.critical) {
			return "#ff6b6b"; // Red
		}
		if (value > this.config.thresholds.warning) {
			return "#ffa500"; // Orange
		}

		return "#4ecdc4"; // Green
	}

	/**
	 * Format memory usage for display
	 */
	private formatMemoryUsage(bytes: number): string {
		const mb = bytes / (1024 * 1024);
		return `${mb.toFixed(1)} MB`;
	}

	/**
	 * Start update loop
	 */
	private startUpdateLoop(): void {
		this.updateTimer = setInterval(() => {
			if (this.state.visible) {
				this.updateDashboardContent();
			}
		}, this.config.updateInterval);
	}

	/**
	 * Toggle dashboard visibility
	 */
	toggle(): void {
		this.state.visible = !this.state.visible;

		if (this.element) {
			this.element.style.display = this.state.visible ? "block" : "none";

			if (this.state.visible) {
				this.updateDashboardContent();
			}
		}
	}

	/**
	 * Show dashboard
	 */
	show(): void {
		this.state.visible = true;

		if (this.element) {
			this.element.style.display = "block";
			this.updateDashboardContent();
		}
	}

	/**
	 * Hide dashboard
	 */
	hide(): void {
		this.state.visible = false;

		if (this.element) {
			this.element.style.display = "none";
		}
	}

	/**
	 * Update configuration
	 */
	configure(config: Partial<DashboardConfig>): void {
		this.config = { ...this.config, ...config };

		if (this.element) {
			this.element.style.cssText = this.getDashboardStyles();
			this.updateDashboardContent();
		}
	}

	/**
	 * Get current state
	 */
	getState(): DashboardState {
		return { ...this.state };
	}

	/**
	 * Clear metrics and warnings
	 */
	clear(): void {
		this.state.metrics = [];
		this.state.warnings = [];
		performanceMonitor.clearMetrics();

		if (this.state.visible) {
			this.updateDashboardContent();
		}
	}

	/**
	 * Destroy dashboard and clean up resources
	 */
	destroy(): void {
		if (this.updateTimer) {
			clearInterval(this.updateTimer);
			this.updateTimer = null;
		}

		if (this.keyboardListener) {
			document.removeEventListener("keydown", this.keyboardListener);
			this.keyboardListener = null;
		}

		if (this.element) {
			this.element.remove();
			this.element = null;
		}

		this.state.metrics = [];
		this.state.warnings = [];
	}
}

// Global dashboard instance
let dashboardInstance: PerformanceDashboard | null = null;

/**
 * Initialize performance dashboard
 */
export function initializePerformanceDashboard(
	config: Partial<DashboardConfig> = {}
): PerformanceDashboard {
	if (dashboardInstance) {
		dashboardInstance.destroy();
	}

	dashboardInstance = new PerformanceDashboard(config);
	return dashboardInstance;
}

/**
 * Get global dashboard instance
 */
export function getPerformanceDashboard(): PerformanceDashboard | null {
	return dashboardInstance;
}

/**
 * Toggle dashboard visibility
 */
export function togglePerformanceDashboard(): void {
	if (dashboardInstance) {
		dashboardInstance.toggle();
	} else if (process.env.NODE_ENV === "development") {
		const newDashboard = initializePerformanceDashboard();
		newDashboard.show();
	}
}

// Auto-initialize in development
if (process.env.NODE_ENV === "development" && typeof window !== "undefined") {
	// Wait for DOM to be ready
	if (document.readyState === "loading") {
		document.addEventListener("DOMContentLoaded", () => {
			initializePerformanceDashboard();
		});
	} else {
		initializePerformanceDashboard();
	}
}

// Make dashboard available globally in development
if (process.env.NODE_ENV === "development") {
	(window as any).__MISS_UI_DASHBOARD__ = {
		toggle: togglePerformanceDashboard,
		show: () => dashboardInstance?.show(),
		hide: () => dashboardInstance?.hide(),
		clear: () => dashboardInstance?.clear(),
		getState: () => dashboardInstance?.getState(),
	};
}

export type { DashboardConfig, DashboardState };

export { PerformanceDashboard };
