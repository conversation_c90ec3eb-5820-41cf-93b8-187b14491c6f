/**
 * Lightweight Factory Components Entry Point
 * These components have minimal dependencies and can be included in the main bundle
 * or loaded quickly on demand
 *
 * Bundle Size Target: <60KB gzipped
 * Dependencies: Minimal, suitable for main bundle inclusion
 * Tree-shaking: Fully optimized for selective imports
 * Usage: import { FactoryLayout, FactoryToast } from "@miss-ui/web/factories-light"
 */

// Layout Factory - Lightweight, minimal dependencies
export {
	FactoryLayout,
	Layout as FactoryLayoutBase,
	LayoutHeader as FactoryLayoutHeader,
	LayoutMain as FactoryLayoutMain,
	LayoutSidebar as FactoryLayoutSidebar,
	LayoutContent as FactoryLayoutContent,
	LayoutFooter as FactoryLayoutFooter,
} from './components/Factory.Layout';
export type {
	LayoutProps as FactoryLayoutProps,
	LayoutHeaderProps as FactoryLayoutHeaderProps,
	LayoutMainProps as FactoryLayoutMainProps,
	LayoutSidebarProps as FactoryLayoutSidebarProps,
	LayoutContentProps as FactoryLayoutContentProps,
	LayoutFooterProps as FactoryLayoutFooterProps,
} from './components/Factory.Layout';

// Toast Factory - Lightweight, minimal dependencies
export {
	Toast as FactoryToast,
	ToastProvider as FactoryToastProvider,
	ToastContainer as FactoryToastContainer,
	useToast as useFactoryToast,
} from './components/Factory.Toast';
export type {
	ToastProps as FactoryToastProps,
	ToastData as FactoryToastData,
	ToastType as FactoryToastType,
	ToastPosition as FactoryToastPosition,
	ToastProviderProps as FactoryToastProviderProps,
	ToastContextValue as FactoryToastContextValue,
	ToastContainerProps as FactoryToastContainerProps,
} from './components/Factory.Toast';
