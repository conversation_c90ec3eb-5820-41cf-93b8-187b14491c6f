/**
 * Dynamic Factory Component Imports
 * This file provides dynamic imports for Factory components to enable code splitting
 */

// Type definitions for dynamic imports
export type FactoryComponent<T = any> = {
	default: T;
};

/**
 * Light Factory Components (smaller bundles, loaded eagerly)
 */
export const loadLayoutFactory = () => import('./components/Factory.Layout/Layout').then(m => m.default);
export const loadToastFactory = () => import('./components/Factory.Toast/Toast').then(m => m.default);

/**
 * Standard Factory Components (medium bundles, loaded on demand)
 */
export const loadFormFactory = () => import('./components/Factory.Form/Form').then(m => m.default);
export const loadSearchFactory = () => import('./components/Factory.Search/Search').then(m => m.default);
export const loadSelectFactory = () => import('./components/Factory.Select/Select').then(m => m.default);
export const loadModalFactory = () => import('./components/Factory.Modal/Modal').then(m => m.default);
export const loadStepperFactory = () => import('./components/Factory.Stepper/Stepper').then(m => m.default);
export const loadTabberFactory = () => import('./components/Factory.Tabber/Tabber').then(m => m.default);

/**
 * Heavy Factory Components (large bundles with heavy dependencies, loaded lazily)
 */
export const loadEditorFactory = () => import('./components/Factory.Editor/Editor').then(m => m.default);
export const loadPlayerFactory = () => import('./components/Factory.Player/Player').then(m => m.default);
export const loadDataTableFactory = () => import('./components/Factory.DataTable/DataTable').then(m => m.default);
export const loadKbarFactory = () => import('./components/Factory.Kbar/Kbar').then(m => m.default);

/**
 * Factory Component Registry
 * Provides a centralized way to access factory components with lazy loading
 */
export class FactoryRegistry {
	private static cache = new Map<string, any>();
	private static loadingPromises = new Map<string, Promise<any>>();

	/**
	 * Load a factory component with caching
	 */
	static async load<T>(name: string, loader: () => Promise<T>): Promise<T> {
		// Return cached component if available
		if (this.cache.has(name)) {
			return this.cache.get(name);
		}

		// Return existing loading promise if component is being loaded
		if (this.loadingPromises.has(name)) {
			return this.loadingPromises.get(name);
		}

		// Start loading the component
		const loadingPromise = loader().then(component => {
			this.cache.set(name, component);
			this.loadingPromises.delete(name);
			return component;
		}).catch(error => {
			this.loadingPromises.delete(name);
			throw error;
		});

		this.loadingPromises.set(name, loadingPromise);
		return loadingPromise;
	}

	/**
	 * Preload factory components for better performance
	 */
	static preload(components: string[]): Promise<void[]> {
		const loaders: Record<string, () => Promise<any>> = {
			layout: loadLayoutFactory,
			toast: loadToastFactory,
			form: loadFormFactory,
			search: loadSearchFactory,
			select: loadSelectFactory,
			modal: loadModalFactory,
			stepper: loadStepperFactory,
			tabber: loadTabberFactory,
			editor: loadEditorFactory,
			player: loadPlayerFactory,
			datatable: loadDataTableFactory,
			kbar: loadKbarFactory,
		};

		return Promise.all(
			components.map(async component => {
				const loader = loaders[component.toLowerCase()];
				if (loader) {
					try {
						await this.load(component, loader);
					} catch (error) {
						console.warn(`Failed to preload factory component: ${component}`, error);
					}
				}
			})
		);
	}

	/**
	 * Clear the component cache
	 */
	static clearCache(): void {
		this.cache.clear();
		this.loadingPromises.clear();
	}

	/**
	 * Get cache statistics
	 */
	static getCacheStats(): { cached: number; loading: number } {
		return {
			cached: this.cache.size,
			loading: this.loadingPromises.size,
		};
	}
}

/**
 * Factory Component Loaders with Registry Integration
 */
export const FactoryLoaders = {
	// Light components
	Layout: () => FactoryRegistry.load('layout', loadLayoutFactory),
	Toast: () => FactoryRegistry.load('toast', loadToastFactory),

	// Standard components
	Form: () => FactoryRegistry.load('form', loadFormFactory),
	Search: () => FactoryRegistry.load('search', loadSearchFactory),
	Select: () => FactoryRegistry.load('select', loadSelectFactory),
	Modal: () => FactoryRegistry.load('modal', loadModalFactory),
	Stepper: () => FactoryRegistry.load('stepper', loadStepperFactory),
	Tabber: () => FactoryRegistry.load('tabber', loadTabberFactory),

	// Heavy components
	Editor: () => FactoryRegistry.load('editor', loadEditorFactory),
	Player: () => FactoryRegistry.load('player', loadPlayerFactory),
	DataTable: () => FactoryRegistry.load('datatable', loadDataTableFactory),
	Kbar: () => FactoryRegistry.load('kbar', loadKbarFactory),
};

/**
 * Utility function to create a lazy factory component wrapper
 */
export function createLazyFactory<T>(
	loader: () => Promise<T>,
	fallback?: React.ComponentType
): React.LazyExoticComponent<React.ComponentType<any>> {
	const LazyComponent = React.lazy(async () => {
		try {
			const component = await loader();
			return { default: component };
		} catch (error) {
			console.error('Failed to load factory component:', error);
			if (fallback) {
				return { default: fallback };
			}
			throw error;
		}
	});

	return LazyComponent;
}

/**
 * React Suspense wrapper for factory components
 */
export function FactorySuspense({
	children,
	fallback,
}: {
	children: React.ReactNode;
	fallback?: React.ReactNode;
}): JSX.Element {
	const defaultFallback = React.createElement('div', null, 'Loading...');
	return React.createElement(React.Suspense, { fallback: fallback || defaultFallback }, children);
}

/**
 * Hook for loading factory components with loading state
 */
export function useFactoryComponent<T>(
	name: string,
	loader: () => Promise<T>
): {
	component: T | null;
	loading: boolean;
	error: Error | null;
} {
	const [component, setComponent] = React.useState<T | null>(null);
	const [loading, setLoading] = React.useState(false);
	const [error, setError] = React.useState<Error | null>(null);

	React.useEffect(() => {
		let cancelled = false;

		const loadComponent = async () => {
			setLoading(true);
			setError(null);

			try {
				const loadedComponent = await FactoryRegistry.load(name, loader);
				if (!cancelled) {
					setComponent(loadedComponent);
				}
			} catch (err) {
				if (!cancelled) {
					setError(err instanceof Error ? err : new Error('Failed to load component'));
				}
			} finally {
				if (!cancelled) {
					setLoading(false);
				}
			}
		};

		loadComponent();

		return () => {
			cancelled = true;
		};
	}, [name, loader]);

	return { component, loading, error };
}

// Re-export React for convenience
import * as React from 'react';
export { React };
