/**
 * Heavy Factory Components Entry Point
 * These components have heavy dependencies and should be loaded on demand
 * Separate bundle to avoid bloating the main library
 *
 * Bundle Size Target: <250KB gzipped
 * Dependencies: Heavy (Lexical, Vidstack), loaded on-demand only
 * Tree-shaking: Optimized for selective imports
 * Usage: import("@miss-ui/web/factories-heavy").then(({ FactoryEditor }) => ...)
 */

// Editor Factory - Heavy weight due to Lexical dependencies
export {
	Editor as FactoryEditor,
	useEditor as useFactoryEditor,
} from './components/Factory.Editor';

export type {
	EditorProps as FactoryEditorProps,
	EditorToolbarProps as FactoryEditorToolbarProps,
	EditorToolbarButton as FactoryEditorToolbarButton,
	EditorToolbarGroup as FactoryEditorToolbarGroup,
	EditorContextValue as FactoryEditorContextValue,
	EditorConfig as FactoryEditorConfig,
	EditorPlugin as FactoryEditorPlugin,
	EditorMode as FactoryEditorMode,
	EditorSize as FactoryEditorSize,
	EditorTheme as FactoryEditorTheme,
	ToolbarPosition as FactoryEditorToolbarPosition,
} from './components/Factory.Editor';

// Player Factory - Heavy weight due to Vidstack dependencies
export {
	Player as FactoryPlayer,
} from './components/Factory.Player';
export type {
	PlayerProps as FactoryPlayerProps,
	PlayerState as FactoryPlayerState,
	PlayerSource as FactoryPlayerSource,
	PlayerTrack as FactoryPlayerTrack,
	PlayerChapter as FactoryPlayerChapter,
	PlayerControls as FactoryPlayerControls,
	PlayerQuality as FactoryPlayerQuality,
	PlayerPlaybackRate as FactoryPlayerPlaybackRate,
	PlayerVolume as FactoryPlayerVolume,
	PlayerKeyboardShortcuts as FactoryPlayerKeyboardShortcuts,
	PlayerEventHandlers as FactoryPlayerEventHandlers,
} from './components/Factory.Player';
