/**
 * Collection Components Entry Point
 * Grouped content and indicators
 *
 * Usage: import { CollectionIndicator, CollectionDivider, CollectionCode } from "@miss-ui/web/collections"
 */

// Collection.Indicator components
export {
	Indicator as CollectionIndicator,
	Loading as CollectionIndicatorLoading,
	Progress as CollectionIndicatorProgress,
	Placeholder as CollectionIndicatorPlaceholder,
	Overlay as CollectionIndicatorOverlay,
} from './components/Collection.Indicator';
export type {
	LoadingProps as CollectionIndicatorLoadingProps,
	ProgressProps as CollectionIndicatorProgressProps,
	PlaceholderProps as CollectionIndicatorPlaceholderProps,
	OverlayProps as CollectionIndicatorOverlayProps,
	LoadingType as CollectionIndicatorLoadingType,
} from './components/Collection.Indicator';

// Collection.Divider components
export {
	Divider as CollectionDivider,
} from './components/Collection.Divider';
export type {
	DividerProps as CollectionDividerProps,
	DividerOrientation as CollectionDividerOrientation,
	DividerSize as CollectionDividerSize,
	DividerVariant as CollectionDividerVariant,
	DividerColor as CollectionDividerColor,
} from './components/Collection.Divider';

// Collection.Code components
export {
	default as CollectionCode,
	Code as CollectionCodeBase,
	CodeBlock as CollectionCodeBlock,
	CodeHighlight as CollectionCodeHighlight,
	CodePreview as CollectionCodePreview,
} from './components/Collection.Code';
export type {
	CodeProps as CollectionCodeProps,
	CodeBlockProps as CollectionCodeBlockProps,
	CodeHighlightProps as CollectionCodeHighlightProps,
	CodePreviewProps as CollectionCodePreviewProps,
	SupportedLanguage as CollectionCodeSupportedLanguage,
	CodeTheme as CollectionCodeTheme,
	CodeSize as CollectionCodeSize,
} from './components/Collection.Code';
