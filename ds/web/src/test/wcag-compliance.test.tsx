import { describe, it, expect, beforeEach } from 'vitest';
import { render, screen, cleanup } from '@testing-library/react';
import { userEvent } from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import type { AxeResults, RunOptions } from 'axe-core';

// Import components for WCAG compliance testing
import { Button } from '@/components/Element.Button';
import { Field } from '@/components/Base/Field';
import { Menu } from '@/components/Element.Menu';
import { List } from '@/components/Element.List';
import { Table } from '@/components/Element.Table';
import { Text } from '@/components/Core.Text';
import { Media } from '@/components/Element.Media';
import { Icon } from '@/components/Element.Icon';
import { Card } from '@/components/Element.Card';
import { Message } from '@/components/Element.Message';
import { Tabs } from '@/components/Module.Tabs';
import { Dropdown } from '@/components/Module.Dropdown';
import { Popover } from '@/components/Module.Popover';
import { Modal } from '@/components/Factory.Modal';
import { Form } from '@/components/Factory.Form';
import { DataTable } from '@/components/Factory.DataTable';
import { Toast } from '@/components/Factory.Toast';
import { Indicator } from '@/components/Collection.Indicator';
import { Pagination } from '@/components/Module.Pagination';
import { Slider } from '@/components/Module.Slider';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

declare global
{
	namespace Vi
	{
		interface JestAssertion<T = any>
		{
			toHaveNoViolations(): T;
		}
	}
}

/**
 * WCAG 2.1 AA Compliance Testing
 * Tests all interactive elements for WCAG compliance including:
 * - Perceivable: Alt text, color contrast, text scaling
 * - Operable: Keyboard access, timing, seizures
 * - Understandable: Readable, predictable, input assistance
 * - Robust: Compatible with assistive technologies
 */
describe('WCAG 2.1 AA Compliance Testing', () =>
{
	let user: ReturnType<typeof userEvent.setup>;

	beforeEach(() =>
	{
		user = userEvent.setup();
		cleanup();
	});

	/**
	 * WCAG axe configuration for comprehensive testing
	 */
	const wcagAxeConfig: RunOptions = {
		rules: {
			// WCAG 2.1 Level A rules
			'aria-allowed-attr': { enabled: true },
			'aria-required-attr': { enabled: true },
			'aria-valid-attr': { enabled: true },
			'aria-valid-attr-value': { enabled: true },
			'button-name': { enabled: true },
			'duplicate-id': { enabled: true },
			'form-field-multiple-labels': { enabled: true },
			'html-has-lang': { enabled: true },
			'image-alt': { enabled: true },
			'input-image-alt': { enabled: true },
			'label': { enabled: true },
			'link-name': { enabled: true },
			'list': { enabled: true },
			'listitem': { enabled: true },
			'nested-interactive': { enabled: true },
			'no-autoplay-audio': { enabled: true },
			'role-img-alt': { enabled: true },
			'scrollable-region-focusable': { enabled: true },
			'select-name': { enabled: true },
			'server-side-image-map': { enabled: true },
			'svg-img-alt': { enabled: true },
			'td-headers-attr': { enabled: true },
			'th-has-data-cells': { enabled: true },
			'valid-lang': { enabled: true },

			// WCAG 2.1 Level AA rules
			'bypass': { enabled: true },
			'document-title': { enabled: true },
			'focus-order-semantics': { enabled: true },
			'heading-order': { enabled: true },
			'landmark-banner-is-top-level': { enabled: true },
			'landmark-contentinfo-is-top-level': { enabled: true },
			'landmark-main-is-top-level': { enabled: true },
			'landmark-no-duplicate-banner': { enabled: true },
			'landmark-no-duplicate-contentinfo': { enabled: true },
			'landmark-one-main': { enabled: true },
			'page-has-heading-one': { enabled: true },
			'region': { enabled: true },
			'tabindex': { enabled: true },

			// Disable problematic rules in test environment
			'color-contrast': { enabled: false }, // Unreliable in jsdom
			'meta-viewport': { enabled: false }, // Not applicable to components
		},
		tags: ['wcag2a', 'wcag2aa', 'wcag21aa'],
	};

	/**
	 * Perceivable - Information and UI components must be presentable to users in ways they can perceive
	 */
	describe('Perceivable (WCAG 2.1 Guideline 1)', () =>
	{
		describe('1.1 Text Alternatives', () =>
		{
			it('should provide text alternatives for non-text content', async () =>
			{
				const { container } = render(
					<div>
						<Media.Image src="/test.jpg" alt="A scenic mountain landscape at sunset" />
						<Media.Video src="/intro.mp4" controls>
							<Media.Track kind="captions" src="/captions.vtt" label="English" />
						</Media.Video>
						<Icon aria-label="Settings menu" name="gear" />
						<Button aria-label="Close dialog">×</Button>
						<Media.Audio src="/podcast.mp3" controls aria-label="Tech podcast episode" />
					</div>,
				);

				const results = await axe(container, wcagAxeConfig);
				expect(results).toHaveNoViolations();

				// Verify specific alt text requirements
				const image = screen.getByRole('img');
				const icon = screen.getByLabelText('Settings menu');
				const closeButton = screen.getByLabelText('Close dialog');
				const audio = screen.getByLabelText('Tech podcast episode');

				expect(image).toHaveAccessibleName('A scenic mountain landscape at sunset');
				expect(icon).toHaveAccessibleName('Settings menu');
				expect(closeButton).toHaveAccessibleName('Close dialog');
				expect(audio).toHaveAccessibleName('Tech podcast episode');
			});

			it('should handle decorative images correctly', async () =>
			{
				const { container } = render(
					<div>
						<Media.Image src="/decoration.jpg" alt="" role="presentation" />
						<Media.Image src="/border.png" alt="" aria-hidden="true" />
						<Icon decorative name="star" aria-hidden="true" />
					</div>,
				);

				const results = await axe(container, wcagAxeConfig);
				expect(results).toHaveNoViolations();
			});
		});

		describe('1.3 Adaptable', () =>
		{
			it('should provide proper semantic structure', async () =>
			{
				const { container } = render(
					<div>
						<Text.Heading level={1}>Main Title</Text.Heading>
						<Text.Heading level={2}>Section Title</Text.Heading>
						<Text.Paragraph>Content paragraph</Text.Paragraph>
						<List>
							<List.Item>First item</List.Item>
							<List.Item>Second item</List.Item>
						</List>
						<Table>
							<Table.Header>
								<Table.Row>
									<Table.HeaderCell>Name</Table.HeaderCell>
									<Table.HeaderCell>Value</Table.HeaderCell>
								</Table.Row>
							</Table.Header>
							<Table.Body>
								<Table.Row>
									<Table.Cell>Item 1</Table.Cell>
									<Table.Cell>100</Table.Cell>
								</Table.Row>
							</Table.Body>
						</Table>
					</div>,
				);

				const results = await axe(container, wcagAxeConfig);
				expect(results).toHaveNoViolations();

				// Verify semantic structure
				const h1 = screen.getByRole('heading', { level: 1 });
				const h2 = screen.getByRole('heading', { level: 2 });
				const list = screen.getByRole('list');
				const table = screen.getByRole('table');

				expect(h1).toBeInTheDocument();
				expect(h2).toBeInTheDocument();
				expect(list).toBeInTheDocument();
				expect(table).toBeInTheDocument();
			});

			it('should maintain meaning when CSS is disabled', () =>
			{
				render(
					<div>
						<Text.Heading level={1}>Important Title</Text.Heading>
						<Text.Paragraph>
							This is <Text.Mark>highlighted text</Text.Mark> and this is{' '}
							<strong>important text</strong>.
						</Text.Paragraph>
						<List>
							<List.Item>
								<strong>Step 1:</strong> First action
							</List.Item>
							<List.Item>
								<strong>Step 2:</strong> Second action
							</List.Item>
						</List>
					</div>,
				);

				// Content should be meaningful without CSS
				const title = screen.getByRole('heading', { level: 1 });
				const strongElements = screen.getAllByText(/Step \d:/);

				expect(title).toHaveTextContent('Important Title');
				expect(strongElements).toHaveLength(2);
			});
		});

		describe('1.4 Distinguishable', () =>
		{
			it('should not rely solely on color to convey information', () =>
			{
				render(
					<div>
						<Message variant="error" icon="×">
							Error: Please fix the issues below
						</Message>
						<Message variant="success" icon="✓">
							Success: Changes saved successfully
						</Message>
						<Message variant="warning" icon="⚠">
							Warning: Please review your input
						</Message>
						<Field.Text
							label="Email"
							error="Invalid email format"
							required
							aria-invalid="true"
						/>
						<Button disabled aria-disabled="true">
							Disabled Button
						</Button>
					</div>,
				);

				// Messages should have icons, not just color
				const errorMessage = screen.getByText(/Error:/);
				const successMessage = screen.getByText(/Success:/);
				const warningMessage = screen.getByText(/Warning:/);

				expect(errorMessage.parentElement).toContainHTML('×');
				expect(successMessage.parentElement).toContainHTML('✓');
				expect(warningMessage.parentElement).toContainHTML('⚠');

				// Form validation should use attributes, not just color
				const emailInput = screen.getByLabelText('Email');
				expect(emailInput).toHaveAttribute('aria-invalid', 'true');
				expect(emailInput).toBeRequired();

				// Disabled state should use attributes
				const disabledButton = screen.getByRole('button');
				expect(disabledButton).toHaveAttribute('aria-disabled', 'true');
			});

			it('should provide sufficient focus indicators', async () =>
			{
				render(
					<div>
						<Button>Focusable Button</Button>
						<Field.Text label="Focusable Input" />
						<Text.Link href="#test">Focusable Link</Text.Link>
						<Field.Checkbox label="Focusable Checkbox" />
					</div>,
				);

				const button = screen.getByRole('button');
				const input = screen.getByRole('textbox');
				const link = screen.getByRole('link');
				const checkbox = screen.getByRole('checkbox');

				// All focusable elements should be able to receive focus
				button.focus();
				expect(button).toHaveFocus();

				input.focus();
				expect(input).toHaveFocus();

				link.focus();
				expect(link).toHaveFocus();

				checkbox.focus();
				expect(checkbox).toHaveFocus();
			});
		});
	});

	/**
	 * Operable - UI components and navigation must be operable
	 */
	describe('Operable (WCAG 2.1 Guideline 2)', () =>
	{
		describe('2.1 Keyboard Accessible', () =>
		{
			it('should make all functionality available via keyboard', async () =>
			{
				const handleClick = vi.fn();
				const handleSubmit = vi.fn();

				render(
					<Form onSubmit={handleSubmit}>
						<Field.Text label="Name" />
						<Field.Select label="Country" options={[{ value: 'us', label: 'US' }]} />
						<Field.Checkbox label="Subscribe" />
						<Button onClick={handleClick}>Click Me</Button>
						<Button type="submit">Submit</Button>
					</Form>,
				);

				const nameInput = screen.getByLabelText('Name');
				const countrySelect = screen.getByLabelText('Country');
				const checkbox = screen.getByLabelText('Subscribe');
				const clickButton = screen.getByRole('button', { name: 'Click Me' });
				const submitButton = screen.getByRole('button', { name: 'Submit' });

				// All elements should be keyboard accessible
				nameInput.focus();
				expect(nameInput).toHaveFocus();

				await user.keyboard('{Tab}');
				expect(countrySelect).toHaveFocus();

				await user.keyboard('{Tab}');
				expect(checkbox).toHaveFocus();

				// Space should toggle checkbox
				await user.keyboard(' ');
				expect(checkbox).toBeChecked();

				await user.keyboard('{Tab}');
				expect(clickButton).toHaveFocus();

				// Enter should activate button
				await user.keyboard('{Enter}');
				expect(handleClick).toHaveBeenCalled();

				await user.keyboard('{Tab}');
				expect(submitButton).toHaveFocus();
			});

			it('should not trap keyboard focus unintentionally', async () =>
			{
				render(
					<div>
						<Button>Before</Button>
						<Dropdown>
							<Dropdown.Trigger>Menu</Dropdown.Trigger>
							<Dropdown.Content>
								<Dropdown.Item>Item 1</Dropdown.Item>
								<Dropdown.Item>Item 2</Dropdown.Item>
							</Dropdown.Content>
						</Dropdown>
						<Button>After</Button>
					</div>,
				);

				const beforeButton = screen.getByRole('button', { name: 'Before' });
				const menuTrigger = screen.getByRole('button', { name: 'Menu' });
				const afterButton = screen.getByRole('button', { name: 'After' });

				// Should be able to tab through normally when dropdown is closed
				beforeButton.focus();
				await user.keyboard('{Tab}');
				expect(menuTrigger).toHaveFocus();

				await user.keyboard('{Tab}');
				expect(afterButton).toHaveFocus();
			});

			it('should properly trap focus in modal dialogs', async () =>
			{
				render(
					<Modal open>
						<Modal.Content>
							<Modal.Header>
								<Modal.Title>Test Modal</Modal.Title>
								<Modal.Close>×</Modal.Close>
							</Modal.Header>
							<Modal.Body>
								<Field.Text label="Input" />
								<Button>Save</Button>
							</Modal.Body>
						</Modal.Content>
					</Modal>,
				);

				const closeButton = screen.getByRole('button', { name: '×' });
				const input = screen.getByRole('textbox');
				const saveButton = screen.getByRole('button', { name: 'Save' });

				// Focus should cycle within modal
				closeButton.focus();
				expect(closeButton).toHaveFocus();

				await user.keyboard('{Tab}');
				expect(input).toHaveFocus();

				await user.keyboard('{Tab}');
				expect(saveButton).toHaveFocus();

				// Tab from last element should return to first
				await user.keyboard('{Tab}');
				expect(closeButton).toHaveFocus();
			});
		});

		describe('2.2 Enough Time', () =>
		{
			it('should provide controls for time-sensitive content', () =>
			{
				render(
					<div>
						<Toast autoClose={false}>
							Persistent notification
						</Toast>
						<Message variant="warning">
							Session expires in 10 minutes.{' '}
							<Button size="sm">Extend Session</Button>
						</Message>
						<Indicator.Loading aria-label="Loading content" />
					</div>,
				);

				const extendButton = screen.getByRole('button', { name: 'Extend Session' });
				const loadingIndicator = screen.getByLabelText('Loading content');

				expect(extendButton).toBeInTheDocument();
				expect(loadingIndicator).toBeInTheDocument();
			});

			it('should not auto-refresh content without user control', () =>
			{
				// Components should not auto-refresh by default
				render(
					<div>
						<DataTable data={[]} columns={[]} />
						<List>
							<List.Item>Static content</List.Item>
						</List>
					</div>,
				);

				// No automatic refresh mechanisms should be present
				const table = screen.getByRole('table');
				const list = screen.getByRole('list');

				expect(table).toBeInTheDocument();
				expect(list).toBeInTheDocument();
			});
		});

		describe('2.3 Seizures and Physical Reactions', () =>
		{
			it('should not contain content that flashes more than 3 times per second', () =>
			{
				render(
					<div>
						<Indicator.Loading />
						<Button loading>Processing...</Button>
						<Transition>
							<div>Animated content</div>
						</Transition>
					</div>,
				);

				// Animations should be subtle and not cause seizures
				const loadingIndicator = screen.getByRole('status');
				const loadingButton = screen.getByRole('button');

				expect(loadingIndicator).toBeInTheDocument();
				expect(loadingButton).toBeInTheDocument();
			});
		});

		describe('2.4 Navigable', () =>
		{
			it('should provide multiple ways to locate content', () =>
			{
				render(
					<div>
						<nav role="navigation" aria-label="Main navigation">
							<Menu>
								<Menu.Item>Home</Menu.Item>
								<Menu.Item>Products</Menu.Item>
								<Menu.Item>Contact</Menu.Item>
							</Menu>
						</nav>
						<nav role="navigation" aria-label="Breadcrumb">
							<List>
								<List.Item>
									<Text.Link href="/">Home</Text.Link>
								</List.Item>
								<List.Item>
									<Text.Link href="/products">Products</Text.Link>
								</List.Item>
								<List.Item aria-current="page">Current Page</List.Item>
							</List>
						</nav>
					</div>,
				);

				const mainNav = screen.getByRole('navigation', { name: 'Main navigation' });
				const breadcrumb = screen.getByRole('navigation', { name: 'Breadcrumb' });

				expect(mainNav).toBeInTheDocument();
				expect(breadcrumb).toBeInTheDocument();
			});

			it('should provide descriptive headings and labels', () =>
			{
				render(
					<div>
						<Text.Heading level={1}>User Profile Settings</Text.Heading>
						<Text.Heading level={2}>Personal Information</Text.Heading>
						<Field.Text label="Full Name" />
						<Field.Text label="Email Address" />
						<Text.Heading level={2}>Privacy Settings</Text.Heading>
						<Field.Checkbox label="Allow marketing emails" />
					</div>,
				);

				const mainHeading = screen.getByRole('heading', { level: 1 });
				const sectionHeadings = screen.getAllByRole('heading', { level: 2 });
				const nameInput = screen.getByLabelText('Full Name');
				const emailInput = screen.getByLabelText('Email Address');

				expect(mainHeading).toHaveTextContent('User Profile Settings');
				expect(sectionHeadings).toHaveLength(2);
				expect(nameInput).toHaveAccessibleName('Full Name');
				expect(emailInput).toHaveAccessibleName('Email Address');
			});

			it('should provide skip links for keyboard navigation', () =>
			{
				render(
					<div>
						<a href="#main-content" className="skip-link">
							Skip to main content
						</a>
						<nav>
							<Menu>
								<Menu.Item>Navigation item</Menu.Item>
							</Menu>
						</nav>
						<main id="main-content">
							<Text.Heading level={1}>Main Content</Text.Heading>
						</main>
					</div>,
				);

				const skipLink = screen.getByRole('link', { name: 'Skip to main content' });
				const mainContent = screen.getByRole('main');

				expect(skipLink).toHaveAttribute('href', '#main-content');
				expect(mainContent).toHaveAttribute('id', 'main-content');
			});
		});

		describe('2.5 Input Modalities', () =>
		{
			it('should support multiple input methods', async () =>
			{
				const handleClick = vi.fn();

				render(
					<div>
						<Button onClick={handleClick}>Multi-input Button</Button>
						<Field.Text label="Text Input" />
						<Slider defaultValue={50} min={0} max={100} />
					</div>,
				);

				const button = screen.getByRole('button');
				const textInput = screen.getByRole('textbox');
				const slider = screen.getByRole('slider');

				// Mouse interaction
				await user.click(button);
				expect(handleClick).toHaveBeenCalledTimes(1);

				// Keyboard interaction
				button.focus();
				await user.keyboard('{Enter}');
				expect(handleClick).toHaveBeenCalledTimes(2);

				// Touch/pointer interaction (simulated)
				await user.click(textInput);
				expect(textInput).toHaveFocus();

				// Keyboard navigation for slider
				slider.focus();
				await user.keyboard('{ArrowRight}');
				expect(slider).toHaveValue('51');
			});
		});
	});

	/**
	 * Understandable - Information and the operation of UI must be understandable
	 */
	describe('Understandable (WCAG 2.1 Guideline 3)', () =>
	{
		describe('3.1 Readable', () =>
		{
			it('should specify the language of content', () =>
			{
				render(
					<div lang="en">
						<Text.Paragraph>English content</Text.Paragraph>
						<Text.Paragraph lang="es">Contenido en español</Text.Paragraph>
						<Text.Paragraph lang="fr">Contenu français</Text.Paragraph>
					</div>,
				);

				const container = screen.getByText('English content').closest('div');
				const spanishText = screen.getByText('Contenido en español');
				const frenchText = screen.getByText('Contenu français');

				expect(container).toHaveAttribute('lang', 'en');
				expect(spanishText).toHaveAttribute('lang', 'es');
				expect(frenchText).toHaveAttribute('lang', 'fr');
			});
		});

		describe('3.2 Predictable', () =>
		{
			it('should not change context on focus', async () =>
			{
				const handleFocus = vi.fn();

				render(
					<div>
						<Field.Text label="Name" onFocus={handleFocus} />
						<Field.Select
							label="Country"
							options={[{ value: 'us', label: 'US' }]}
							onFocus={handleFocus}
						/>
						<Button onFocus={handleFocus}>Submit</Button>
					</div>,
				);

				const nameInput = screen.getByLabelText('Name');
				const countrySelect = screen.getByLabelText('Country');
				const button = screen.getByRole('button');

				// Focusing elements should not change context unexpectedly
				nameInput.focus();
				expect(handleFocus).toHaveBeenCalledTimes(1);
				expect(document.activeElement).toBe(nameInput);

				countrySelect.focus();
				expect(handleFocus).toHaveBeenCalledTimes(2);
				expect(document.activeElement).toBe(countrySelect);

				button.focus();
				expect(handleFocus).toHaveBeenCalledTimes(3);
				expect(document.activeElement).toBe(button);
			});

			it('should provide consistent navigation', () =>
			{
				render(
					<div>
						<nav role="navigation" aria-label="Primary">
							<Menu>
								<Menu.Item>Home</Menu.Item>
								<Menu.Item>About</Menu.Item>
								<Menu.Item>Contact</Menu.Item>
							</Menu>
						</nav>
						<main>
							<Text.Heading level={1}>Page Content</Text.Heading>
						</main>
						<nav role="navigation" aria-label="Footer">
							<Menu>
								<Menu.Item>Privacy</Menu.Item>
								<Menu.Item>Terms</Menu.Item>
								<Menu.Item>Support</Menu.Item>
							</Menu>
						</nav>
					</div>,
				);

				const primaryNav = screen.getByRole('navigation', { name: 'Primary' });
				const footerNav = screen.getByRole('navigation', { name: 'Footer' });

				expect(primaryNav).toBeInTheDocument();
				expect(footerNav).toBeInTheDocument();
			});
		});

		describe('3.3 Input Assistance', () =>
		{
			it('should identify and describe input errors', () =>
			{
				render(
					<Form>
						<Field.Text
							label="Email Address"
							error="Please enter a valid email address"
							required
							aria-invalid="true"
						/>
						<Field.Select
							label="Country"
							error="Please select a country"
							options={[]}
							required
							aria-invalid="true"
						/>
						<div role="alert" aria-live="assertive">
							Please fix the errors above before submitting
						</div>
					</Form>,
				);

				const emailInput = screen.getByLabelText('Email Address');
				const countrySelect = screen.getByLabelText('Country');
				const errorSummary = screen.getByRole('alert');

				expect(emailInput).toHaveAttribute('aria-invalid', 'true');
				expect(emailInput).toHaveAccessibleDescription();
				expect(countrySelect).toHaveAttribute('aria-invalid', 'true');
				expect(countrySelect).toHaveAccessibleDescription();
				expect(errorSummary).toHaveAttribute('aria-live', 'assertive');
			});

			it('should provide labels and instructions for inputs', () =>
			{
				render(
					<Form>
						<Field.Text
							label="Password"
							type="password"
							required
							helperText="Must be at least 8 characters with one number"
						/>
						<Field.Text
							label="Confirm Password"
							type="password"
							required
							helperText="Must match the password above"
						/>
						<Field.Checkbox
							label="I agree to the terms and conditions"
							required
						/>
					</Form>,
				);

				const passwordInput = screen.getByLabelText('Password');
				const confirmInput = screen.getByLabelText('Confirm Password');
				const checkbox = screen.getByLabelText('I agree to the terms and conditions');

				expect(passwordInput).toBeRequired();
				expect(passwordInput).toHaveAccessibleDescription();
				expect(confirmInput).toBeRequired();
				expect(confirmInput).toHaveAccessibleDescription();
				expect(checkbox).toBeRequired();
			});

			it('should prevent and help users avoid mistakes', () =>
			{
				render(
					<Form>
						<Field.Text
							label="Credit Card Number"
							pattern="[0-9]{16}"
							maxLength={16}
							helperText="Enter 16 digits without spaces"
						/>
						<Field.Text
							label="Expiry Date"
							placeholder="MM/YY"
							pattern="[0-9]{2}/[0-9]{2}"
							helperText="Format: MM/YY"
						/>
						<Button type="submit">
							Process Payment
						</Button>
						<div role="alert" style={{ display: 'none' }} id="payment-errors">
							Payment processing errors will appear here
						</div>
					</Form>,
				);

				const cardInput = screen.getByLabelText('Credit Card Number');
				const expiryInput = screen.getByLabelText('Expiry Date');

				expect(cardInput).toHaveAttribute('maxlength', '16');
				expect(cardInput).toHaveAccessibleDescription();
				expect(expiryInput).toHaveAttribute('placeholder', 'MM/YY');
				expect(expiryInput).toHaveAccessibleDescription();
			});
		});
	});

	/**
	 * Robust - Content must be robust enough to be interpreted by a wide variety of user agents
	 */
	describe('Robust (WCAG 2.1 Guideline 4)', () =>
	{
		describe('4.1 Compatible', () =>
		{
			it('should use valid HTML and ARIA', async () =>
			{
				const { container } = render(
					<div>
						<Button aria-label="Close">×</Button>
						<Field.Text label="Name" required />
						<Menu>
							<Menu.Item>Item 1</Menu.Item>
							<Menu.Item>Item 2</Menu.Item>
						</Menu>
						<Table>
							<Table.Header>
								<Table.Row>
									<Table.HeaderCell>Header</Table.HeaderCell>
								</Table.Row>
							</Table.Header>
							<Table.Body>
								<Table.Row>
									<Table.Cell>Data</Table.Cell>
								</Table.Row>
							</Table.Body>
						</Table>
					</div>,
				);

				const results = await axe(container, wcagAxeConfig);
				expect(results).toHaveNoViolations();
			});

			it('should provide proper name, role, and value for all UI components', () =>
			{
				render(
					<div>
						<Button role="button" aria-label="Save document">
							Save
						</Button>
						<Field.Text
							role="textbox"
							aria-label="User name"
							aria-required="true"
						/>
						<Field.Checkbox
							role="checkbox"
							aria-label="Subscribe to newsletter"
							aria-checked="false"
						/>
						<Slider
							role="slider"
							aria-label="Volume"
							aria-valuemin="0"
							aria-valuemax="100"
							aria-valuenow="50"
						/>
					</div>,
				);

				const button = screen.getByRole('button');
				const textbox = screen.getByRole('textbox');
				const checkbox = screen.getByRole('checkbox');
				const slider = screen.getByRole('slider');

				// Check names
				expect(button).toHaveAccessibleName();
				expect(textbox).toHaveAccessibleName();
				expect(checkbox).toHaveAccessibleName();
				expect(slider).toHaveAccessibleName();

				// Check roles
				expect(button).toHaveAttribute('role', 'button');
				expect(textbox).toHaveAttribute('role', 'textbox');
				expect(checkbox).toHaveAttribute('role', 'checkbox');
				expect(slider).toHaveAttribute('role', 'slider');

				// Check values/states
				expect(textbox).toHaveAttribute('aria-required', 'true');
				expect(checkbox).toHaveAttribute('aria-checked', 'false');
				expect(slider).toHaveAttribute('aria-valuenow', '50');
			});

			it('should work with assistive technologies', () =>
			{
				render(
					<div>
						<Modal open>
							<Modal.Content>
								<Modal.Header>
									<Modal.Title>Accessible Modal</Modal.Title>
								</Modal.Header>
								<Modal.Body>
									<p>Modal content</p>
								</Modal.Body>
							</Modal.Content>
						</Modal>
						<Dropdown>
							<Dropdown.Trigger aria-haspopup="menu">
								Menu
							</Dropdown.Trigger>
							<Dropdown.Content role="menu">
								<Dropdown.Item role="menuitem">Option 1</Dropdown.Item>
							</Dropdown.Content>
						</Dropdown>
					</div>,
				);

				const modal = screen.getByRole('dialog');
				const menuButton = screen.getByRole('button', { name: 'Menu' });

				expect(modal).toHaveAttribute('aria-modal', 'true');
				expect(menuButton).toHaveAttribute('aria-haspopup', 'menu');
			});
		});
	});

	/**
	 * Comprehensive WCAG compliance test
	 */
	describe('Comprehensive WCAG Compliance', () =>
	{
		it('should pass all WCAG 2.1 AA tests for a complete form', async () =>
		{
			const { container } = render(
				<div lang="en">
					<Text.Heading level={1}>User Registration Form</Text.Heading>
					<Form>
						<Text.Heading level={2}>Personal Information</Text.Heading>
						<Field.Text
							label="Full Name"
							required
							helperText="Enter your first and last name"
						/>
						<Field.Text
							label="Email Address"
							type="email"
							required
							helperText="We'll use this to contact you"
						/>
						<Field.Text
							label="Phone Number"
							type="tel"
							helperText="Optional: Include country code"
						/>

						<Text.Heading level={2}>Account Settings</Text.Heading>
						<Field.Text
							label="Password"
							type="password"
							required
							helperText="Must be at least 8 characters"
						/>
						<Field.Select
							label="Country"
							required
							options={[
								{ value: 'us', label: 'United States' },
								{ value: 'ca', label: 'Canada' },
							]}
						/>

						<Text.Heading level={2}>Preferences</Text.Heading>
						<Field.Checkbox label="Send me marketing emails" />
						<Field.Radio name="theme" value="light" label="Light theme" />
						<Field.Radio name="theme" value="dark" label="Dark theme" />

						<Button type="submit">Create Account</Button>
						<Button type="button" variant="secondary">Cancel</Button>
					</Form>
				</div>,
			);

			const results = await axe(container, wcagAxeConfig);
			expect(results).toHaveNoViolations();

			// Additional manual checks
			const form = screen.getByRole('form') || container.querySelector('form');
			const headings = screen.getAllByRole('heading');
			const requiredInputs = screen.getAllByRole('textbox', { name: /required/i });
			const buttons = screen.getAllByRole('button');

			expect(form).toBeInTheDocument();
			expect(headings).toHaveLength(3); // h1 + 2 h2s
			expect(buttons).toHaveLength(2);
		});
	});
});
