import { describe, it, expect, beforeEach } from 'vitest';
import { render, screen, cleanup } from '@testing-library/react';
import { userEvent } from '@testing-library/user-event';

// Import components for screen reader testing
import { Button } from '@/components/Element.Button';
import { Field } from '@/components/Base/Field';
import { Menu } from '@/components/Element.Menu';
import { List } from '@/components/Element.List';
import { Table } from '@/components/Element.Table';
import { Text } from '@/components/Core.Text';
import { Media } from '@/components/Element.Media';
import { Icon } from '@/components/Element.Icon';
import { Card } from '@/components/Element.Card';
import { Message } from '@/components/Element.Message';
import { Tabs } from '@/components/Module.Tabs';
import { Dropdown } from '@/components/Module.Dropdown';
import { Popover } from '@/components/Module.Popover';
import { Modal } from '@/components/Factory.Modal';
import { Form } from '@/components/Factory.Form';
import { DataTable } from '@/components/Factory.DataTable';
import { Toast } from '@/components/Factory.Toast';
import { Indicator } from '@/components/Collection.Indicator';
import { Pagination } from '@/components/Module.Pagination';
import { Slider } from '@/components/Module.Slider';

/**
 * Screen reader compatibility testing
 * Tests ARIA attributes, semantic HTML, accessible names, descriptions, and state announcements
 */
describe('Screen Reader Compatibility Testing', () =>
{
	let user: ReturnType<typeof userEvent.setup>;

	beforeEach(() =>
	{
		user = userEvent.setup();
		cleanup();
	});

	/**
	 * ARIA attributes and roles testing
	 */
	describe('ARIA Attributes and Roles', () =>
	{
		it('should have proper ARIA roles for interactive elements', () =>
		{
			render(
				<div>
					<Button>Click me</Button>
					<Button role="switch" aria-checked="false">Toggle</Button>
					<Menu>
						<Menu.Item>Home</Menu.Item>
						<Menu.Item>About</Menu.Item>
					</Menu>
					<Tabs defaultValue="tab1">
						<Tabs.List>
							<Tabs.Trigger value="tab1">Tab 1</Tabs.Trigger>
							<Tabs.Trigger value="tab2">Tab 2</Tabs.Trigger>
						</Tabs.List>
						<Tabs.Content value="tab1">Content 1</Tabs.Content>
						<Tabs.Content value="tab2">Content 2</Tabs.Content>
					</Tabs>
				</div>,
			);

			// Check button roles
			const buttons = screen.getAllByRole('button');
			expect(buttons).toHaveLength(3); // Regular button + toggle + tab triggers

			// Check menu roles
			const menu = screen.getByRole('menu');
			const menuItems = screen.getAllByRole('menuitem');
			expect(menu).toBeInTheDocument();
			expect(menuItems).toHaveLength(2);

			// Check tab roles
			const tabList = screen.getByRole('tablist');
			const tabs = screen.getAllByRole('tab');
			const tabPanels = screen.getAllByRole('tabpanel');
			expect(tabList).toBeInTheDocument();
			expect(tabs).toHaveLength(2);
			expect(tabPanels).toHaveLength(1); // Only active panel is rendered
		});

		it('should have proper ARIA attributes for form elements', () =>
		{
			render(
				<Form>
					<Field.Text
						label="Email Address"
						placeholder="Enter your email"
						required
						error="Please enter a valid email"
						helperText="We'll never share your email"
					/>
					<Field.Select
						label="Country"
						options={[
							{ value: 'us', label: 'United States' },
							{ value: 'ca', label: 'Canada' },
						]}
						required
					/>
					<Field.Checkbox label="I agree to the terms and conditions" />
					<Field.Radio name="newsletter" value="yes" label="Yes, send me newsletters" />
					<Field.Radio name="newsletter" value="no" label="No, don't send newsletters" />
				</Form>,
			);

			const emailInput = screen.getByLabelText('Email Address');
			const countrySelect = screen.getByLabelText('Country');
			const checkbox = screen.getByLabelText('I agree to the terms and conditions');
			const radioButtons = screen.getAllByRole('radio');

			// Check required attributes
			expect(emailInput).toBeRequired();
			expect(emailInput).toHaveAttribute('aria-required', 'true');
			expect(countrySelect).toBeRequired();
			expect(countrySelect).toHaveAttribute('aria-required', 'true');

			// Check error states
			expect(emailInput).toHaveAttribute('aria-invalid', 'true');
			expect(emailInput).toHaveAccessibleDescription();

			// Check radio group
			expect(radioButtons).toHaveLength(2);
			radioButtons.forEach((radio) =>
			{
				expect(radio).toHaveAttribute('name', 'newsletter');
			});
		});

		it('should have proper ARIA attributes for data display components', () =>
		{
			render(
				<div>
					<Table>
						<Table.Header>
							<Table.Row>
								<Table.HeaderCell>Name</Table.HeaderCell>
								<Table.HeaderCell>Age</Table.HeaderCell>
								<Table.HeaderCell>Actions</Table.HeaderCell>
							</Table.Row>
						</Table.Header>
						<Table.Body>
							<Table.Row>
								<Table.Cell>John Doe</Table.Cell>
								<Table.Cell>30</Table.Cell>
								<Table.Cell>
									<Button size="sm">Edit</Button>
								</Table.Cell>
							</Table.Row>
						</Table.Body>
					</Table>
					<List>
						<List.Item>First item</List.Item>
						<List.Item>Second item</List.Item>
					</List>
				</div>,
			);

			// Check table structure
			const table = screen.getByRole('table');
			const columnHeaders = screen.getAllByRole('columnheader');
			const cells = screen.getAllByRole('cell');

			expect(table).toBeInTheDocument();
			expect(columnHeaders).toHaveLength(3);
			expect(cells).toHaveLength(3);

			// Check header scope attributes
			columnHeaders.forEach((header) =>
			{
				expect(header).toHaveAttribute('scope', 'col');
			});

			// Check list structure
			const list = screen.getByRole('list');
			const listItems = screen.getAllByRole('listitem');
			expect(list).toBeInTheDocument();
			expect(listItems).toHaveLength(2);
		});
	});

	/**
	 * Accessible names and descriptions testing
	 */
	describe('Accessible Names and Descriptions', () =>
	{
		it('should provide accessible names for all interactive elements', () =>
		{
			render(
				<div>
					<Button>Save Document</Button>
					<Button aria-label="Close dialog">×</Button>
					<Button aria-labelledby="save-label">
						<Icon name="save" />
					</Button>
					<span id="save-label" hidden>Save changes</span>
					<Field.Text label="Full Name" />
					<Field.Select label="Preferred Language" options={[]} />
				</div>,
			);

			const saveButton = screen.getByRole('button', { name: 'Save Document' });
			const closeButton = screen.getByRole('button', { name: 'Close dialog' });
			const iconButton = screen.getByRole('button', { name: 'Save changes' });
			const nameInput = screen.getByRole('textbox', { name: 'Full Name' });
			const languageSelect = screen.getByRole('combobox', { name: 'Preferred Language' });

			expect(saveButton).toHaveAccessibleName('Save Document');
			expect(closeButton).toHaveAccessibleName('Close dialog');
			expect(iconButton).toHaveAccessibleName('Save changes');
			expect(nameInput).toHaveAccessibleName('Full Name');
			expect(languageSelect).toHaveAccessibleName('Preferred Language');
		});

		it('should provide accessible descriptions for complex elements', () =>
		{
			render(
				<Form>
					<Field.Text
						label="Password"
						type="password"
						helperText="Must be at least 8 characters long"
						error="Password is too short"
					/>
					<Field.Select
						label="Time Zone"
						options={[{ value: 'utc', label: 'UTC' }]}
						helperText="Select your local time zone"
					/>
					<Button
						aria-describedby="delete-warning"
						variant="danger"
					>
						Delete Account
					</Button>
					<p id="delete-warning">This action cannot be undone</p>
				</Form>,
			);

			const passwordInput = screen.getByLabelText('Password');
			const timezoneSelect = screen.getByLabelText('Time Zone');
			const deleteButton = screen.getByRole('button', { name: 'Delete Account' });

			// Check accessible descriptions
			expect(passwordInput).toHaveAccessibleDescription();
			expect(timezoneSelect).toHaveAccessibleDescription();
			expect(deleteButton).toHaveAccessibleDescription();
		});

		it('should provide accessible names for media elements', () =>
		{
			render(
				<div>
					<Media.Image
						src="/profile.jpg"
						alt="User profile photo showing John Smith"
					/>
					<Media.Video
						src="/intro.mp4"
						controls
						aria-label="Company introduction video"
					>
						<Media.Track
							kind="captions"
							src="/captions.vtt"
							label="English captions"
						/>
					</Media.Video>
					<Media.Audio
						src="/podcast.mp3"
						controls
						aria-label="Weekly tech podcast episode 42"
					/>
					<Icon aria-label="Settings" name="gear" />
				</div>,
			);

			const image = screen.getByRole('img');
			const video = screen.getByLabelText('Company introduction video');
			const audio = screen.getByLabelText('Weekly tech podcast episode 42');
			const icon = screen.getByLabelText('Settings');

			expect(image).toHaveAccessibleName('User profile photo showing John Smith');
			expect(video).toHaveAccessibleName('Company introduction video');
			expect(audio).toHaveAccessibleName('Weekly tech podcast episode 42');
			expect(icon).toHaveAccessibleName('Settings');
		});
	});

	/**
	 * State announcements and live regions testing
	 */
	describe('State Announcements and Live Regions', () =>
	{
		it('should announce loading states', () =>
		{
			render(
				<div>
					<Button loading>Saving...</Button>
					<Indicator.Loading />
					<DataTable loading data={[]} columns={[]} />
					<div aria-live="polite" aria-busy="true">
						Loading content...
					</div>
				</div>,
			);

			const loadingButton = screen.getByRole('button');
			const loadingIndicator = screen.getByRole('status');
			const liveRegion = screen.getByText('Loading content...');

			expect(loadingButton).toHaveAttribute('aria-busy', 'true');
			expect(loadingIndicator).toBeInTheDocument();
			expect(liveRegion).toHaveAttribute('aria-live', 'polite');
			expect(liveRegion).toHaveAttribute('aria-busy', 'true');
		});

		it('should announce form validation errors', () =>
		{
			render(
				<Form>
					<Field.Text
						label="Email"
						error="Please enter a valid email address"
						aria-invalid="true"
					/>
					<Field.Select
						label="Country"
						error="Please select a country"
						options={[]}
						aria-invalid="true"
					/>
					<div role="alert" aria-live="assertive">
						Please fix the errors above before submitting
					</div>
				</Form>,
			);

			const emailInput = screen.getByLabelText('Email');
			const countrySelect = screen.getByLabelText('Country');
			const errorAlert = screen.getByRole('alert');

			expect(emailInput).toHaveAttribute('aria-invalid', 'true');
			expect(countrySelect).toHaveAttribute('aria-invalid', 'true');
			expect(errorAlert).toHaveAttribute('aria-live', 'assertive');
		});

		it('should announce success messages', () =>
		{
			render(
				<div>
					<Message variant="success" role="status">
						Your changes have been saved successfully
					</Message>
					<Toast.Provider>
						<Toast variant="success">
							Profile updated successfully
						</Toast>
					</Toast.Provider>
				</div>,
			);

			const successMessage = screen.getByRole('status');
			expect(successMessage).toHaveTextContent('Your changes have been saved successfully');
		});

		it('should announce dynamic content changes', async () =>
		{
			const TestComponent = () =>
			{
				const [count, setCount] = React.useState(0);
				const [status, setStatus] = React.useState('');

				const increment = () =>
				{
					setCount(c => c + 1);
					setStatus(`Count updated to ${count + 1}`);
				};

				return (
					<div>
						<Button onClick={increment}>Increment</Button>
						<div aria-live="polite" aria-atomic="true">
							Count: {count}
						</div>
						<div role="status" aria-live="polite">
							{status}
						</div>
					</div>
				);
			};

			render(<TestComponent />);

			const button = screen.getByRole('button', { name: 'Increment' });
			const countDisplay = screen.getByText('Count: 0');
			const statusRegion = screen.getByRole('status');

			expect(countDisplay).toHaveAttribute('aria-live', 'polite');
			expect(countDisplay).toHaveAttribute('aria-atomic', 'true');
			expect(statusRegion).toHaveAttribute('aria-live', 'polite');

			// Click button and check updates
			await user.click(button);
			expect(screen.getByText('Count: 1')).toBeInTheDocument();
			expect(statusRegion).toHaveTextContent('Count updated to 1');
		});
	});

	/**
	 * Navigation and landmark testing
	 */
	describe('Navigation and Landmarks', () =>
	{
		it('should provide proper landmark roles', () =>
		{
			render(
				<div>
					<header role="banner">
						<nav role="navigation">
							<Menu>
								<Menu.Item>Home</Menu.Item>
								<Menu.Item>About</Menu.Item>
							</Menu>
						</nav>
					</header>
					<main role="main">
						<section aria-labelledby="content-heading">
							<h1 id="content-heading">Main Content</h1>
							<p>Content goes here</p>
						</section>
					</main>
					<aside role="complementary" aria-label="Related links">
						<h2>Related</h2>
						<List>
							<List.Item>Link 1</List.Item>
							<List.Item>Link 2</List.Item>
						</List>
					</aside>
					<footer role="contentinfo">
						<p>Copyright 2024</p>
					</footer>
				</div>,
			);

			const banner = screen.getByRole('banner');
			const navigation = screen.getByRole('navigation');
			const main = screen.getByRole('main');
			const complementary = screen.getByRole('complementary');
			const contentinfo = screen.getByRole('contentinfo');

			expect(banner).toBeInTheDocument();
			expect(navigation).toBeInTheDocument();
			expect(main).toBeInTheDocument();
			expect(complementary).toBeInTheDocument();
			expect(contentinfo).toBeInTheDocument();
		});

		it('should provide skip links for keyboard navigation', () =>
		{
			render(
				<div>
					<a href="#main-content" className="skip-link">
						Skip to main content
					</a>
					<nav>
						<Menu>
							<Menu.Item>Home</Menu.Item>
							<Menu.Item>About</Menu.Item>
						</Menu>
					</nav>
					<main id="main-content">
						<h1>Main Content</h1>
					</main>
				</div>,
			);

			const skipLink = screen.getByRole('link', { name: 'Skip to main content' });
			const mainContent = screen.getByRole('main');

			expect(skipLink).toHaveAttribute('href', '#main-content');
			expect(mainContent).toHaveAttribute('id', 'main-content');
		});

		it('should provide proper heading hierarchy', () =>
		{
			render(
				<div>
					<Text.Heading level={1}>Page Title</Text.Heading>
					<Text.Heading level={2}>Section Title</Text.Heading>
					<Text.Heading level={3}>Subsection Title</Text.Heading>
					<Text.Heading level={2}>Another Section</Text.Heading>
				</div>,
			);

			const h1 = screen.getByRole('heading', { level: 1 });
			const h2Elements = screen.getAllByRole('heading', { level: 2 });
			const h3 = screen.getByRole('heading', { level: 3 });

			expect(h1).toHaveTextContent('Page Title');
			expect(h2Elements).toHaveLength(2);
			expect(h3).toHaveTextContent('Subsection Title');
		});
	});

	/**
	 * Complex component screen reader testing
	 */
	describe('Complex Component Screen Reader Support', () =>
	{
		it('should provide proper DataTable screen reader support', () =>
		{
			const data = [
				{ id: 1, name: 'John Doe', age: 30, email: '<EMAIL>' },
				{ id: 2, name: 'Jane Smith', age: 25, email: '<EMAIL>' },
			];

			const columns = [
				{ key: 'name', header: 'Full Name' },
				{ key: 'age', header: 'Age' },
				{ key: 'email', header: 'Email Address' },
			];

			render(
				<DataTable
					data={data}
					columns={columns}
					caption="Employee information table"
				/>,
			);

			const table = screen.getByRole('table');
			const caption = screen.getByText('Employee information table');
			const columnHeaders = screen.getAllByRole('columnheader');
			const cells = screen.getAllByRole('cell');

			expect(table).toBeInTheDocument();
			expect(caption).toBeInTheDocument();
			expect(columnHeaders).toHaveLength(3);
			expect(cells).toHaveLength(6); // 2 rows × 3 columns

			// Check header associations
			columnHeaders.forEach((header) =>
			{
				expect(header).toHaveAttribute('scope', 'col');
			});
		});

		it('should provide proper Modal screen reader support', async () =>
		{
			const handleClose = vi.fn();

			render(
				<Modal open onClose={handleClose}>
					<Modal.Content>
						<Modal.Header>
							<Modal.Title>Confirm Delete</Modal.Title>
							<Modal.Close aria-label="Close dialog">×</Modal.Close>
						</Modal.Header>
						<Modal.Body>
							<p>Are you sure you want to delete this item?</p>
							<Button variant="danger">Delete</Button>
							<Button onClick={handleClose}>Cancel</Button>
						</Modal.Body>
					</Modal.Content>
				</Modal>,
			);

			const dialog = screen.getByRole('dialog');
			const title = screen.getByText('Confirm Delete');
			const closeButton = screen.getByRole('button', { name: 'Close dialog' });

			expect(dialog).toHaveAttribute('aria-modal', 'true');
			expect(dialog).toHaveAttribute('aria-labelledby');
			expect(title).toBeInTheDocument();
			expect(closeButton).toHaveAccessibleName('Close dialog');
		});

		it('should provide proper Dropdown screen reader support', async () =>
		{
			render(
				<Dropdown>
					<Dropdown.Trigger aria-haspopup="menu">
						Options Menu
					</Dropdown.Trigger>
					<Dropdown.Content role="menu">
						<Dropdown.Item role="menuitem">Edit</Dropdown.Item>
						<Dropdown.Item role="menuitem">Delete</Dropdown.Item>
						<Dropdown.Item role="menuitem" disabled>
							Archive
						</Dropdown.Item>
					</Dropdown.Content>
				</Dropdown>,
			);

			const trigger = screen.getByRole('button', { name: 'Options Menu' });

			expect(trigger).toHaveAttribute('aria-haspopup', 'menu');
			expect(trigger).toHaveAttribute('aria-expanded', 'false');

			// Open dropdown
			await user.click(trigger);
			expect(trigger).toHaveAttribute('aria-expanded', 'true');

			const menu = screen.getByRole('menu');
			const menuItems = screen.getAllByRole('menuitem');

			expect(menu).toBeInTheDocument();
			expect(menuItems).toHaveLength(3);
			expect(menuItems[2]).toHaveAttribute('aria-disabled', 'true');
		});

		it('should provide proper Pagination screen reader support', () =>
		{
			render(
				<Pagination
					total={100}
					page={5}
					aria-label="Table pagination navigation"
				/>,
			);

			const navigation = screen.getByRole('navigation', { name: 'Table pagination navigation' });
			const prevButton = screen.getByRole('button', { name: /previous/i });
			const nextButton = screen.getByRole('button', { name: /next/i });
			const currentPage = screen.getByRole('button', { name: '5' });

			expect(navigation).toBeInTheDocument();
			expect(prevButton).toBeInTheDocument();
			expect(nextButton).toBeInTheDocument();
			expect(currentPage).toHaveAttribute('aria-current', 'page');
		});

		it('should provide proper Slider screen reader support', () =>
		{
			render(
				<Slider
					defaultValue={50}
					min={0}
					max={100}
					step={5}
					aria-label="Volume control"
					aria-valuetext="50 percent"
				/>,
			);

			const slider = screen.getByRole('slider');

			expect(slider).toHaveAttribute('aria-label', 'Volume control');
			expect(slider).toHaveAttribute('aria-valuemin', '0');
			expect(slider).toHaveAttribute('aria-valuemax', '100');
			expect(slider).toHaveAttribute('aria-valuenow', '50');
			expect(slider).toHaveAttribute('aria-valuetext', '50 percent');
		});
	});

	/**
	 * Error handling and recovery testing
	 */
	describe('Error Handling and Recovery', () =>
	{
		it('should announce form submission errors', () =>
		{
			render(
				<Form>
					<div role="alert" aria-live="assertive">
						Form submission failed. Please check your connection and try again.
					</div>
					<Field.Text
						label="Email"
						error="This field is required"
						aria-invalid="true"
					/>
					<Button type="submit">Submit</Button>
				</Form>,
			);

			const alert = screen.getByRole('alert');
			const emailInput = screen.getByLabelText('Email');

			expect(alert).toHaveAttribute('aria-live', 'assertive');
			expect(emailInput).toHaveAttribute('aria-invalid', 'true');
			expect(emailInput).toHaveAccessibleDescription();
		});

		it('should provide recovery instructions for errors', () =>
		{
			render(
				<div>
					<Message variant="error" role="alert">
						<strong>Error:</strong> Unable to save changes.
						<br />
						Please check your internet connection and try again.
					</Message>
					<Button>Retry</Button>
				</div>,
			);

			const errorMessage = screen.getByRole('alert');
			const retryButton = screen.getByRole('button', { name: 'Retry' });

			expect(errorMessage).toBeInTheDocument();
			expect(retryButton).toBeInTheDocument();
		});

		it('should handle timeout and session expiry announcements', () =>
		{
			render(
				<div>
					<div role="alert" aria-live="assertive">
						Your session will expire in 5 minutes. Please save your work.
					</div>
					<Button>Extend Session</Button>
				</div>,
			);

			const timeoutWarning = screen.getByRole('alert');
			const extendButton = screen.getByRole('button', { name: 'Extend Session' });

			expect(timeoutWarning).toHaveAttribute('aria-live', 'assertive');
			expect(extendButton).toBeInTheDocument();
		});
	});
});
