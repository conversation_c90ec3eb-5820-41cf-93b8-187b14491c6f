import { describe, it, expect } from 'vitest';
import { render } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';
import type { AxeResults, RunOptions } from 'axe-core';
import { glob } from 'glob';
import { readFileSync } from 'fs';
import { resolve } from 'path';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

declare global
{
	namespace Vi
	{
		interface JestAssertion<T = any>
		{
			toHaveNoViolations(): T;
		}
	}
}

/**
 * Default axe configuration for comprehensive accessibility testing
 */
const comprehensiveAxeConfig: RunOptions = {
	rules: {
		// Core accessibility rules
		'aria-allowed-attr': { enabled: true },
		'aria-required-attr': { enabled: true },
		'aria-valid-attr': { enabled: true },
		'aria-valid-attr-value': { enabled: true },
		'button-name': { enabled: true },
		'duplicate-id': { enabled: true },
		'form-field-multiple-labels': { enabled: true },
		'html-has-lang': { enabled: true },
		'image-alt': { enabled: true },
		'input-image-alt': { enabled: true },
		'label': { enabled: true },
		'link-name': { enabled: true },
		'list': { enabled: true },
		'listitem': { enabled: true },
		'nested-interactive': { enabled: true },
		'no-autoplay-audio': { enabled: true },
		'role-img-alt': { enabled: true },
		'scrollable-region-focusable': { enabled: true },
		'select-name': { enabled: true },
		'server-side-image-map': { enabled: true },
		'svg-img-alt': { enabled: true },
		'td-headers-attr': { enabled: true },
		'th-has-data-cells': { enabled: true },
		'valid-lang': { enabled: true },

		// Keyboard navigation rules
		'focus-order-semantics': { enabled: true },
		'focusable-content': { enabled: true },
		'tabindex': { enabled: true },

		// Screen reader compatibility rules
		'aria-hidden-body': { enabled: true },
		'aria-hidden-focus': { enabled: true },
		'aria-labelledby': { enabled: true },
		'aria-describedby': { enabled: true },

		// WCAG compliance rules
		'bypass': { enabled: true },
		'document-title': { enabled: true },
		'heading-order': { enabled: true },
		'landmark-banner-is-top-level': { enabled: true },
		'landmark-contentinfo-is-top-level': { enabled: true },
		'landmark-main-is-top-level': { enabled: true },
		'landmark-no-duplicate-banner': { enabled: true },
		'landmark-no-duplicate-contentinfo': { enabled: true },
		'landmark-one-main': { enabled: true },
		'page-has-heading-one': { enabled: true },
		'region': { enabled: true },

		// Disable problematic rules in test environment
		'color-contrast': { enabled: false }, // Unreliable in jsdom
		'meta-viewport': { enabled: false }, // Not applicable to components
	},
	tags: ['wcag2a', 'wcag2aa', 'wcag21aa', 'wcag21aaa', 'best-practice'],
};

/**
 * Run axe accessibility audit on a component
 */
const runAccessibilityAudit = async (
	container: HTMLElement,
	componentName: string,
	config: RunOptions = comprehensiveAxeConfig,
): Promise<AxeResults> =>
{
	try
	{
		const results = await axe(container, config);

		// Log violations for debugging
		if (results.violations.length > 0)
		{
			console.warn(`Accessibility violations found in ${componentName}:`, results.violations);
		}

		return results;
	}
	catch (error)
	{
		console.error(`Error running accessibility audit for ${componentName}:`, error);
		throw error;
	}
};

/**
 * Test keyboard navigation for interactive elements
 */
const testKeyboardNavigation = async (container: HTMLElement, componentName: string) =>
{
	const focusableElements = container.querySelectorAll(
		'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
	);

	if (focusableElements.length === 0)
	{
		console.log(`No focusable elements found in ${componentName}`);
		return;
	}

	// Test that all focusable elements can receive focus
	focusableElements.forEach((element, index) =>
	{
		(element as HTMLElement).focus();
		expect(document.activeElement).toBe(element);
		console.log(`✓ Element ${index + 1} in ${componentName} is focusable`);
	});
};

/**
 * Test ARIA attributes and screen reader compatibility
 */
const testScreenReaderCompatibility = (container: HTMLElement, componentName: string) =>
{
	// Check for proper ARIA roles
	const elementsWithRoles = container.querySelectorAll('[role]');
	elementsWithRoles.forEach((element) =>
	{
		const role = element.getAttribute('role');
		expect(role).toBeTruthy();
		console.log(`✓ ${componentName} has element with role: ${role}`);
	});

	// Check for proper labels
	const interactiveElements = container.querySelectorAll(
		'button, input, select, textarea, [role="button"], [role="textbox"], [role="combobox"]',
	);

	interactiveElements.forEach((element) =>
	{
		const hasLabel = element.getAttribute('aria-label') ||
			element.getAttribute('aria-labelledby') ||
			element.textContent?.trim() ||
			(element as HTMLInputElement).labels?.length > 0;

		if (!hasLabel)
		{
			console.warn(`⚠️ Interactive element in ${componentName} may be missing accessible name`);
		}
	});

	// Check for proper descriptions
	const elementsWithDescriptions = container.querySelectorAll('[aria-describedby]');
	elementsWithDescriptions.forEach((element) =>
	{
		const describedBy = element.getAttribute('aria-describedby');
		const descriptionElement = container.querySelector(`#${describedBy}`);
		if (!descriptionElement)
		{
			console.warn(`⚠️ ${componentName} has aria-describedby pointing to non-existent element`);
		}
	});
};

/**
 * Test WCAG compliance for interactive elements
 */
const testWCAGCompliance = (container: HTMLElement, componentName: string) =>
{
	// Test focus management
	const focusableElements = container.querySelectorAll(
		'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])',
	);

	focusableElements.forEach((element) =>
	{
		// Check that focusable elements are not hidden
		const computedStyle = window.getComputedStyle(element as HTMLElement);
		const isVisible = computedStyle.display !== 'none' && computedStyle.visibility !== 'hidden';

		if (!isVisible)
		{
			console.warn(`⚠️ Focusable element in ${componentName} is hidden`);
		}
	});

	// Test error states
	const elementsWithErrors = container.querySelectorAll('[aria-invalid="true"]');
	elementsWithErrors.forEach((element) =>
	{
		const hasErrorDescription = element.getAttribute('aria-describedby');
		if (!hasErrorDescription)
		{
			console.warn(`⚠️ Element with error state in ${componentName} missing error description`);
		}
	});

	// Test loading states
	const loadingElements = container.querySelectorAll('[aria-busy="true"]');
	loadingElements.forEach((element) =>
	{
		console.log(`✓ ${componentName} properly indicates loading state`);
	});
};

/**
 * Comprehensive accessibility test suite for a component
 */
const runComponentAccessibilityTests = async (
	renderComponent: () => { container: HTMLElement },
	componentName: string,
) =>
{
	describe(`${componentName} Accessibility Tests`, () =>
	{
		it('should have no axe accessibility violations', async () =>
		{
			const { container } = renderComponent();
			const results = await runAccessibilityAudit(container, componentName);
			expect(results).toHaveNoViolations();
		});

		it('should support keyboard navigation', async () =>
		{
			const { container } = renderComponent();
			await testKeyboardNavigation(container, componentName);
		});

		it('should be compatible with screen readers', () =>
		{
			const { container } = renderComponent();
			testScreenReaderCompatibility(container, componentName);
		});

		it('should comply with WCAG guidelines', () =>
		{
			const { container } = renderComponent();
			testWCAGCompliance(container, componentName);
		});
	});
};

/**
 * Generate accessibility test report
 */
const generateAccessibilityReport = async (results: AxeResults[], componentName: string) =>
{
	const totalViolations = results.reduce((sum, result) => sum + result.violations.length, 0);
	const totalPasses = results.reduce((sum, result) => sum + result.passes.length, 0);

	const report = {
		component: componentName,
		timestamp: new Date().toISOString(),
		summary: {
			totalTests: results.length,
			totalViolations,
			totalPasses,
			passRate: totalPasses / (totalPasses + totalViolations) * 100,
		},
		violations: results.flatMap(result => result.violations),
		passes: results.flatMap(result => result.passes),
	};

	console.log(`\n📊 Accessibility Report for ${componentName}:`);
	console.log(`   Total Tests: ${report.summary.totalTests}`);
	console.log(`   Violations: ${report.summary.totalViolations}`);
	console.log(`   Passes: ${report.summary.totalPasses}`);
	console.log(`   Pass Rate: ${report.summary.passRate.toFixed(2)}%`);

	if (report.violations.length > 0)
	{
		console.log('\n❌ Violations:');
		report.violations.forEach((violation, index) =>
		{
			console.log(`   ${index + 1}. ${violation.description}`);
			console.log(`      Impact: ${violation.impact}`);
			console.log(`      Help: ${violation.helpUrl}`);
		});
	}

	return report;
};

export {
	runAccessibilityAudit,
	testKeyboardNavigation,
	testScreenReaderCompatibility,
	testWCAGCompliance,
	runComponentAccessibilityTests,
	generateAccessibilityReport,
	comprehensiveAxeConfig,
};
