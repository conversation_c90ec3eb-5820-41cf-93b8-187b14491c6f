import { describe, it, expect, beforeEach } from 'vitest';
import { render, screen, cleanup } from '@testing-library/react';
import { userEvent } from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import type { AxeResults } from 'axe-core';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

declare global
{
	namespace Vi
	{
		interface JestAssertion<T = any>
		{
			toHaveNoViolations(): T;
		}
	}
}

/**
 * Comprehensive accessibility compliance test suite
 * Tests all components for WCAG compliance, keyboard navigation, and screen reader compatibility
 */
describe('Accessibility Compliance Testing', () =>
{
	let user: ReturnType<typeof userEvent.setup>;

	beforeEach(() =>
	{
		user = userEvent.setup();
		cleanup();
	});

	/**
	 * Axe-core accessibility audits for all components
	 */
	describe('Axe-core Accessibility Audits', () =>
	{
		const axeConfig = {
			rules: {
				// Enable all WCAG 2.1 AA rules
				'color-contrast': { enabled: false }, // Disabled in jsdom environment
				'aria-allowed-attr': { enabled: true },
				'aria-required-attr': { enabled: true },
				'aria-valid-attr': { enabled: true },
				'aria-valid-attr-value': { enabled: true },
				'button-name': { enabled: true },
				'duplicate-id': { enabled: true },
				'form-field-multiple-labels': { enabled: true },
				'html-has-lang': { enabled: true },
				'image-alt': { enabled: true },
				'input-image-alt': { enabled: true },
				'label': { enabled: true },
				'link-name': { enabled: true },
				'list': { enabled: true },
				'listitem': { enabled: true },
				'nested-interactive': { enabled: true },
				'role-img-alt': { enabled: true },
				'scrollable-region-focusable': { enabled: true },
				'select-name': { enabled: true },
				'svg-img-alt': { enabled: true },
				'td-headers-attr': { enabled: true },
				'th-has-data-cells': { enabled: true },
				'valid-lang': { enabled: true },
			},
			tags: ['wcag2a', 'wcag2aa', 'wcag21aa', 'best-practice'],
		};

		it('Basic HTML elements should have no accessibility violations', async () =>
		{
			const { container } = render(
				<div>
					<button>Default Button</button>
					<button disabled>Disabled Button</button>
					<button type="submit">Submit Button</button>
				</div>,
			);

			const results = await axe(container, axeConfig);
			expect(results).toHaveNoViolations();
		});

		it('Form elements should have no accessibility violations', async () =>
		{
			const { container } = render(
				<form>
					<label htmlFor="name">Name</label>
					<input id="name" type="text" placeholder="Enter your name" />

					<label htmlFor="country">Country</label>
					<select id="country">
						<option value="us">United States</option>
					</select>

					<label>
						<input type="checkbox" />
						I agree to terms
					</label>

					<fieldset>
						<legend>Gender</legend>
						<label>
							<input type="radio" name="gender" value="male" />
							Male
						</label>
						<label>
							<input type="radio" name="gender" value="female" />
							Female
						</label>
					</fieldset>

					<button type="submit">Submit</button>
				</form>,
			);

			const results = await axe(container, axeConfig);
			expect(results).toHaveNoViolations();
		});

		it('Navigation elements should have no accessibility violations', async () =>
		{
			const { container } = render(
				<div>
					<nav role="navigation">
						<ul role="menu">
							<li role="menuitem">
								<a href="#home">Home</a>
							</li>
							<li role="menuitem">
								<a href="#about">About</a>
							</li>
							<li role="menuitem">
								<a href="#contact">Contact</a>
							</li>
						</ul>
					</nav>

					<div role="tablist">
						<button role="tab" aria-selected="true" aria-controls="panel1">Tab 1</button>
						<button role="tab" aria-selected="false" aria-controls="panel2">Tab 2</button>
					</div>
					<div id="panel1" role="tabpanel">Content 1</div>
					<div id="panel2" role="tabpanel" hidden>Content 2</div>
				</div>,
			);

			const results = await axe(container, axeConfig);
			expect(results).toHaveNoViolations();
		});

		it('Data display elements should have no accessibility violations', async () =>
		{
			const { container } = render(
				<div>
					<table>
						<thead>
							<tr>
								<th scope="col">Name</th>
								<th scope="col">Age</th>
							</tr>
						</thead>
						<tbody>
							<tr>
								<td>John</td>
								<td>30</td>
							</tr>
						</tbody>
					</table>

					<ul>
						<li>Item 1</li>
						<li>Item 2</li>
					</ul>
				</div>,
			);

			const results = await axe(container, axeConfig);
			expect(results).toHaveNoViolations();
		});

		it('Media elements should have no accessibility violations', async () =>
		{
			const { container } = render(
				<div>
					<img src="/test.jpg" alt="Test image" />
					<video src="/test.mp4" controls>
						<track kind="captions" src="/captions.vtt" />
					</video>
					<audio src="/test.mp3" controls />
				</div>,
			);

			const results = await axe(container, axeConfig);
			expect(results).toHaveNoViolations();
		});

		it('Interactive elements should have no accessibility violations', async () =>
		{
			const { container } = render(
				<div>
					<button aria-haspopup="menu" aria-expanded="false">
						Open Menu
					</button>
					<div role="menu" hidden>
						<div role="menuitem">Option 1</div>
						<div role="menuitem">Option 2</div>
					</div>

					<input
						type="range"
						min="0"
						max="100"
						defaultValue="50"
						aria-label="Volume control"
					/>

					<nav aria-label="Pagination">
						<button aria-label="Previous page">Previous</button>
						<button aria-current="page">1</button>
						<button>2</button>
						<button aria-label="Next page">Next</button>
					</nav>
				</div>,
			);

			const results = await axe(container, axeConfig);
			expect(results).toHaveNoViolations();
		});
	});

	/**
	 * Keyboard navigation testing across all interactive components
	 */
	describe('Keyboard Navigation Testing', () =>
	{
		it('Button should support keyboard navigation', async () =>
		{
			render(
				<div>
					<Button>Button 1</Button>
					<Button>Button 2</Button>
					<Button>Button 3</Button>
				</div>,
			);

			const buttons = screen.getAllByRole('button');

			// Focus first button
			buttons[0].focus();
			expect(document.activeElement).toBe(buttons[0]);

			// Tab to next button
			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(buttons[1]);

			// Tab to next button
			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(buttons[2]);

			// Shift+Tab back
			await user.keyboard('{Shift>}{Tab}{/Shift}');
			expect(document.activeElement).toBe(buttons[1]);

			// Enter key should activate button
			const clickHandler = vi.fn();
			buttons[1].onclick = clickHandler;
			await user.keyboard('{Enter}');
			expect(clickHandler).toHaveBeenCalled();
		});

		it('Form fields should support keyboard navigation', async () =>
		{
			render(
				<Form>
					<Field.Text label="Name" />
					<Field.Select label="Country" options={[{ value: 'us', label: 'US' }]} />
					<Field.Checkbox label="Agree" />
					<Button type="submit">Submit</Button>
				</Form>,
			);

			const textInput = screen.getByLabelText('Name');
			const selectInput = screen.getByLabelText('Country');
			const checkbox = screen.getByLabelText('Agree');
			const submitButton = screen.getByRole('button', { name: 'Submit' });

			// Tab through form elements
			textInput.focus();
			expect(document.activeElement).toBe(textInput);

			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(selectInput);

			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(checkbox);

			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(submitButton);

			// Space should toggle checkbox
			checkbox.focus();
			await user.keyboard(' ');
			expect(checkbox).toBeChecked();
		});

		it('Menu should support arrow key navigation', async () =>
		{
			render(
				<Menu>
					<Menu.Item>Home</Menu.Item>
					<Menu.Item>About</Menu.Item>
					<Menu.Item>Contact</Menu.Item>
				</Menu>,
			);

			const menuItems = screen.getAllByRole('menuitem');

			// Focus first item
			menuItems[0].focus();
			expect(document.activeElement).toBe(menuItems[0]);

			// Arrow down to next item
			await user.keyboard('{ArrowDown}');
			expect(document.activeElement).toBe(menuItems[1]);

			// Arrow down to next item
			await user.keyboard('{ArrowDown}');
			expect(document.activeElement).toBe(menuItems[2]);

			// Arrow up to previous item
			await user.keyboard('{ArrowUp}');
			expect(document.activeElement).toBe(menuItems[1]);
		});

		it('Tabs should support arrow key navigation', async () =>
		{
			render(
				<Tabs defaultValue="tab1">
					<Tabs.List>
						<Tabs.Trigger value="tab1">Tab 1</Tabs.Trigger>
						<Tabs.Trigger value="tab2">Tab 2</Tabs.Trigger>
						<Tabs.Trigger value="tab3">Tab 3</Tabs.Trigger>
					</Tabs.List>
					<Tabs.Content value="tab1">Content 1</Tabs.Content>
					<Tabs.Content value="tab2">Content 2</Tabs.Content>
					<Tabs.Content value="tab3">Content 3</Tabs.Content>
				</Tabs>,
			);

			const tabTriggers = screen.getAllByRole('tab');

			// Focus first tab
			tabTriggers[0].focus();
			expect(document.activeElement).toBe(tabTriggers[0]);

			// Arrow right to next tab
			await user.keyboard('{ArrowRight}');
			expect(document.activeElement).toBe(tabTriggers[1]);

			// Arrow left to previous tab
			await user.keyboard('{ArrowLeft}');
			expect(document.activeElement).toBe(tabTriggers[0]);
		});

		it('Table should support keyboard navigation', async () =>
		{
			render(
				<Table>
					<Table.Header>
						<Table.Row>
							<Table.HeaderCell tabIndex={0}>Name</Table.HeaderCell>
							<Table.HeaderCell tabIndex={0}>Age</Table.HeaderCell>
						</Table.Row>
					</Table.Header>
					<Table.Body>
						<Table.Row>
							<Table.Cell tabIndex={0}>John</Table.Cell>
							<Table.Cell tabIndex={0}>30</Table.Cell>
						</Table.Row>
					</Table.Body>
				</Table>,
			);

			const cells = screen.getAllByRole('cell');
			const columnHeaders = screen.getAllByRole('columnheader');

			// Focus first header
			columnHeaders[0].focus();
			expect(document.activeElement).toBe(columnHeaders[0]);

			// Tab to next header
			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(columnHeaders[1]);

			// Tab to first data cell
			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(cells[0]);
		});
	});

	/**
	 * Screen reader compatibility testing
	 */
	describe('Screen Reader Compatibility', () =>
	{
		it('Button should have proper ARIA attributes', () =>
		{
			render(
				<div>
					<Button>Click me</Button>
					<Button disabled>Disabled button</Button>
					<Button aria-label="Close dialog">×</Button>
				</div>,
			);

			const buttons = screen.getAllByRole('button');

			// Check accessible names
			expect(buttons[0]).toHaveAccessibleName('Click me');
			expect(buttons[1]).toHaveAccessibleName('Disabled button');
			expect(buttons[2]).toHaveAccessibleName('Close dialog');

			// Check disabled state
			expect(buttons[1]).toBeDisabled();
			expect(buttons[1]).toHaveAttribute('aria-disabled', 'true');
		});

		it('Form fields should have proper labels and descriptions', () =>
		{
			render(
				<Form>
					<Field.Text
						label="Email"
						placeholder="Enter your email"
						required
						error="Invalid email"
					/>
					<Field.Select
						label="Country"
						options={[{ value: 'us', label: 'United States' }]}
						required
					/>
					<Field.Checkbox label="I agree to the terms and conditions" />
				</Form>,
			);

			const emailInput = screen.getByLabelText('Email');
			const countrySelect = screen.getByLabelText('Country');
			const checkbox = screen.getByLabelText('I agree to the terms and conditions');

			// Check required attributes
			expect(emailInput).toBeRequired();
			expect(countrySelect).toBeRequired();
			expect(checkbox).not.toBeRequired();

			// Check ARIA attributes
			expect(emailInput).toHaveAttribute('aria-invalid', 'true');
			expect(emailInput).toHaveAccessibleDescription();
		});

		it('Navigation components should have proper ARIA roles', () =>
		{
			render(
				<div>
					<Menu>
						<Menu.Item>Home</Menu.Item>
						<Menu.Item>About</Menu.Item>
					</Menu>
					<nav>
						<List role="menubar">
							<List.Item role="none">
								<Button role="menuitem">Home</Button>
							</List.Item>
							<List.Item role="none">
								<Button role="menuitem">About</Button>
							</List.Item>
						</List>
					</nav>
				</div>,
			);

			// Check menu roles
			const menu = screen.getByRole('menu');
			const menuItems = screen.getAllByRole('menuitem');

			expect(menu).toBeInTheDocument();
			expect(menuItems).toHaveLength(4); // 2 from Menu component + 2 from nav
		});

		it('Data tables should have proper headers and captions', () =>
		{
			render(
				<Table>
					<Table.Header>
						<Table.Row>
							<Table.HeaderCell>Name</Table.HeaderCell>
							<Table.HeaderCell>Age</Table.HeaderCell>
							<Table.HeaderCell>City</Table.HeaderCell>
						</Table.Row>
					</Table.Header>
					<Table.Body>
						<Table.Row>
							<Table.Cell>John Doe</Table.Cell>
							<Table.Cell>30</Table.Cell>
							<Table.Cell>New York</Table.Cell>
						</Table.Row>
					</Table.Body>
				</Table>,
			);

			const table = screen.getByRole('table');
			const columnHeaders = screen.getAllByRole('columnheader');
			const cells = screen.getAllByRole('cell');

			expect(table).toBeInTheDocument();
			expect(columnHeaders).toHaveLength(3);
			expect(cells).toHaveLength(3);

			// Check header associations
			columnHeaders.forEach((header) =>
			{
				expect(header).toHaveAttribute('scope', 'col');
			});
		});

		it('Media components should have proper alternative text', () =>
		{
			render(
				<div>
					<Media.Image src="/test.jpg" alt="A beautiful sunset over mountains" />
					<Media.Video src="/test.mp4" controls>
						<Media.Track kind="captions" src="/captions.vtt" label="English captions" />
					</Media.Video>
					<Icon aria-label="Settings" />
				</div>,
			);

			const image = screen.getByRole('img');
			const video = screen.getByRole('application'); // Video with controls
			const icon = screen.getByLabelText('Settings');

			expect(image).toHaveAccessibleName('A beautiful sunset over mountains');
			expect(video).toBeInTheDocument();
			expect(icon).toHaveAccessibleName('Settings');
		});

		it('Interactive components should announce state changes', async () =>
		{
			render(
				<div>
					<Dropdown>
						<Dropdown.Trigger>Options</Dropdown.Trigger>
						<Dropdown.Content>
							<Dropdown.Item>Option 1</Dropdown.Item>
							<Dropdown.Item>Option 2</Dropdown.Item>
						</Dropdown.Content>
					</Dropdown>
					<Field.Checkbox label="Enable notifications" />
				</div>,
			);

			const trigger = screen.getByRole('button', { name: 'Options' });
			const checkbox = screen.getByRole('checkbox');

			// Check initial states
			expect(trigger).toHaveAttribute('aria-expanded', 'false');
			expect(checkbox).not.toBeChecked();

			// Test state changes
			await user.click(trigger);
			expect(trigger).toHaveAttribute('aria-expanded', 'true');

			await user.click(checkbox);
			expect(checkbox).toBeChecked();
		});
	});

	/**
	 * WCAG compliance testing for interactive elements
	 */
	describe('WCAG Compliance for Interactive Elements', () =>
	{
		it('All interactive elements should be focusable', () =>
		{
			render(
				<div>
					<Button>Button</Button>
					<Field.Text label="Input" />
					<Field.Select label="Select" options={[]} />
					<Field.Checkbox label="Checkbox" />
					<Text.Link href="#test">Link</Text.Link>
				</div>,
			);

			const button = screen.getByRole('button');
			const input = screen.getByRole('textbox');
			const select = screen.getByRole('combobox');
			const checkbox = screen.getByRole('checkbox');
			const link = screen.getByRole('link');

			// All interactive elements should be focusable
			expect(button).not.toHaveAttribute('tabindex', '-1');
			expect(input).not.toHaveAttribute('tabindex', '-1');
			expect(select).not.toHaveAttribute('tabindex', '-1');
			expect(checkbox).not.toHaveAttribute('tabindex', '-1');
			expect(link).not.toHaveAttribute('tabindex', '-1');
		});

		it('Focus indicators should be visible', async () =>
		{
			render(
				<div>
					<Button>Focus me</Button>
					<Field.Text label="Focus me too" />
				</div>,
			);

			const button = screen.getByRole('button');
			const input = screen.getByRole('textbox');

			// Focus elements and check for focus indicators
			button.focus();
			expect(button).toHaveFocus();

			input.focus();
			expect(input).toHaveFocus();
		});

		it('Error states should be properly announced', () =>
		{
			render(
				<Form>
					<Field.Text
						label="Email"
						error="Please enter a valid email address"
						required
					/>
					<Field.Select
						label="Country"
						error="Please select a country"
						options={[]}
						required
					/>
				</Form>,
			);

			const emailInput = screen.getByLabelText('Email');
			const countrySelect = screen.getByLabelText('Country');

			// Check error states
			expect(emailInput).toHaveAttribute('aria-invalid', 'true');
			expect(countrySelect).toHaveAttribute('aria-invalid', 'true');

			// Check error descriptions
			expect(emailInput).toHaveAccessibleDescription();
			expect(countrySelect).toHaveAccessibleDescription();
		});

		it('Loading states should be properly announced', () =>
		{
			render(
				<div>
					<Button loading>Loading...</Button>
					<Indicator.Loading />
					<DataTable loading data={[]} columns={[]} />
				</div>,
			);

			const loadingButton = screen.getByRole('button');
			const loadingIndicator = screen.getByRole('status');

			expect(loadingButton).toHaveAttribute('aria-busy', 'true');
			expect(loadingIndicator).toBeInTheDocument();
		});

		it('Modal dialogs should trap focus', async () =>
		{
			render(
				<Modal open>
					<Modal.Content>
						<Modal.Header>
							<Modal.Title>Test Modal</Modal.Title>
							<Modal.Close>×</Modal.Close>
						</Modal.Header>
						<Modal.Body>
							<Field.Text label="Name" />
							<Button>Save</Button>
						</Modal.Body>
					</Modal.Content>
				</Modal>,
			);

			const modal = screen.getByRole('dialog');
			const closeButton = screen.getByRole('button', { name: '×' });
			const input = screen.getByRole('textbox');
			const saveButton = screen.getByRole('button', { name: 'Save' });

			expect(modal).toBeInTheDocument();
			expect(modal).toHaveAttribute('aria-modal', 'true');

			// Focus should be trapped within modal
			closeButton.focus();
			expect(document.activeElement).toBe(closeButton);

			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(input);

			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(saveButton);

			// Tab from last element should cycle back to first
			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(closeButton);
		});

		it('Tooltips should be accessible', async () =>
		{
			render(
				<div>
					<Popover>
						<Popover.Trigger aria-describedby="tooltip">
							Hover me
						</Popover.Trigger>
						<Popover.Content role="tooltip" id="tooltip">
							This is a tooltip
						</Popover.Content>
					</Popover>
				</div>,
			);

			const trigger = screen.getByRole('button');
			const tooltip = screen.getByRole('tooltip');

			expect(trigger).toHaveAttribute('aria-describedby', 'tooltip');
			expect(tooltip).toHaveAttribute('id', 'tooltip');
		});
	});

	/**
	 * Color contrast and visual accessibility
	 */
	describe('Visual Accessibility', () =>
	{
		it('Components should not rely solely on color for information', () =>
		{
			render(
				<div>
					<Message variant="error" icon="×">
						Error message with icon
					</Message>
					<Message variant="success" icon="✓">
						Success message with icon
					</Message>
					<Button variant="primary" disabled>
						Disabled button (with disabled attribute)
					</Button>
				</div>,
			);

			const errorMessage = screen.getByText('Error message with icon');
			const successMessage = screen.getByText('Success message with icon');
			const disabledButton = screen.getByRole('button');

			// Messages should have icons, not just color
			expect(errorMessage.parentElement).toContainHTML('×');
			expect(successMessage.parentElement).toContainHTML('✓');

			// Disabled button should have proper attribute
			expect(disabledButton).toBeDisabled();
		});

		it('Focus indicators should be visible and sufficient', async () =>
		{
			render(
				<div>
					<Button>Test Button</Button>
					<Field.Text label="Test Input" />
					<Text.Link href="#test">Test Link</Text.Link>
				</div>,
			);

			const button = screen.getByRole('button');
			const input = screen.getByRole('textbox');
			const link = screen.getByRole('link');

			// Focus each element and verify focus is visible
			button.focus();
			expect(button).toHaveFocus();

			input.focus();
			expect(input).toHaveFocus();

			link.focus();
			expect(link).toHaveFocus();
		});
	});
});
