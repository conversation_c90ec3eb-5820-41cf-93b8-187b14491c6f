#!/usr/bin/env node

/**
 * Comprehensive accessibility test runner
 * Executes all accessibility compliance tests and generates reports
 */

import { execSync } from 'child_process';
import { writeFileSync, mkdirSync, existsSync } from 'fs';
import { resolve } from 'path';

interface TestResult
{
	suite: string;
	passed: number;
	failed: number;
	skipped: number;
	duration: number;
	violations: Array<{
		rule: string;
		description: string;
		impact: string;
		nodes: number;
	}>;
}

interface AccessibilityReport
{
	timestamp: string;
	summary: {
		totalTests: number;
		totalPassed: number;
		totalFailed: number;
		totalSkipped: number;
		passRate: number;
		duration: number;
	};
	suites: TestResult[];
	recommendations: string[];
}

/**
 * Test suites to run
 */
const testSuites = [
	{
		name: 'Accessibility Compliance',
		file: 'accessibility-compliance.test.tsx',
		description: 'Comprehensive axe-core accessibility audits',
	},
	{
		name: 'Keyboard Navigation',
		file: 'keyboard-navigation.test.tsx',
		description: 'Keyboard navigation and interaction testing',
	},
	{
		name: 'Screen Reader Compatibility',
		file: 'screen-reader-compatibility.test.tsx',
		description: 'ARIA attributes and screen reader support',
	},
	{
		name: 'WCAG Compliance',
		file: 'wcag-compliance.test.tsx',
		description: 'WCAG 2.1 AA compliance testing',
	},
];

/**
 * Run a specific test suite
 */
const runTestSuite = async (suite: typeof testSuites[0]): Promise<TestResult> =>
{
	console.log(`\n🧪 Running ${suite.name} tests...`);

	const startTime = Date.now();

	try
	{
		const result = execSync(
			`pnpm vitest run src/test/${suite.file} --reporter=json`,
			{
				encoding: 'utf-8',
				cwd: resolve(__dirname, '..', '..'),
			},
		);

		const duration = Date.now() - startTime;
		const testResult = JSON.parse(result);

		const passed = testResult.numPassedTests || 0;
		const failed = testResult.numFailedTests || 0;
		const skipped = testResult.numPendingTests || 0;

		console.log(`✅ ${suite.name}: ${passed} passed, ${failed} failed, ${skipped} skipped`);

		return {
			suite: suite.name,
			passed,
			failed,
			skipped,
			duration,
			violations: [], // Would be populated from axe results in real implementation
		};
	}
	catch (error)
	{
		console.error(`❌ ${suite.name} failed:`, error);

		return {
			suite: suite.name,
			passed: 0,
			failed: 1,
			skipped: 0,
			duration: Date.now() - startTime,
			violations: [
				{
					rule: 'test-execution',
					description: 'Test suite failed to execute',
					impact: 'critical',
					nodes: 0,
				},
			],
		};
	}
};

/**
 * Generate accessibility recommendations based on test results
 */
const generateRecommendations = (results: TestResult[]): string[] =>
{
	const recommendations: string[] = [];

	const totalFailed = results.reduce((sum, result) => sum + result.failed, 0);
	const totalViolations = results.reduce((sum, result) => sum + result.violations.length, 0);

	if (totalFailed > 0)
	{
		recommendations.push(
			'🔧 Fix failing tests to ensure all components meet accessibility standards',
		);
	}

	if (totalViolations > 0)
	{
		recommendations.push(
			'🎯 Address accessibility violations found by axe-core audits',
		);
	}

	// Check for specific patterns
	const keyboardSuite = results.find(r => r.suite === 'Keyboard Navigation');
	if (keyboardSuite && keyboardSuite.failed > 0)
	{
		recommendations.push(
			'⌨️ Improve keyboard navigation support for better accessibility',
		);
	}

	const screenReaderSuite = results.find(r => r.suite === 'Screen Reader Compatibility');
	if (screenReaderSuite && screenReaderSuite.failed > 0)
	{
		recommendations.push(
			'🔊 Enhance ARIA attributes and screen reader compatibility',
		);
	}

	const wcagSuite = results.find(r => r.suite === 'WCAG Compliance');
	if (wcagSuite && wcagSuite.failed > 0)
	{
		recommendations.push(
			'📋 Address WCAG 2.1 AA compliance issues for better accessibility',
		);
	}

	if (recommendations.length === 0)
	{
		recommendations.push(
			'🎉 All accessibility tests are passing! Consider adding more comprehensive tests.',
		);
	}

	return recommendations;
};

/**
 * Generate HTML report
 */
const generateHTMLReport = (report: AccessibilityReport): string =>
{
	return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accessibility Test Report</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .metric {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            color: #666;
            font-size: 0.9em;
        }
        .pass { color: #28a745; }
        .fail { color: #dc3545; }
        .skip { color: #ffc107; }
        .suite {
            background: white;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .suite-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        .suite-title {
            margin: 0;
            color: #333;
        }
        .suite-stats {
            display: flex;
            gap: 20px;
            margin-top: 10px;
        }
        .recommendations {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
        }
        .recommendations h2 {
            margin-top: 0;
            color: #1976d2;
        }
        .recommendations ul {
            margin: 0;
            padding-left: 20px;
        }
        .recommendations li {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Accessibility Test Report</h1>
        <p>Generated on ${new Date(report.timestamp).toLocaleString()}</p>
        <p>Total duration: ${(report.summary.duration / 1000).toFixed(2)}s</p>
    </div>

    <div class="summary">
        <div class="metric">
            <div class="metric-value">${report.summary.totalTests}</div>
            <div class="metric-label">Total Tests</div>
        </div>
        <div class="metric">
            <div class="metric-value pass">${report.summary.totalPassed}</div>
            <div class="metric-label">Passed</div>
        </div>
        <div class="metric">
            <div class="metric-value fail">${report.summary.totalFailed}</div>
            <div class="metric-label">Failed</div>
        </div>
        <div class="metric">
            <div class="metric-value skip">${report.summary.totalSkipped}</div>
            <div class="metric-label">Skipped</div>
        </div>
        <div class="metric">
            <div class="metric-value">${report.summary.passRate.toFixed(1)}%</div>
            <div class="metric-label">Pass Rate</div>
        </div>
    </div>

    <h2>📊 Test Suites</h2>
    ${report.suites.map(suite => `
        <div class="suite">
            <div class="suite-header">
                <h3 class="suite-title">${suite.suite}</h3>
                <div class="suite-stats">
                    <span class="pass">✅ ${suite.passed} passed</span>
                    <span class="fail">❌ ${suite.failed} failed</span>
                    <span class="skip">⏭️ ${suite.skipped} skipped</span>
                    <span>⏱️ ${(suite.duration / 1000).toFixed(2)}s</span>
                </div>
            </div>
        </div>
    `).join('')}

    <div class="recommendations">
        <h2>💡 Recommendations</h2>
        <ul>
            ${report.recommendations.map(rec => `<li>${rec}</li>`).join('')}
        </ul>
    </div>
</body>
</html>
`;
};

/**
 * Main execution function
 */
const main = async () =>
{
	console.log('🚀 Starting comprehensive accessibility testing...\n');

	const startTime = Date.now();
	const results: TestResult[] = [];

	// Run all test suites
	for (const suite of testSuites)
	{
		const result = await runTestSuite(suite);
		results.push(result);
	}

	const totalDuration = Date.now() - startTime;

	// Calculate summary
	const summary = {
		totalTests: results.reduce((sum, r) => sum + r.passed + r.failed + r.skipped, 0),
		totalPassed: results.reduce((sum, r) => sum + r.passed, 0),
		totalFailed: results.reduce((sum, r) => sum + r.failed, 0),
		totalSkipped: results.reduce((sum, r) => sum + r.skipped, 0),
		passRate: 0,
		duration: totalDuration,
	};

	summary.passRate = summary.totalTests > 0
		? (summary.totalPassed / summary.totalTests) * 100
		: 0;

	// Generate recommendations
	const recommendations = generateRecommendations(results);

	// Create report
	const report: AccessibilityReport = {
		timestamp: new Date().toISOString(),
		summary,
		suites: results,
		recommendations,
	};

	// Ensure reports directory exists
	const reportsDir = resolve(__dirname, '..', '..', 'reports');
	if (!existsSync(reportsDir))
	{
		mkdirSync(reportsDir, { recursive: true });
	}

	// Write JSON report
	const jsonReportPath = resolve(reportsDir, 'accessibility-report.json');
	writeFileSync(jsonReportPath, JSON.stringify(report, null, 2));

	// Write HTML report
	const htmlReportPath = resolve(reportsDir, 'accessibility-report.html');
	writeFileSync(htmlReportPath, generateHTMLReport(report));

	// Print summary
	console.log('\n📋 Test Summary:');
	console.log(`   Total Tests: ${summary.totalTests}`);
	console.log(`   Passed: ${summary.totalPassed}`);
	console.log(`   Failed: ${summary.totalFailed}`);
	console.log(`   Skipped: ${summary.totalSkipped}`);
	console.log(`   Pass Rate: ${summary.passRate.toFixed(1)}%`);
	console.log(`   Duration: ${(summary.duration / 1000).toFixed(2)}s`);

	console.log('\n💡 Recommendations:');
	recommendations.forEach(rec => console.log(`   ${rec}`));

	console.log(`\n📄 Reports generated:`);
	console.log(`   JSON: ${jsonReportPath}`);
	console.log(`   HTML: ${htmlReportPath}`);

	// Exit with appropriate code
	process.exit(summary.totalFailed > 0 ? 1 : 0);
};

// Run if called directly
if (require.main === module)
{
	main().catch((error) =>
	{
		console.error('❌ Test runner failed:', error);
		process.exit(1);
	});
}

export { main as runAllAccessibilityTests };
