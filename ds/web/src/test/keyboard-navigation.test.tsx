import { describe, it, expect, beforeEach } from 'vitest';
import { render, screen, cleanup } from '@testing-library/react';
import { userEvent } from '@testing-library/user-event';

// Import components for keyboard navigation testing
import { Button } from '@/components/Element.Button';
import { Field } from '@/components/Base/Field';
import { Menu } from '@/components/Element.Menu';
import { List } from '@/components/Element.List';
import { Table } from '@/components/Element.Table';
import { Tabs } from '@/components/Module.Tabs';
import { Dropdown } from '@/components/Module.Dropdown';
import { Popover } from '@/components/Module.Popover';
import { Modal } from '@/components/Factory.Modal';
import { Form } from '@/components/Factory.Form';
import { DataTable } from '@/components/Factory.DataTable';
import { Pagination } from '@/components/Module.Pagination';
import { Slider } from '@/components/Module.Slider';
import { Search } from '@/components/Factory.Search';

/**
 * Comprehensive keyboard navigation testing across the component library
 * Tests Tab, Shift+Tab, Arrow keys, Enter, Space, and Escape functionality
 */
describe('Keyboard Navigation Testing', () =>
{
	let user: ReturnType<typeof userEvent.setup>;

	beforeEach(() =>
	{
		user = userEvent.setup();
		cleanup();
	});

	/**
	 * Basic Tab navigation tests
	 */
	describe('Tab Navigation', () =>
	{
		it('should navigate through buttons with Tab and Shift+Tab', async () =>
		{
			render(
				<div>
					<Button>First Button</Button>
					<Button>Second Button</Button>
					<Button>Third Button</Button>
					<Button disabled>Disabled Button</Button>
					<Button>Last Button</Button>
				</div>,
			);

			const buttons = screen.getAllByRole('button').filter(btn => !btn.hasAttribute('disabled'));

			// Start from first button
			buttons[0].focus();
			expect(document.activeElement).toBe(buttons[0]);

			// Tab forward through enabled buttons
			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(buttons[1]);

			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(buttons[2]);

			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(buttons[3]); // Skip disabled button

			// Tab backward with Shift+Tab
			await user.keyboard('{Shift>}{Tab}{/Shift}');
			expect(document.activeElement).toBe(buttons[2]);

			await user.keyboard('{Shift>}{Tab}{/Shift}');
			expect(document.activeElement).toBe(buttons[1]);
		});

		it('should navigate through form fields in logical order', async () =>
		{
			render(
				<Form>
					<Field.Text label="First Name" />
					<Field.Text label="Last Name" />
					<Field.Select label="Country" options={[{ value: 'us', label: 'US' }]} />
					<Field.Checkbox label="Subscribe to newsletter" />
					<Field.Radio name="gender" value="male" label="Male" />
					<Field.Radio name="gender" value="female" label="Female" />
					<Button type="submit">Submit</Button>
				</Form>,
			);

			const firstName = screen.getByLabelText('First Name');
			const lastName = screen.getByLabelText('Last Name');
			const country = screen.getByLabelText('Country');
			const checkbox = screen.getByLabelText('Subscribe to newsletter');
			const maleRadio = screen.getByLabelText('Male');
			const femaleRadio = screen.getByLabelText('Female');
			const submitButton = screen.getByRole('button', { name: 'Submit' });

			// Navigate through form in order
			firstName.focus();
			expect(document.activeElement).toBe(firstName);

			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(lastName);

			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(country);

			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(checkbox);

			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(maleRadio);

			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(femaleRadio);

			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(submitButton);
		});

		it('should handle complex component tab order', async () =>
		{
			render(
				<div>
					<Button>Before Dropdown</Button>
					<Dropdown>
						<Dropdown.Trigger>Open Menu</Dropdown.Trigger>
						<Dropdown.Content>
							<Dropdown.Item>Item 1</Dropdown.Item>
							<Dropdown.Item>Item 2</Dropdown.Item>
						</Dropdown.Content>
					</Dropdown>
					<Button>After Dropdown</Button>
				</div>,
			);

			const beforeButton = screen.getByRole('button', { name: 'Before Dropdown' });
			const dropdownTrigger = screen.getByRole('button', { name: 'Open Menu' });
			const afterButton = screen.getByRole('button', { name: 'After Dropdown' });

			// Navigate through components
			beforeButton.focus();
			expect(document.activeElement).toBe(beforeButton);

			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(dropdownTrigger);

			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(afterButton);
		});
	});

	/**
	 * Arrow key navigation tests
	 */
	describe('Arrow Key Navigation', () =>
	{
		it('should navigate menu items with arrow keys', async () =>
		{
			render(
				<Menu>
					<Menu.Item>Home</Menu.Item>
					<Menu.Item>About</Menu.Item>
					<Menu.Item>Services</Menu.Item>
					<Menu.Item>Contact</Menu.Item>
				</Menu>,
			);

			const menuItems = screen.getAllByRole('menuitem');

			// Focus first item
			menuItems[0].focus();
			expect(document.activeElement).toBe(menuItems[0]);

			// Navigate down with arrow keys
			await user.keyboard('{ArrowDown}');
			expect(document.activeElement).toBe(menuItems[1]);

			await user.keyboard('{ArrowDown}');
			expect(document.activeElement).toBe(menuItems[2]);

			await user.keyboard('{ArrowDown}');
			expect(document.activeElement).toBe(menuItems[3]);

			// Navigate up with arrow keys
			await user.keyboard('{ArrowUp}');
			expect(document.activeElement).toBe(menuItems[2]);

			await user.keyboard('{ArrowUp}');
			expect(document.activeElement).toBe(menuItems[1]);
		});

		it('should navigate tabs with arrow keys', async () =>
		{
			render(
				<Tabs defaultValue="tab1">
					<Tabs.List>
						<Tabs.Trigger value="tab1">Tab 1</Tabs.Trigger>
						<Tabs.Trigger value="tab2">Tab 2</Tabs.Trigger>
						<Tabs.Trigger value="tab3">Tab 3</Tabs.Trigger>
					</Tabs.List>
					<Tabs.Content value="tab1">Content 1</Tabs.Content>
					<Tabs.Content value="tab2">Content 2</Tabs.Content>
					<Tabs.Content value="tab3">Content 3</Tabs.Content>
				</Tabs>,
			);

			const tabTriggers = screen.getAllByRole('tab');

			// Focus first tab
			tabTriggers[0].focus();
			expect(document.activeElement).toBe(tabTriggers[0]);

			// Navigate right with arrow keys
			await user.keyboard('{ArrowRight}');
			expect(document.activeElement).toBe(tabTriggers[1]);

			await user.keyboard('{ArrowRight}');
			expect(document.activeElement).toBe(tabTriggers[2]);

			// Navigate left with arrow keys
			await user.keyboard('{ArrowLeft}');
			expect(document.activeElement).toBe(tabTriggers[1]);

			await user.keyboard('{ArrowLeft}');
			expect(document.activeElement).toBe(tabTriggers[0]);
		});

		it('should navigate table cells with arrow keys', async () =>
		{
			render(
				<Table>
					<Table.Header>
						<Table.Row>
							<Table.HeaderCell tabIndex={0}>Name</Table.HeaderCell>
							<Table.HeaderCell tabIndex={0}>Age</Table.HeaderCell>
							<Table.HeaderCell tabIndex={0}>City</Table.HeaderCell>
						</Table.Row>
					</Table.Header>
					<Table.Body>
						<Table.Row>
							<Table.Cell tabIndex={0}>John</Table.Cell>
							<Table.Cell tabIndex={0}>30</Table.Cell>
							<Table.Cell tabIndex={0}>NYC</Table.Cell>
						</Table.Row>
						<Table.Row>
							<Table.Cell tabIndex={0}>Jane</Table.Cell>
							<Table.Cell tabIndex={0}>25</Table.Cell>
							<Table.Cell tabIndex={0}>LA</Table.Cell>
						</Table.Row>
					</Table.Body>
				</Table>,
			);

			const headerCells = screen.getAllByRole('columnheader');
			const dataCells = screen.getAllByRole('cell');

			// Focus first header cell
			headerCells[0].focus();
			expect(document.activeElement).toBe(headerCells[0]);

			// Navigate right through headers
			await user.keyboard('{ArrowRight}');
			expect(document.activeElement).toBe(headerCells[1]);

			await user.keyboard('{ArrowRight}');
			expect(document.activeElement).toBe(headerCells[2]);

			// Navigate down to data cells
			await user.keyboard('{ArrowDown}');
			expect(document.activeElement).toBe(dataCells[2]); // Same column, first data row
		});

		it('should handle circular navigation in menus', async () =>
		{
			render(
				<Menu>
					<Menu.Item>First</Menu.Item>
					<Menu.Item>Second</Menu.Item>
					<Menu.Item>Third</Menu.Item>
				</Menu>,
			);

			const menuItems = screen.getAllByRole('menuitem');

			// Start at first item
			menuItems[0].focus();
			expect(document.activeElement).toBe(menuItems[0]);

			// Navigate up from first item should go to last
			await user.keyboard('{ArrowUp}');
			expect(document.activeElement).toBe(menuItems[2]);

			// Navigate down from last item should go to first
			await user.keyboard('{ArrowDown}');
			expect(document.activeElement).toBe(menuItems[0]);
		});
	});

	/**
	 * Enter and Space key activation tests
	 */
	describe('Enter and Space Key Activation', () =>
	{
		it('should activate buttons with Enter and Space', async () =>
		{
			const handleClick = vi.fn();

			render(
				<div>
					<Button onClick={handleClick}>Click Me</Button>
					<Button onClick={handleClick} type="submit">Submit</Button>
				</div>,
			);

			const buttons = screen.getAllByRole('button');

			// Test Enter key activation
			buttons[0].focus();
			await user.keyboard('{Enter}');
			expect(handleClick).toHaveBeenCalledTimes(1);

			// Test Space key activation
			buttons[1].focus();
			await user.keyboard(' ');
			expect(handleClick).toHaveBeenCalledTimes(2);
		});

		it('should toggle checkboxes with Space', async () =>
		{
			render(
				<Form>
					<Field.Checkbox label="Option 1" />
					<Field.Checkbox label="Option 2" defaultChecked />
				</Form>,
			);

			const checkboxes = screen.getAllByRole('checkbox');

			// Toggle unchecked checkbox
			checkboxes[0].focus();
			expect(checkboxes[0]).not.toBeChecked();

			await user.keyboard(' ');
			expect(checkboxes[0]).toBeChecked();

			// Toggle checked checkbox
			checkboxes[1].focus();
			expect(checkboxes[1]).toBeChecked();

			await user.keyboard(' ');
			expect(checkboxes[1]).not.toBeChecked();
		});

		it('should select radio buttons with Space and arrow keys', async () =>
		{
			render(
				<Form>
					<Field.Radio name="size" value="small" label="Small" />
					<Field.Radio name="size" value="medium" label="Medium" />
					<Field.Radio name="size" value="large" label="Large" />
				</Form>,
			);

			const radioButtons = screen.getAllByRole('radio');

			// Select first radio with Space
			radioButtons[0].focus();
			await user.keyboard(' ');
			expect(radioButtons[0]).toBeChecked();

			// Navigate and select with arrow keys
			await user.keyboard('{ArrowDown}');
			expect(document.activeElement).toBe(radioButtons[1]);
			expect(radioButtons[1]).toBeChecked();
			expect(radioButtons[0]).not.toBeChecked();
		});

		it('should open dropdowns with Enter and Space', async () =>
		{
			render(
				<Dropdown>
					<Dropdown.Trigger>Options</Dropdown.Trigger>
					<Dropdown.Content>
						<Dropdown.Item>Option 1</Dropdown.Item>
						<Dropdown.Item>Option 2</Dropdown.Item>
					</Dropdown.Content>
				</Dropdown>,
			);

			const trigger = screen.getByRole('button', { name: 'Options' });

			// Test Enter key
			trigger.focus();
			expect(trigger).toHaveAttribute('aria-expanded', 'false');

			await user.keyboard('{Enter}');
			expect(trigger).toHaveAttribute('aria-expanded', 'true');

			// Close and test Space key
			await user.keyboard('{Escape}');
			expect(trigger).toHaveAttribute('aria-expanded', 'false');

			await user.keyboard(' ');
			expect(trigger).toHaveAttribute('aria-expanded', 'true');
		});
	});

	/**
	 * Escape key functionality tests
	 */
	describe('Escape Key Functionality', () =>
	{
		it('should close dropdowns with Escape', async () =>
		{
			render(
				<Dropdown>
					<Dropdown.Trigger>Menu</Dropdown.Trigger>
					<Dropdown.Content>
						<Dropdown.Item>Item 1</Dropdown.Item>
						<Dropdown.Item>Item 2</Dropdown.Item>
					</Dropdown.Content>
				</Dropdown>,
			);

			const trigger = screen.getByRole('button', { name: 'Menu' });

			// Open dropdown
			trigger.focus();
			await user.keyboard('{Enter}');
			expect(trigger).toHaveAttribute('aria-expanded', 'true');

			// Close with Escape
			await user.keyboard('{Escape}');
			expect(trigger).toHaveAttribute('aria-expanded', 'false');
			expect(document.activeElement).toBe(trigger); // Focus returns to trigger
		});

		it('should close modals with Escape', async () =>
		{
			const handleClose = vi.fn();

			render(
				<Modal open onClose={handleClose}>
					<Modal.Content>
						<Modal.Header>
							<Modal.Title>Test Modal</Modal.Title>
						</Modal.Header>
						<Modal.Body>
							<Button>Inside Modal</Button>
						</Modal.Body>
					</Modal.Content>
				</Modal>,
			);

			const modal = screen.getByRole('dialog');
			const button = screen.getByRole('button', { name: 'Inside Modal' });

			expect(modal).toBeInTheDocument();

			// Focus element inside modal
			button.focus();
			expect(document.activeElement).toBe(button);

			// Close modal with Escape
			await user.keyboard('{Escape}');
			expect(handleClose).toHaveBeenCalled();
		});

		it('should close popovers with Escape', async () =>
		{
			render(
				<Popover>
					<Popover.Trigger>Show Popover</Popover.Trigger>
					<Popover.Content>
						<p>Popover content</p>
						<Button>Action</Button>
					</Popover.Content>
				</Popover>,
			);

			const trigger = screen.getByRole('button', { name: 'Show Popover' });

			// Open popover
			trigger.focus();
			await user.keyboard('{Enter}');

			// Close with Escape
			await user.keyboard('{Escape}');
			expect(document.activeElement).toBe(trigger); // Focus returns to trigger
		});
	});

	/**
	 * Focus trap testing for modal components
	 */
	describe('Focus Trap Testing', () =>
	{
		it('should trap focus within modal dialogs', async () =>
		{
			render(
				<Modal open>
					<Modal.Content>
						<Modal.Header>
							<Modal.Title>Test Modal</Modal.Title>
							<Modal.Close>×</Modal.Close>
						</Modal.Header>
						<Modal.Body>
							<Field.Text label="Name" />
							<Button>Save</Button>
							<Button>Cancel</Button>
						</Modal.Body>
					</Modal.Content>
				</Modal>,
			);

			const closeButton = screen.getByRole('button', { name: '×' });
			const nameInput = screen.getByLabelText('Name');
			const saveButton = screen.getByRole('button', { name: 'Save' });
			const cancelButton = screen.getByRole('button', { name: 'Cancel' });

			// Focus should start at first focusable element
			closeButton.focus();
			expect(document.activeElement).toBe(closeButton);

			// Tab through modal elements
			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(nameInput);

			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(saveButton);

			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(cancelButton);

			// Tab from last element should cycle to first
			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(closeButton);

			// Shift+Tab should go backwards
			await user.keyboard('{Shift>}{Tab}{/Shift}');
			expect(document.activeElement).toBe(cancelButton);
		});

		it('should trap focus within dropdown menus', async () =>
		{
			render(
				<Dropdown>
					<Dropdown.Trigger>Menu</Dropdown.Trigger>
					<Dropdown.Content>
						<Dropdown.Item>Item 1</Dropdown.Item>
						<Dropdown.Item>Item 2</Dropdown.Item>
						<Dropdown.Item>Item 3</Dropdown.Item>
					</Dropdown.Content>
				</Dropdown>,
			);

			const trigger = screen.getByRole('button', { name: 'Menu' });

			// Open dropdown
			trigger.focus();
			await user.keyboard('{Enter}');

			const menuItems = screen.getAllByRole('menuitem');

			// Focus should move to first menu item
			expect(document.activeElement).toBe(menuItems[0]);

			// Arrow navigation within menu
			await user.keyboard('{ArrowDown}');
			expect(document.activeElement).toBe(menuItems[1]);

			await user.keyboard('{ArrowDown}');
			expect(document.activeElement).toBe(menuItems[2]);

			// Tab should not leave the menu
			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(menuItems[0]); // Cycles within menu
		});
	});

	/**
	 * Complex component keyboard navigation
	 */
	describe('Complex Component Navigation', () =>
	{
		it('should handle DataTable keyboard navigation', async () =>
		{
			const data = [
				{ id: 1, name: 'John', age: 30 },
				{ id: 2, name: 'Jane', age: 25 },
			];

			const columns = [
				{ key: 'name', header: 'Name' },
				{ key: 'age', header: 'Age' },
			];

			render(<DataTable data={data} columns={columns} />);

			// Test navigation through table elements
			const table = screen.getByRole('table');
			expect(table).toBeInTheDocument();

			// Focus should be manageable within table
			const firstCell = table.querySelector('td');
			if (firstCell)
			{
				(firstCell as HTMLElement).focus();
				expect(document.activeElement).toBe(firstCell);
			}
		});

		it('should handle Pagination keyboard navigation', async () =>
		{
			render(<Pagination total={100} page={5} />);

			const prevButton = screen.getByRole('button', { name: /previous/i });
			const nextButton = screen.getByRole('button', { name: /next/i });
			const pageButtons = screen.getAllByRole('button').filter(btn =>
				/^\d+$/.test(btn.textContent || '')
			);

			// Navigate through pagination controls
			prevButton.focus();
			expect(document.activeElement).toBe(prevButton);

			await user.keyboard('{Tab}');
			if (pageButtons.length > 0)
			{
				expect(document.activeElement).toBe(pageButtons[0]);
			}
		});

		it('should handle Search component keyboard navigation', async () =>
		{
			const handleSearch = vi.fn();

			render(
				<Search
					onSearch={handleSearch}
					placeholder="Search..."
				/>,
			);

			const searchInput = screen.getByRole('textbox');
			const searchButton = screen.getByRole('button');

			// Navigate between search input and button
			searchInput.focus();
			expect(document.activeElement).toBe(searchInput);

			await user.keyboard('{Tab}');
			expect(document.activeElement).toBe(searchButton);

			// Enter in search input should trigger search
			searchInput.focus();
			await user.type(searchInput, 'test query');
			await user.keyboard('{Enter}');
			expect(handleSearch).toHaveBeenCalledWith('test query');
		});

		it('should handle Slider keyboard navigation', async () =>
		{
			render(<Slider defaultValue={50} min={0} max={100} />);

			const slider = screen.getByRole('slider');

			slider.focus();
			expect(document.activeElement).toBe(slider);

			// Arrow keys should change value
			await user.keyboard('{ArrowRight}');
			expect(slider).toHaveValue('51');

			await user.keyboard('{ArrowLeft}');
			expect(slider).toHaveValue('50');

			// Page Up/Down for larger increments
			await user.keyboard('{PageUp}');
			expect(parseInt(slider.value)).toBeGreaterThan(50);

			await user.keyboard('{PageDown}');
			expect(parseInt(slider.value)).toBeLessThan(60);
		});
	});

	/**
	 * Keyboard shortcuts and hotkeys
	 */
	describe('Keyboard Shortcuts', () =>
	{
		it('should support common keyboard shortcuts in forms', async () =>
		{
			const handleSubmit = vi.fn();

			render(
				<Form onSubmit={handleSubmit}>
					<Field.Text label="Name" />
					<Field.Text label="Email" />
					<Button type="submit">Submit</Button>
				</Form>,
			);

			const nameInput = screen.getByLabelText('Name');

			// Ctrl+Enter should submit form from any field
			nameInput.focus();
			await user.type(nameInput, 'John Doe');
			await user.keyboard('{Control>}{Enter}{/Control}');
			expect(handleSubmit).toHaveBeenCalled();
		});

		it('should support accessibility shortcuts', async () =>
		{
			render(
				<div>
					<Button accessKey="s">Save</Button>
					<Button accessKey="c">Cancel</Button>
				</div>,
			);

			const saveButton = screen.getByRole('button', { name: 'Save' });
			const cancelButton = screen.getByRole('button', { name: 'Cancel' });

			expect(saveButton).toHaveAttribute('accesskey', 's');
			expect(cancelButton).toHaveAttribute('accesskey', 'c');
		});
	});
});
