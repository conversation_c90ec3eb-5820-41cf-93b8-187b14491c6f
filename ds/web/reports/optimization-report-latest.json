{"timestamp": "2025-07-22T15:01:24.245Z", "version": "0.1.0", "summary": {"totalBundles": 12, "failedBundles": 0, "warningBundles": 0, "totalSize": {"rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "criticalPathSize": 0, "criticalPathBudget": 200}, "treeShakingEffectiveness": 0, "codeSplittingRatio": 0}, "bundleAnalysis": {"results": [{"filename": "index.es.js", "config": {"name": "Main Bundle", "maxSize": 120, "warningSize": 100, "description": "Main library entry point with core components", "priority": "critical", "loadTiming": "immediate", "cacheStrategy": "aggressive"}, "compression": {"raw": 0, "gzipped": 0, "brotli": 0, "gzipRatio": 0, "brotliRatio": 0, "brotliImprovement": 0}, "sizeCheck": {"violation": false, "warning": false, "config": {"name": "Main Bundle", "maxSize": 120, "warningSize": 100, "description": "Main library entry point with core components", "priority": "critical", "loadTiming": "immediate", "cacheStrategy": "aggressive"}, "actualSize": 0, "excess": 0, "remaining": 120}, "status": "ok"}, {"filename": "core/index.es.js", "config": {"name": "Core Components", "maxSize": 40, "warningSize": 30, "description": "Document structure and basic layout components", "priority": "critical", "loadTiming": "immediate", "cacheStrategy": "aggressive"}, "compression": {"raw": 0, "gzipped": 0, "brotli": 0, "gzipRatio": 0, "brotliRatio": 0, "brotliImprovement": 0}, "sizeCheck": {"violation": false, "warning": false, "config": {"name": "Core Components", "maxSize": 40, "warningSize": 30, "description": "Document structure and basic layout components", "priority": "critical", "loadTiming": "immediate", "cacheStrategy": "aggressive"}, "actualSize": 0, "excess": 0, "remaining": 40}, "status": "ok"}, {"filename": "elements/index.es.js", "config": {"name": "Element Components", "maxSize": 60, "warningSize": 45, "description": "Interactive and display elements", "priority": "critical", "loadTiming": "immediate", "cacheStrategy": "aggressive"}, "compression": {"raw": 0, "gzipped": 0, "brotli": 0, "gzipRatio": 0, "brotliRatio": 0, "brotliImprovement": 0}, "sizeCheck": {"violation": false, "warning": false, "config": {"name": "Element Components", "maxSize": 60, "warningSize": 45, "description": "Interactive and display elements", "priority": "critical", "loadTiming": "immediate", "cacheStrategy": "aggressive"}, "actualSize": 0, "excess": 0, "remaining": 60}, "status": "ok"}, {"filename": "constants.es.js", "config": {"name": "Constants", "maxSize": 15, "warningSize": 10, "description": "Theme tokens and design constants", "priority": "critical", "loadTiming": "immediate", "cacheStrategy": "aggressive"}, "compression": {"raw": 0, "gzipped": 0, "brotli": 0, "gzipRatio": 0, "brotliRatio": 0, "brotliImprovement": 0}, "sizeCheck": {"violation": false, "warning": false, "config": {"name": "Constants", "maxSize": 15, "warningSize": 10, "description": "Theme tokens and design constants", "priority": "critical", "loadTiming": "immediate", "cacheStrategy": "aggressive"}, "actualSize": 0, "excess": 0, "remaining": 15}, "status": "ok"}, {"filename": "hooks.es.js", "config": {"name": "<PERSON>s", "maxSize": 10, "warningSize": 8, "description": "React hooks and utilities", "priority": "critical", "loadTiming": "immediate", "cacheStrategy": "aggressive"}, "compression": {"raw": 0, "gzipped": 0, "brotli": 0, "gzipRatio": 0, "brotliRatio": 0, "brotliImprovement": 0}, "sizeCheck": {"violation": false, "warning": false, "config": {"name": "<PERSON>s", "maxSize": 10, "warningSize": 8, "description": "React hooks and utilities", "priority": "critical", "loadTiming": "immediate", "cacheStrategy": "aggressive"}, "actualSize": 0, "excess": 0, "remaining": 10}, "status": "ok"}, {"filename": "utils.es.js", "config": {"name": "Utils", "maxSize": 20, "warningSize": 15, "description": "Utility functions and helpers", "priority": "critical", "loadTiming": "immediate", "cacheStrategy": "aggressive"}, "compression": {"raw": 0, "gzipped": 0, "brotli": 0, "gzipRatio": 0, "brotliRatio": 0, "brotliImprovement": 0}, "sizeCheck": {"violation": false, "warning": false, "config": {"name": "Utils", "maxSize": 20, "warningSize": 15, "description": "Utility functions and helpers", "priority": "critical", "loadTiming": "immediate", "cacheStrategy": "aggressive"}, "actualSize": 0, "excess": 0, "remaining": 20}, "status": "ok"}, {"filename": "collections/index.es.js", "config": {"name": "Collection Components", "maxSize": 35, "warningSize": 25, "description": "Grouped content and status indicators", "priority": "standard", "loadTiming": "on-demand", "cacheStrategy": "standard"}, "compression": {"raw": 0, "gzipped": 0, "brotli": 0, "gzipRatio": 0, "brotliRatio": 0, "brotliImprovement": 0}, "sizeCheck": {"violation": false, "warning": false, "config": {"name": "Collection Components", "maxSize": 35, "warningSize": 25, "description": "Grouped content and status indicators", "priority": "standard", "loadTiming": "on-demand", "cacheStrategy": "standard"}, "actualSize": 0, "excess": 0, "remaining": 35}, "status": "ok"}, {"filename": "modules/index.es.js", "config": {"name": "Module Components", "maxSize": 50, "warningSize": 40, "description": "Complex interactive patterns", "priority": "standard", "loadTiming": "on-demand", "cacheStrategy": "standard"}, "compression": {"raw": 0, "gzipped": 0, "brotli": 0, "gzipRatio": 0, "brotliRatio": 0, "brotliImprovement": 0}, "sizeCheck": {"violation": false, "warning": false, "config": {"name": "Module Components", "maxSize": 50, "warningSize": 40, "description": "Complex interactive patterns", "priority": "standard", "loadTiming": "on-demand", "cacheStrategy": "standard"}, "actualSize": 0, "excess": 0, "remaining": 50}, "status": "ok"}, {"filename": "base/index.es.js", "config": {"name": "Base Components", "maxSize": 25, "warningSize": 20, "description": "Low-level building blocks", "priority": "standard", "loadTiming": "on-demand", "cacheStrategy": "standard"}, "compression": {"raw": 0, "gzipped": 0, "brotli": 0, "gzipRatio": 0, "brotliRatio": 0, "brotliImprovement": 0}, "sizeCheck": {"violation": false, "warning": false, "config": {"name": "Base Components", "maxSize": 25, "warningSize": 20, "description": "Low-level building blocks", "priority": "standard", "loadTiming": "on-demand", "cacheStrategy": "standard"}, "actualSize": 0, "excess": 0, "remaining": 25}, "status": "ok"}, {"filename": "factories-light.es.js", "config": {"name": "Light Factories", "maxSize": 60, "warningSize": 45, "description": "Lightweight factory components (Layout, Toast)", "priority": "factory", "loadTiming": "lazy", "cacheStrategy": "long-term", "dependencies": ["minimal"]}, "compression": {"raw": 0, "gzipped": 0, "brotli": 0, "gzipRatio": 0, "brotliRatio": 0, "brotliImprovement": 0}, "sizeCheck": {"violation": false, "warning": false, "config": {"name": "Light Factories", "maxSize": 60, "warningSize": 45, "description": "Lightweight factory components (Layout, Toast)", "priority": "factory", "loadTiming": "lazy", "cacheStrategy": "long-term", "dependencies": ["minimal"]}, "actualSize": 0, "excess": 0, "remaining": 60}, "status": "ok"}, {"filename": "factories.es.js", "config": {"name": "Standard Factories", "maxSize": 80, "warningSize": 60, "description": "Standard factory components with moderate dependencies", "priority": "factory", "loadTiming": "lazy", "cacheStrategy": "long-term", "dependencies": ["moderate"]}, "compression": {"raw": 0, "gzipped": 0, "brotli": 0, "gzipRatio": 0, "brotliRatio": 0, "brotliImprovement": 0}, "sizeCheck": {"violation": false, "warning": false, "config": {"name": "Standard Factories", "maxSize": 80, "warningSize": 60, "description": "Standard factory components with moderate dependencies", "priority": "factory", "loadTiming": "lazy", "cacheStrategy": "long-term", "dependencies": ["moderate"]}, "actualSize": 0, "excess": 0, "remaining": 80}, "status": "ok"}, {"filename": "factories-heavy.es.js", "config": {"name": "Heavy Factories", "maxSize": 250, "warningSize": 200, "description": "Heavy factory components (Editor, Player) with large dependencies", "priority": "factory", "loadTiming": "lazy", "cacheStrategy": "long-term", "dependencies": ["@lexical/*", "@vidstack/react"]}, "compression": {"raw": 0, "gzipped": 0, "brotli": 0, "gzipRatio": 0, "brotliRatio": 0, "brotliImprovement": 0}, "sizeCheck": {"violation": false, "warning": false, "config": {"name": "Heavy Factories", "maxSize": 250, "warningSize": 200, "description": "Heavy factory components (Editor, Player) with large dependencies", "priority": "factory", "loadTiming": "lazy", "cacheStrategy": "long-term", "dependencies": ["@lexical/*", "@vidstack/react"]}, "actualSize": 0, "excess": 0, "remaining": 250}, "status": "ok"}], "violations": [], "warnings": [], "totals": {"rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "criticalPathSize": 0, "criticalPathBudget": 200}, "analysis": {"compressionRatio": null, "brotliImprovement": null, "criticalPathExceeded": false}}, "treeShakingAnalysis": {"totalFiles": 398, "totalExports": 1936, "barrelExports": 179, "namespaceImports": 2, "sideEffectFiles": 82, "dynamicImports": 20, "issues": [{"file": "base.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "collections.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Base/Clone/Clone.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: SlotBasic", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Clone/Clone.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: PropMerging", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Clone/Clone.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: RefHandling", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Clone/Clone.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CloneComponent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Clone/Clone.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ComplexComposition", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Clone/Clone.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Accessibility", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Clone/Clone.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Base/Clone/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Base/Code/Code.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: codeStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Code/Code.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Base/Code/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Base/Drip/Drip.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Default", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Drip/Drip.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CustomColors", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Drip/Drip.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: DifferentSizes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Drip/Drip.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: DifferentDurations", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Drip/Drip.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: DisabledDrip", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Drip/Drip.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CardWithDrip", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Drip/Drip.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Accessibility", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Drip/Drip.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dripStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Drip/Drip.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Base/Drip/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Base/Field/Field.Text.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: InputTypes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.Text.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Textarea", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.Text.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Sizes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.Text.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Colors", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.Text.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Variants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.Text.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: States", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.Text.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithFieldWrapper", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.Text.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ControlledInput", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.Text.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Accessibility", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Default", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithTextInput", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithError", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: HorizontalLayout", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithCheckbox", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithSelect", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithValidation", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: MultipleFields", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Accessibility", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: fieldWrapper", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: fieldWrapperVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: fieldLabel", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: fieldLabelVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: fieldDescription", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: fieldErrorMessage", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: inputRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: textareaInput", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: textareaResizeVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectInput", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: checkboxContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: radioVisual", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: switchContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: switchThumb", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: fileInput", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: rangeInput", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: rangeValueLabel", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: colorInput", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Field/Field.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Base/Field/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Base/Flex/Flex.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Default", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Flex/Flex.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Directions", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Flex/Flex.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Alignment", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Flex/Flex.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Justification", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Flex/Flex.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Wrapping", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Flex/Flex.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Gaps", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Flex/Flex.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: FlexProperties", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Flex/Flex.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: SemanticElements", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Flex/Flex.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: InlineFlex", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Flex/Flex.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Interactive", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Flex/Flex.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Accessibility", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Flex/Flex.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: flexStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Flex/Flex.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Base/Flex/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Base/Link/Link.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: linkStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Link/Link.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Base/Link/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Base/Overlay/Overlay.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: overlayStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Overlay/Overlay.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Base/Overlay/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Base/Portal/Portal.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Default", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Portal/Portal.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Disabled", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Portal/Portal.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithCustomContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Portal/Portal.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Interactive", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Portal/Portal.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Accessibility", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Portal/Portal.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: portalWrapper", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Portal/Portal.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Base/Portal/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Base/Pre/Pre.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: preStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Pre/Pre.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Base/Pre/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Base/ScrollArea/ScrollArea.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: scrollAreaStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/ScrollArea/ScrollArea.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Base/ScrollArea/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Base/Transition/Transition.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Default", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Transition/Transition.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: SlideTransition", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Transition/Transition.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ScaleTransition", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Transition/Transition.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithCallbacks", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Transition/Transition.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Accessibility", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Base/Transition/Transition.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Base/Transition/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Base/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Collection.Code/Code.Block.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Collection.Code/Code.Highlight.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Collection.Code/Code.Preview.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Collection.Code/Code.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: InlineCode", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: BlockCode", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: DifferentSizes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: BasicCodeBlock", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CodeBlockWithLineNumbers", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CodeBlockWithFilename", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CodeBlockWithHighlighting", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: DifferentLanguages", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: SyntaxHighlighting", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: HighlightComparison", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: LivePreview", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: PreviewWithError", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: PreviewOnlyCode", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: PreviewOnlyResult", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: AdvancedSyntaxHighlighting", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Counter", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ComprehensiveExample", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CollectionIntegration", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CodeExample", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: inlineCode", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: codeBlock", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: codeHeader", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: codeContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: lineNumbers", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: lineNumber", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: codePre", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: copyButton", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: languageLabel", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: highlightedLine", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: editableCode", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: previewContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: previewHeader", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: previewContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: previewError", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: codeRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tokenStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Collection.Code/Code.types.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: LANGUAGE_ALIASES", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.types.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: DEFAULT_LANGUAGE", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.types.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: DEFAULT_THEME", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/Code.types.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: DEFAULT_SIZE", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Code/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Collection.Divider/Divider.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Sizes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Divider/Divider.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Variants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Divider/Divider.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Colors", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Divider/Divider.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CustomSpacing", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Divider/Divider.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CustomLength", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Divider/Divider.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: VerticalVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Divider/Divider.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ResponsiveBehavior", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Divider/Divider.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: AdvancedStyling", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Divider/Divider.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ComplexExample", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Divider/Divider.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dividerRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Divider/Divider.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Collection.Divider/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Collection.Indicator/Indicator.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: LoadingDefault", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: LoadingWithLabel", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: LoadingAnimations", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: LoadingTypes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: LoadingSizes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: LoadingColors", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ProgressDefault", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ProgressSizes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ProgressColors", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ProgressVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ProgressInteractive", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ProgressAnimated", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: PlaceholderDefault", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: PlaceholderSizes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: PlaceholderShapes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: PlaceholderCard", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: OverlayDefault", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: OverlayWithLoading", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CollectionShowcase", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: loadingContainerRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: loadingIndicatorRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: pointsStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: pointsOpacityStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: spinnerStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: spinnerSpanStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: loadingLabelRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: progressRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: progressBarRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: placeholderRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Indicator.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: overlayRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Collection.Indicator/Loading.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Collection.Indicator/Overlay.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Collection.Indicator/Placeholder.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Collection.Indicator/Progress.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Collection.Indicator/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Core.Document/Document.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: LightTheme", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Document/Document.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: DarkTheme", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Document/Document.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: RightToLeft", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Document/Document.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: MultiLanguage", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Document/Document.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithCustomViewport", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Document/Document.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: DocumentHeadExample", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Document/Document.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: DocumentMetaExample", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Document/Document.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: DocumentTitleExample", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Document/Document.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: DocumentBodyExample", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Document/Document.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: DocumentShowcase", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Document/Document.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: documentRoot", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Document/Document.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: documentBody", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Document/Document.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: documentFrame", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Document/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Core.Text/Text.Abbr.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Core.Text/Text.Heading.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Core.Text/Text.Kbd.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Core.Text/Text.Label.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Core.Text/Text.Link.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Core.Text/Text.Mark.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Core.Text/Text.Paragraph.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Core.Text/Text.Quote.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Core.Text/Text.Sub.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Core.Text/Text.Sup.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Core.Text/Text.Time.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Core.Text/Text.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Colored", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Text/Text.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Decorated", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Text/Text.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Transformed", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Text/Text.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Heading1", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Text/Text.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Heading2", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Text/Text.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Heading3", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Text/Text.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ExternalLink", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Text/Text.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: RequiredLabel", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Text/Text.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Abbreviation", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Text/Text.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ScientificNotation", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Text/Text.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: TypographyScale", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Text/Text.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: TextVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Text/Text.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: InteractiveElements", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Text/Text.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: SemanticElements", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Text/Text.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: AccessibilityFeatures", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Text/Text.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: textRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Text/Text.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: headingRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Text/Text.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: linkRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Text/Text.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: labelRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Text/Text.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbdRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Text/Text.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: paragraphRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Text/Text.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: quoteRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Text/Text.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: markRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Text/Text.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: abbrRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Text/Text.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: timeRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Text/Text.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: subSupRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.Text/Text.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Core.Text/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Core.Text/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Core.View/View.Column.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Core.View/View.Container.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Core.View/View.Device.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Core.View/View.Divider.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Core.View/View.Row.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Core.View/View.Scrollbar.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Core.View/View.Spacer.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Core.View/View.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: FullWidth", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: FullHeight", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithPadding", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithMargin", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: SemanticElements", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ContainerDefault", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ContainerSizes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ContainerCentered", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: RowDefault", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: RowAlignment", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: RowJustification", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ColumnDefault", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ColumnAlignment", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: DividerHorizontal", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: DividerVertical", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: DividerVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: SpacerVertical", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: SpacerHorizontal", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: SpacerGrow", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ScrollbarVertical", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ScrollbarHorizontal", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ScrollbarCustom", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: DeviceResponsive", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: LayoutShowcase", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: viewVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: containerBase", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: containerMaxWidths", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: scrollbarBase", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: scrollbarSizes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: scrollbarHidden", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: scrollbarNative", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dividerVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: spacerVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: deviceBase", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: deviceShow", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: deviceHide", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: deviceHoverOnly", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: deviceTouchOnly", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: rowVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: columnVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Core.View/View.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Core.View/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.Button/Button.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Colors", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Button/Button.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithIcons", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Button/Button.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ColorMatrix", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Button/Button.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: RippleEffects", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Button/Button.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: LoadingStates", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Button/Button.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: FormExample", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Button/Button.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: BaseUIFeatures", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Button/Button.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: buttonStateStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Button/Button.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: buttonRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Button/Button.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: buttonContentStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Button/Button.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.Button/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.Card/Card.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Sizes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Card/Card.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Colors", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Card/Card.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Variants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Card/Card.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: FullWidth", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Card/Card.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithHeaderAndFooter", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Card/Card.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithDividers", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Card/Card.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: NoPadding", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Card/Card.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: StructuredCard", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Card/Card.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: cardRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Card/Card.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: cardSectionStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Card/Card.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: cardContentWrapper", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Card/Card.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.Card/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.Icon/Icon.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Sizes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Icon/Icon.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CustomSize", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Icon/Icon.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Colors", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Icon/Icon.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CustomColor", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Icon/Icon.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Rotations", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Icon/Icon.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: IconExamples", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Icon/Icon.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: NamedIcons", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Icon/Icon.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: iconRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Icon/Icon.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: svgStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Icon/Icon.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: iconWrapperStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Icon/Icon.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: accessibilityStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Icon/Icon.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.Icon/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.List/List.Item.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.List/List.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithDividers", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.List/List.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.List/List.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Sizes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.List/List.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Colors", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.List/List.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Variants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.List/List.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: listRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.List/List.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: listItemRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.List/List.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: listItemContentStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.List/List.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: listSpacingStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.List/List.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.List/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.Media/Media.AspectRatio.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.Media/Media.Audio.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.Media/Media.Caption.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.Media/Media.Embed.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.Media/Media.Image.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.Media/Media.Svg.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.Media/Media.Track.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.Media/Media.Video.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.Media/Media.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CustomLoadingContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CustomErrorContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ImageDefault", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ImageLazy", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ImageWithFallback", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ImageClickable", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: VideoDefault", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: VideoWithPoster", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: VideoAutoplay", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: AudioDefault", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: AudioAutoplay", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: EmbedDefault", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: EmbedMap", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: SvgDefault", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: SvgClickable", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: AspectRatioDefault", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: AspectRatioSquare", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: AspectRatioWithImage", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CaptionDefault", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CaptionSizes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CaptionColors", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CaptionWithMedia", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: InteractiveGallery", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: VideoPlayerDemo", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: AudioPlayerDemo", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ResponsiveGrid", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: mediaRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: imageStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: objectFitVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: videoStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: audioStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: embedStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: svgStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: aspectRatioStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: captionStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: captionSizeVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: captionColorVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: captionAlignVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Media/Media.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.Media/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.Menu/Menu.Item.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.Menu/Menu.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithDescription", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Menu/Menu.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithHeaders", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Menu/Menu.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Sizes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Menu/Menu.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Colors", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Menu/Menu.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Variants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Menu/Menu.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ItemStates", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Menu/Menu.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ItemSizes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Menu/Menu.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Placements", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Menu/Menu.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: KeyboardNavigation", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Menu/Menu.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: menuRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Menu/Menu.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: menuItemRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Menu/Menu.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: menuItemContentStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Menu/Menu.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: menuTriggerStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Menu/Menu.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: menuBackdropStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Menu/Menu.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.Menu/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.Message/Message.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Default", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Message/Message.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithTitle", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Message/Message.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithTitleAndDescription", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Message/Message.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: SeverityVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Message/Message.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: StyleVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Message/Message.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: SizeVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Message/Message.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: DismissibleVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Message/Message.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: AutoHide", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Message/Message.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: AutoHideWithPause", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Message/Message.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CustomIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Message/Message.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithActions", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Message/Message.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithoutIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Message/Message.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: FullWidth", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Message/Message.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ComplexMessage", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Message/Message.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: RadiusVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Message/Message.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: AllCombinations", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Message/Message.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: messageRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Message/Message.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: messageContentStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Message/Message.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: iconSizeVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Message/Message.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.Message/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.Row/Column.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.Row/Row.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Default", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Row/Row.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithAlignment", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Row/Row.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithWrapping", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Row/Row.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: SpaceBetween", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Row/Row.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Reversed", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Row/Row.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ColumnDefault", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Row/Row.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ColumnCentered", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Row/Row.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ColumnSpaceBetween", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Row/Row.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CompoundLayout", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Row/Row.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: rowVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Row/Row.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: columnVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Row/Row.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: gapStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Row/Row.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.Row/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Element.Table/Table.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Default", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Table/Table.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithSorting", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Table/Table.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithFiltering", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Table/Table.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithSelection", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Table/Table.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithFooter", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Table/Table.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: RealTimeUpdates", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Table/Table.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithPagination", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Table/Table.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: EditableTable", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Table/Table.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tableContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Table/Table.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tableHeaderGroup", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Table/Table.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tableBodyGroup", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Table/Table.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stripedRow", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Table/Table.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tableFooterGroup", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Table/Table.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tableRow", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Table/Table.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tableHeaderCell", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Table/Table.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tableCell", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Table/Table.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: sortIndicator", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Table/Table.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: sortIndicatorActive", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Table/Table.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: sortIndicatorAscending", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Table/Table.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: sortIndicatorDescending", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Table/Table.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: filterInput", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Table/Table.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: loadingOverlay", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Table/Table.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: emptyState", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Table/Table.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectionCheckbox", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Table/Table.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: responsiveTable", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Table/Table.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: colorVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Element.Table/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.DataTable/DataTable.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Default", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: BasicConfiguration", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: AdvancedConfiguration", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithSelection", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: VirtualScrolling", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CustomRendering", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: LoadingState", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: EmptyState", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithToolbar", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: RealWorldExample", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ErrorHandling", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableSearch", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableSearchInput", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableActions", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableActionButton", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableTable", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableVirtualContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableVirtualRow", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTablePaginationInfo", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTablePaginationButton", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTablePaginationSelect", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableEmpty", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableEmptyIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableEmptyText", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableLoading", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableLoadingSpinner", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableError", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableErrorText", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableFilter", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableFilterInput", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableFilterButton", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableSortIndicator", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableSelection", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableSelectionCheckbox", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableResizeHandle", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableExportMenu", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableExportMenuItem", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableColumnToggle", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableColumnToggleItem", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableColumnToggleCheckbox", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dataTableResponsive", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.DataTable/DataTable.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.DataTable/DataTable.types.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.DataTable/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.DataTable/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Editor/Editor.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithLabel", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: BottomToolbar", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ReadOnly", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithError", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithMarkdown", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithHeightConstraints", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: AutoFocus", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: editorContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: editorToolbar", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: toolbarGroup", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: toolbarButton", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: toolbarDropdown", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: toolbarDropdownMenu", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: toolbarDropdownItem", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: toolbarSeparator", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: editorContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: editorPlaceholder", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: editor<PERSON><PERSON>er", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: editorCount", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: editorError", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: editorHelperText", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: richTextContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: linkDialog", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: imageUploadArea", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: collaborativeCursor", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: collaborativeSelection", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Editor/Editor.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Editor/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Editor/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Form/Form.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithSelectFields", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Form/Form.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithGroups", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Form/Form.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: GridLayout", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Form/Form.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Sizes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Form/Form.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Variants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Form/Form.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: AutoSave", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Form/Form.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ConditionalFields", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Form/Form.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ReadOnly", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Form/Form.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: formContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Form/Form.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: formGroup", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Form/Form.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: formGroupHeader", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Form/Form.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: formGroupTitle", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Form/Form.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: formGroupDescription", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Form/Form.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: formField", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Form/Form.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: formFieldLabel", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Form/Form.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: formFieldDescription", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Form/Form.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: formFieldError", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Form/Form.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: formFieldSelect", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Form/Form.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: formFieldTextarea", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Form/Form.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: formFieldCheckbox", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Form/Form.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: formActions", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Form/Form.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: formLoadingOverlay", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Form/Form.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: gridLayoutVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Form/Form.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: responsiveGridVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Form/Form.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Form/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Form/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Kbar/Kbar.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Default", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithProvider", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: NestedActions", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CustomStyling", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: NoResults", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: DisabledActions", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: LargeDataset", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CustomEmptyState", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarOverlay", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarModal", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarSearchContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarSearchInput", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarSearchIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarResults", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarSection", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarSectionHeader", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarActionItem", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarActionIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarActionContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarActionName", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarActionDescription", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarActionShortcut", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarShortcutKey", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarEmpty", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarEmptyIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarEmptyText", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarEmptyDescription", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarLoading", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarLoadingSpinner", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarBreadcrumb", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarBreadcrumbItem", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarBreadcrumbSeparator", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarFooter", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarFooterShortcuts", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarFooterShortcut", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarHighlight", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarResponsive", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: kbarDarkMode", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Kbar/Kbar.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Kbar/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Kbar/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Layout/Layout.Content.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Layout/Layout.Footer.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Layout/Layout.Header.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Layout/Layout.Main.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Layout/Layout.Sidebar.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Layout/Layout.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: BasicConfiguration", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithSidebar", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: HorizontalLayout", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CollapsibleSidebar", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ResizableSidebar", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: DualSidebars", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ResponsiveLayout", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: layoutContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: layoutHeader", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: layoutMain", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: layoutSidebar", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: layoutSidebarResizeHandle", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: layoutContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: layoutFooter", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: layoutContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: layoutMain", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: layoutHeader", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: layoutFooter", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: layoutSidebar", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: layoutContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: layoutPanel", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: resizeHandle", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: collapseButton", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: panelHeader", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: panelContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: layoutGrid", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: gridAreaVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: responsiveLayoutVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: layoutAnimations", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Layout/Layout.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Layout/LayoutComponents.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Layout/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Layout/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Modal/Modal.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Default", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Modal/Modal.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Sizes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Modal/Modal.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Animations", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Modal/Modal.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ScrollableContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Modal/Modal.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: FormModal", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Modal/Modal.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithTrigger", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Modal/Modal.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: StackedModals", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Modal/Modal.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CustomBackdrop", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Modal/Modal.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: modalOverlay", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Modal/Modal.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: modalContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Modal/Modal.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: modalHeader", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Modal/Modal.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: modalTitle", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Modal/Modal.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: modalSubtitle", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Modal/Modal.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: modalCloseButton", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Modal/Modal.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: modalBody", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Modal/Modal.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: modalFooter", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Modal/Modal.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: modalBackdrop", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Modal/Modal.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: modalAnimations", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Modal/Modal.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: modalContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Modal/Modal.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: modalScrollContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Modal/Modal.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Modal/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Modal/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Player/Player.stories.tsx", "type": "namespace-import", "severity": "warning", "message": "Namespace imports prevent tree-shaking", "suggestion": "Use named imports: import { specific } from \"module\"", "impact": "high"}, {"file": "components/Factory.Player/Player.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CustomAspectRatio", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Player/Player.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: AutoplayMuted", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Player/Player.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Looped", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Player/Player.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: NoControls", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Player/Player.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithSubtitles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Player/Player.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithCustomOverlay", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Player/Player.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithEventHandlers", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Player/Player.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: playerContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Player/Player.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: playerVideo", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Player/Player.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: playerOverlay", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Player/Player.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: playerControls", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Player/Player.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: progressBar", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Player/Player.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: progressBuffer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Player/Player.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: progressThumb", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Player/Player.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: controlsRow", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Player/Player.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: controlButton", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Player/Player.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: playButtonLarge", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Player/Player.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: timeDisplay", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Player/Player.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: volumeContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Player/Player.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: volumeSlider", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Player/Player.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: loadingSpinner", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Player/Player.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: errorMessage", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Player/Player.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: posterImage", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Player/Player.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: fullscreenStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Player/Player.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: settingsMenu", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Player/Player.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: settingsMenuItem", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Player/Player.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Player/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Search/Search.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Sizes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CustomConfig", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithoutSuggestions", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CustomEmptyState", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithLoading", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithError", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ReadOnly", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithoutSearchIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithoutClearButton", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: AutoFocus", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchInputContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchInput", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchActions", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchActionButton", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchDropdown", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchFilters", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchFilter", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchSort", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchResultsHeader", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchResults", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchResult", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchResultIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchResultAvatar", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchResultContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchResultTitle", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchResultDescription", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchResultCategory", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchSuggestions", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchSuggestionsHeader", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchSuggestion", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchSuggestionIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchSuggestionText", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchEmpty", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchEmptyIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchEmptyText", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchEmptyDescription", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchLoading", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchLoadingSpinner", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchLoadingText", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchError", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchErrorIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchErrorText", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchHighlight", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: recentSearches", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: recentSearchesHeader", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: clearRecentButton", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: keyboardHint", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Search/Search.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Search/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Search/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Select/Select.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Default", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithLabel", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ErrorState", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Sizes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: VirtualScrolling", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CustomOptions", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: MaxTagCount", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Placements", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: PerformanceTest", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectLabel", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectLabelRequired", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectTrigger", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectPlaceholder", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectValue", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectTags", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectTag", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectTagText", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectTagClose", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectActions", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectActionButton", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectArrow", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectDropdown", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectSearchContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectSearchInput", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectOptions", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectOptionGroup", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectOptionGroupHeader", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectOptionIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectOptionAvatar", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectOptionContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectOptionLabel", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectOptionDescription", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectOptionCheckbox", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectAllOption", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectEmpty", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectEmptyIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectEmptyText", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectLoading", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectLoadingSpinner", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectHelperText", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectErrorMessage", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectVirtualContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectVirtualItem", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectResponsive", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectDarkMode", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Select/Select.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Select/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Select/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Stepper/Stepper.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithIcons", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: NonLinear", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithValidation", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Sizes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Variants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Colors", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CustomRendering", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stepperContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: orientationVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stepperHeader", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stepperHeaderVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: progressBar", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: progressBarVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stepItem", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stepItemVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stepIndicator", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stepContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stepContentVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stepTitle", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stepDescription", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stepConnector", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stepConnectorVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stepConnectorCompleted", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stepperContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stepPanel", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stepperNavigation", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: navigationButton", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: loadingSpinner", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: optionalIndicator", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: errorMessage", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: completedIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: animations", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Stepper/Stepper.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Stepper/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Stepper/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Tabber/Tabber.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithTemplates", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithPersistence", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Variants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Sizes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ManyTabs", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: TabStates", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: TabsOnly", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CustomRendering", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tabberContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tabberOrientationVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tabList", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tabScrollContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tabScrollContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tabIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tabLabel", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tabBadge", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tabIndicators", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tabDirtyIndicator", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tabLoadingIndicator", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tabErrorIndicator", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tabCloseButton", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: addTabButton", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tabPanels", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tabPanel", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: emptyState", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: emptyStateIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: emptyStateText", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: emptyStateDescription", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: scrollButtonLeft", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: scrollButtonRight", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dropZone", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Tabber/Tabber.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Tabber/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Tabber/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Toast/Toast.Container.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Toast/Toast.Provider.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Toast/Toast.context.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Toast/Toast.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithAction", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Toast/Toast.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: NotDismissible", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Toast/Toast.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CustomIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Toast/Toast.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Sizes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Toast/Toast.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: AllTypes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Toast/Toast.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ToastSystem", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Toast/Toast.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithComplexAction", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Toast/Toast.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: LongContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Toast/Toast.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: AutoDismissDemo", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Toast/Toast.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: toastContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Toast/Toast.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: toastIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Toast/Toast.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: toastContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Toast/Toast.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: toastTitle", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Toast/Toast.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: toastDescription", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Toast/Toast.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: toastActions", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Toast/Toast.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: toastActionButton", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Toast/Toast.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: toastCloseButton", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Toast/Toast.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: toastProgressBar", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Factory.Toast/Toast.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Toast/importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Factory.Toast/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Module.Collapser/Collapser.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: collapserContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Collapser/Collapser.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: collapserTrigger", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Collapser/Collapser.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: triggerContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Collapser/Collapser.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: triggerText", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Collapser/Collapser.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: triggerIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Collapser/Collapser.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: collapserContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Collapser/Collapser.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: contentInner", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Collapser/Collapser.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: contentInnerSizeVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Collapser/Collapser.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: collapserGroup", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Collapser/Collapser.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: groupSpacingVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Collapser/Collapser.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: groupDivider", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Collapser/Collapser.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: collapserAnimations", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Collapser/Collapser.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Module.Collapser/CollapserGroup.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Module.Collapser/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Module.ContextMenu/ContextMenu.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: contextMenuContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.ContextMenu/ContextMenu.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: contextMenuSizeVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.ContextMenu/ContextMenu.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: menuItemList", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.ContextMenu/ContextMenu.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: menuItem", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.ContextMenu/ContextMenu.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: menuItemIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.ContextMenu/ContextMenu.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: menuItemContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.ContextMenu/ContextMenu.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: menuItemShortcut", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.ContextMenu/ContextMenu.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: submenuIndicator", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.ContextMenu/ContextMenu.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: checkIndicator", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.ContextMenu/ContextMenu.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: separator", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.ContextMenu/ContextMenu.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: groupHeader", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.ContextMenu/ContextMenu.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: iconSpace", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.ContextMenu/ContextMenu.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: enterAnimation", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.ContextMenu/ContextMenu.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: exitAnimation", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.ContextMenu/ContextMenu.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: contextMenuAnimations", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.ContextMenu/ContextMenu.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: submenuContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.ContextMenu/ContextMenu.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: backdrop", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.ContextMenu/ContextMenu.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Module.ContextMenu/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Module.Drawer/Drawer.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: drawerContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Drawer/Drawer.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: drawerPlacementVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Drawer/Drawer.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: drawerSizeVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Drawer/Drawer.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: drawerBackdrop", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Drawer/Drawer.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: drawerHeader", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Drawer/Drawer.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: headerContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Drawer/Drawer.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: headerTitle", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Drawer/Drawer.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: headerSubtitle", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Drawer/Drawer.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: closeButton", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Drawer/Drawer.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: drawerBody", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Drawer/Drawer.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: drawerFooter", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Drawer/Drawer.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: slideAnimations", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Drawer/Drawer.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: backdropAnimation", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Drawer/Drawer.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Module.Drawer/DrawerComponents.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Module.Drawer/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Module.Dropdown/Dropdown.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithIcons", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithDescriptions", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Grouped", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Multiple", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Searchable", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: SearchableMultiple", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Sizes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Variants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Colors", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: States", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Clearable", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CustomRendering", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: LargeDataset", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Placements", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: KeyboardNavigation", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Accessibility", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dropdownContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dropdownTrigger", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dropdownContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: searchInput", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: optionList", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: optionIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: optionContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: optionLabel", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: optionDescription", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dropdownIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: dropdownIconOpen", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: clearButton", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: emptyState", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: loadingState", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: groupHeader", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: selectedValue", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Dropdown/Dropdown.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Module.Dropdown/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Module.Pagination/Pagination.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithFirstLastButtons", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Pagination/Pagination.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithPageInfo", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Pagination/Pagination.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ManyPages", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Pagination/Pagination.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CustomPageInfo", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Pagination/Pagination.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: paginationContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Pagination/Pagination.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: paginationSizeVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Pagination/Pagination.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: paginationItem", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Pagination/Pagination.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: navigationIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Pagination/Pagination.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: pageInfo", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Pagination/Pagination.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: paginationContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Pagination/Pagination.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: paginationSizeVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Pagination/Pagination.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: paginationItem", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Pagination/Pagination.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: navigationIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Pagination/Pagination.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: pageInfo", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Pagination/Pagination.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Module.Pagination/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Module.Popover/Popover.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Placements", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Popover/Popover.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Sizes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Popover/Popover.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Colors", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Popover/Popover.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Variants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Popover/Popover.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithArrow", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Popover/Popover.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithForm", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Popover/Popover.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ContextMenu", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Popover/Popover.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: TooltipBasic", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Popover/Popover.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: TooltipPlacements", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Popover/Popover.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: PreviewCard", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Popover/Popover.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: KeyboardNavigation", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Popover/Popover.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Accessibility", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Popover/Popover.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: popoverRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Popover/Popover.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: arrowStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Popover/Popover.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: popoverTriggerStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Popover/Popover.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: popoverBackdropStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Popover/Popover.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tooltipStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Popover/Popover.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tooltipArrowStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Popover/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Module.Slider/Slider.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: sliderContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Slider/Slider.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: sliderRail", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Slider/Slider.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: sliderTrack", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Slider/Slider.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: sliderHandle", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Slider/Slider.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: sliderMark", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Slider/Slider.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: sliderMarkLabel", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Slider/Slider.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: sliderTooltip", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Slider/Slider.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: sliderValueLabel", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Slider/Slider.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: sliderError", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Slider/Slider.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: sliderHelperText", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Slider/Slider.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Module.Slider/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Module.Sticky/Sticky.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stickyContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Sticky/Sticky.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stickyContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Sticky/Sticky.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stickyPlaceholder", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Sticky/Sticky.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stickyContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Sticky/Sticky.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stickyElement", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Sticky/Sticky.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stickyPlaceholder", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Sticky/Sticky.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stickyStateVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Sticky/Sticky.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stickyPositionVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Sticky/Sticky.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stickyDebug", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Sticky/Sticky.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: responsiveVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Sticky/Sticky.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stickyAnimations", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Sticky/Sticky.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stickyScrollContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Sticky/Sticky.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: stickyOptimized", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Sticky/Sticky.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Module.Sticky/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Module.Tabs/Tabs.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithIcons", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tabs/Tabs.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Sizes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tabs/Tabs.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Variants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tabs/Tabs.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Colors", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tabs/Tabs.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Vertical", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tabs/Tabs.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Centered", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tabs/Tabs.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: FullWidth", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tabs/Tabs.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: ClosableTabs", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tabs/Tabs.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithAddButton", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tabs/Tabs.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Controlled", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tabs/Tabs.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithoutPanels", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tabs/Tabs.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: KeyboardNavigation", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tabs/Tabs.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Accessibility", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tabs/Tabs.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tabsContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tabs/Tabs.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tabsContainerVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tabs/Tabs.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tabList", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tabs/Tabs.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tabIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tabs/Tabs.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tabLabel", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tabs/Tabs.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tabBadge", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tabs/Tabs.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tabCloseButton", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tabs/Tabs.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tabPanels", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tabs/Tabs.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tabPanel", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tabs/Tabs.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: scrollButton", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tabs/Tabs.styles.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: addTabButton", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tabs/Tabs.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Module.Tabs/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Module.Tooltip/Tooltip.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Placements", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tooltip/Tooltip.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Sizes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tooltip/Tooltip.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Colors", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tooltip/Tooltip.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Variants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tooltip/Tooltip.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: RichContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tooltip/Tooltip.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithProvider", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tooltip/Tooltip.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CustomDelays", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tooltip/Tooltip.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CompoundComponent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tooltip/Tooltip.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: tooltipRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tooltip/Tooltip.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: arrowBase", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tooltip/Tooltip.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: arrowVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tooltip/Tooltip.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: animationStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tooltip/Tooltip.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Module.Tooltip/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Module.Tree/Tree.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Default", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tree/Tree.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithSelection", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tree/Tree.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: MultiSelect", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tree/Tree.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Small", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tree/Tree.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: Large", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tree/Tree.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: WithLines", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tree/Tree.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: NoExpandIcons", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tree/Tree.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: LimitedDepth", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tree/Tree.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: NoAnimation", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tree/Tree.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CustomExpandIcons", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tree/Tree.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: FileSystem", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tree/Tree.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: CustomRenderer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tree/Tree.stories.tsx", "type": "unused-export", "severity": "info", "message": "Potentially unused export: KeyboardNavigation", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tree/Tree.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: treeContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tree/Tree.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: treeSizeVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tree/Tree.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: treeItem", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tree/Tree.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: treeItemContent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tree/Tree.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: expandButton", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tree/Tree.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: expandIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tree/Tree.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: itemIcon", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tree/Tree.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: itemLabel", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tree/Tree.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: childrenContainer", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tree/Tree.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: indentation", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tree/Tree.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: connectingLine", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tree/Tree.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: lastItemLine", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tree/Tree.styles.css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: horizontalLine", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "components/Module.Tree/Tree.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Module.Tree/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/Module.importer.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "components/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "constants/darkTheme.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: darkThemeCSSVars", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "constants/defaults/themeUtils.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createResponsiveStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "constants/defaults/themeUtils.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: getCSSVar", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "constants/defaults/themeUtils.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createCSSVars", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "constants/defaults/themeUtils.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: mergeThemes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "constants/defaults/themeUtils.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: getSystemThemeMode", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "constants/defaults/themeUtils.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createMediaQuery", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "constants/defaults/themeUtils.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: remToPx", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "constants/defaults/themeUtils.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: withOpacity", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "constants/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "constants/keyCodes.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: isKey", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "constants/keyCodes.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: isKeyCode", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "constants/keyCodes.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: isArrowKey", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "constants/keyCodes.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: isNavigationKey", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "constants/keyCodes.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: isActionKey", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "constants/keyCodes.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: isModifierKey", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "constants/keyCodes.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: isLetterKey", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "constants/keyCodes.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: isDigitKey", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "constants/keyCodes.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: isFunctionKey", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "constants/keyCodes.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: hasModifier", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "constants/keyCodes.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: getModifiers", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "constants/keyCodes.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createKeyboardShortcut", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "constants/keyCodes.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: matchesKeyboardShortcut", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "constants/lightTheme.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: lightThemeCSSVars", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "core.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "elements.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "factories-dynamic.ts", "type": "namespace-import", "severity": "warning", "message": "Namespace imports prevent tree-shaking", "suggestion": "Use named imports: import { specific } from \"module\"", "impact": "high"}, {"file": "factories-dynamic.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: FactoryLoaders", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "factories-dynamic.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createLazyFactory", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "factories-dynamic.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: FactorySuspense", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "factories-dynamic.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: useFactoryComponent", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "factories-heavy.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "factories-light.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "factories.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "hooks/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "hooks/useDOMRef.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "hooks/useDrip.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "modules.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "test/accessibility-utils.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "test/custom-matchers.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "test/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "test/test-utils.tsx", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "test/visual-regression-utils.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "test-build.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: unusedFunction", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "test-build.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: unusedStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/componentHelpers.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "utils/composeRefs.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "utils/css.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "utils/css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: responsiveStyle", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createComponentVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createSizeVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createColorVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createRadiusVariants", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createFocusStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createHoverStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createActiveStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createDisabledStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createLoadingStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createTransitionStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createShadowStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createTruncateStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createLineClampStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createVisuallyHiddenStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createFlexStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createGridStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createAbsoluteStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createFixedStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createStickyStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createZIndexStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createAnimationStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createKeyframes", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/css.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createComponentRecipe", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/cssOptimization.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: mergeClasses", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/cssOptimization.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: conditionalClass", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/cssOptimization.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: optimizeMediaQueries", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/cssOptimization.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: optimizeThemeStyles", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/cssOptimization.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: optimizeCSSVariables", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/cssOptimization.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: optimizeAnimations", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/index.ts", "type": "barrel-export", "severity": "warning", "message": "Barrel exports may prevent tree-shaking", "suggestion": "Use direct imports instead of re-exports", "impact": "medium"}, {"file": "utils/themeProvider.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: applyTheme", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}, {"file": "utils/themeProvider.ts", "type": "unused-export", "severity": "info", "message": "Potentially unused export: createInlineThemeVars", "suggestion": "Remove unused exports to improve tree-shaking", "impact": "low"}], "effectiveness": 0}, "codeSplittingAnalysis": {"chunkCategories": {"main": [], "factoriesLight": [], "factories": [], "factoriesHeavy": [], "factoriesDynamic": [], "components": [], "utilities": [], "chunks": []}, "totalSize": 0, "mainSize": 0, "factorySize": 0, "splittingRatio": 0, "mainBundleRatio": 0, "recommendations": ["Low code splitting ratio - move more components to factory bundles", "No dynamic chunks detected - implement lazy loading for better performance"]}, "recommendations": [{"priority": "medium", "category": "Tree-shaking", "title": "Tree-shaking Optimization Opportunity", "description": "Current effectiveness: 0.0% (target: 85%)", "impact": "Medium - Reduces bundle size through dead code elimination", "actions": ["Replace barrel exports with direct imports", "Use named imports instead of namespace imports", "Mark side-effect modules appropriately", "Optimize import/export patterns"]}, {"priority": "medium", "category": "Tree-shaking", "title": "Tree-shaking Optimization Opportunity", "description": "Current effectiveness: 0.0% (target: 85%)", "impact": "Medium - Reduces bundle size through dead code elimination", "actions": ["Replace barrel exports with direct imports", "Use named imports instead of namespace imports", "Implement more dynamic imports for code splitting", "Remove unused exports and dependencies"]}, {"priority": "medium", "category": "Code Splitting", "title": "Code Splitting Optimization", "description": "Current splitting ratio: 0.0% (target: >40%)", "impact": "Medium - Improves initial load performance", "actions": ["Move more components to factory bundles", "Implement lazy loading for heavy components", "Use dynamic imports for conditional features", "Optimize chunk splitting configuration"]}], "performance": {"compressionRatio": null, "brotliImprovement": null, "criticalPathOptimal": true}, "buildInfo": {"nodeVersion": "v22.17.0", "platform": "darwin", "arch": "arm64", "environment": "development"}}