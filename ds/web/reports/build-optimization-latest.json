{"timestamp": "2025-07-22T15:01:24.252Z", "buildStatus": "failed", "optimizations": {"viteTreeShaking": "configured", "cssOptimization": "configured", "codeSplitting": "configured", "bundleAnalysis": "configured"}, "configurations": {"mainConfig": "vite.config.ts", "analyzeConfig": "vite.config.analyze.ts", "chunksConfig": "vite.config.chunks.ts", "cssOptimizeConfig": "vite.css-optimize.config.ts"}, "scripts": {"bundleMonitor": "scripts/bundle-monitor.cjs", "bundleOptimize": "scripts/bundle-optimize-enhanced.cjs", "bundleSizeConfig": "scripts/bundle-size-config.cjs", "cssOptimizer": "scripts/css-optimizer.cjs"}, "recommendations": ["Use pnpm build:analyze for detailed bundle analysis", "Use pnpm build:chunks for optimal code splitting", "Use pnpm build:css-optimize for CSS optimization", "Monitor bundle sizes with pnpm bundle:monitor", "Run comprehensive analysis with pnpm bundle:optimize-enhanced"]}