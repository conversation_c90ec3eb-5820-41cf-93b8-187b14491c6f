{"timestamp": "2025-07-22T18:55:27.605Z", "version": "0.1.0", "summary": {"totalBundles": 12, "violations": 0, "warnings": 0, "performanceIssues": 0, "averageScore": 100, "totalSize": {"rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "criticalPathSize": 0}, "analysis": {"compressionRatio": null, "brotliImprovement": null, "averagePerformanceScore": 100}}, "bundles": [{"filename": "index.es.js", "rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "loadTimes": {"3G": 0, "4G": 0, "WiFi": 0, "Cable": 0}, "budget": {"max": 120, "warning": 100, "critical": true, "description": "Main library bundle - critical for initial load", "loadTime": 2000, "priority": "high"}, "performanceScore": 100, "recommendations": []}, {"filename": "factories-light.es.js", "rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "loadTimes": {"3G": 0, "4G": 0, "WiFi": 0, "Cable": 0}, "budget": {"max": 60, "warning": 45, "critical": false, "description": "Light factory components", "loadTime": 1000, "priority": "medium"}, "performanceScore": 100, "recommendations": []}, {"filename": "factories.es.js", "rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "loadTimes": {"3G": 0, "4G": 0, "WiFi": 0, "Cable": 0}, "budget": {"max": 80, "warning": 60, "critical": false, "description": "Standard factory components", "loadTime": 1500, "priority": "medium"}, "performanceScore": 100, "recommendations": []}, {"filename": "factories-heavy.es.js", "rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "loadTimes": {"3G": 0, "4G": 0, "WiFi": 0, "Cable": 0}, "budget": {"max": 250, "warning": 200, "critical": false, "description": "Heavy factory components (lazy loaded)", "loadTime": 5000, "priority": "low"}, "performanceScore": 100, "recommendations": []}, {"filename": "core/index.es.js", "rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "loadTimes": {"3G": 0, "4G": 0, "WiFi": 0, "Cable": 0}, "budget": {"max": 40, "warning": 30, "critical": true, "description": "Core components - essential functionality", "loadTime": 800, "priority": "high"}, "performanceScore": 100, "recommendations": []}, {"filename": "elements/index.es.js", "rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "loadTimes": {"3G": 0, "4G": 0, "WiFi": 0, "Cable": 0}, "budget": {"max": 60, "warning": 45, "critical": true, "description": "Element components - commonly used", "loadTime": 1200, "priority": "high"}, "performanceScore": 100, "recommendations": []}, {"filename": "collections/index.es.js", "rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "loadTimes": {"3G": 0, "4G": 0, "WiFi": 0, "Cable": 0}, "budget": {"max": 35, "warning": 25, "critical": false, "description": "Collection components", "loadTime": 700, "priority": "medium"}, "performanceScore": 100, "recommendations": []}, {"filename": "modules/index.es.js", "rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "loadTimes": {"3G": 0, "4G": 0, "WiFi": 0, "Cable": 0}, "budget": {"max": 50, "warning": 40, "critical": false, "description": "Module components", "loadTime": 1000, "priority": "medium"}, "performanceScore": 100, "recommendations": []}, {"filename": "base/index.es.js", "rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "loadTimes": {"3G": 0, "4G": 0, "WiFi": 0, "Cable": 0}, "budget": {"max": 25, "warning": 20, "critical": false, "description": "Base utilities and primitives", "loadTime": 500, "priority": "low"}, "performanceScore": 100, "recommendations": []}, {"filename": "constants.es.js", "rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "loadTimes": {"3G": 0, "4G": 0, "WiFi": 0, "Cable": 0}, "budget": {"max": 15, "warning": 10, "critical": true, "description": "Theme constants and tokens", "loadTime": 300, "priority": "high"}, "performanceScore": 100, "recommendations": []}, {"filename": "hooks.es.js", "rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "loadTimes": {"3G": 0, "4G": 0, "WiFi": 0, "Cable": 0}, "budget": {"max": 10, "warning": 8, "critical": true, "description": "React hooks utilities", "loadTime": 200, "priority": "high"}, "performanceScore": 100, "recommendations": []}, {"filename": "utils.es.js", "rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "loadTimes": {"3G": 0, "4G": 0, "WiFi": 0, "Cable": 0}, "budget": {"max": 20, "warning": 15, "critical": true, "description": "Utility functions", "loadTime": 400, "priority": "high"}, "performanceScore": 100, "recommendations": []}], "violations": [], "warnings": [], "performanceIssues": [], "recommendations": [], "buildInfo": {"nodeVersion": "v22.17.0", "platform": "darwin", "arch": "arm64", "timestamp": "2025-07-22T18:55:27.609Z", "environment": "development"}, "networkAnalysis": {"loadTimes": {"3G": 0, "4G": 0, "WiFi": 0, "Cable": 0}, "criticalPathLoadTimes": {"3G": 0, "4G": 0, "WiFi": 0, "Cable": 0}}}