{"timestamp": "2025-07-22T14:59:53.992Z", "version": "0.1.0", "summary": {"totalBundles": 12, "violations": 0, "warnings": 0, "totalSize": {"rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "criticalPathSize": 0}, "analysis": {"compressionRatio": null, "brotliImprovement": null, "splittingRatio": null}}, "bundles": [{"filename": "index.es.js", "rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "limits": {"max": 120, "warning": 100, "critical": true}, "status": "ok"}, {"filename": "factories-light.es.js", "rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "limits": {"max": 60, "warning": 45, "critical": false}, "status": "ok"}, {"filename": "factories.es.js", "rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "limits": {"max": 80, "warning": 60, "critical": false}, "status": "ok"}, {"filename": "factories-heavy.es.js", "rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "limits": {"max": 250, "warning": 200, "critical": false}, "status": "ok"}, {"filename": "core/index.es.js", "rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "limits": {"max": 40, "warning": 30, "critical": true}, "status": "ok"}, {"filename": "elements/index.es.js", "rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "limits": {"max": 60, "warning": 45, "critical": true}, "status": "ok"}, {"filename": "collections/index.es.js", "rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "limits": {"max": 35, "warning": 25, "critical": false}, "status": "ok"}, {"filename": "modules/index.es.js", "rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "limits": {"max": 50, "warning": 40, "critical": false}, "status": "ok"}, {"filename": "base/index.es.js", "rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "limits": {"max": 25, "warning": 20, "critical": false}, "status": "ok"}, {"filename": "constants.es.js", "rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "limits": {"max": 15, "warning": 10, "critical": true}, "status": "ok"}, {"filename": "hooks.es.js", "rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "limits": {"max": 10, "warning": 8, "critical": true}, "status": "ok"}, {"filename": "utils.es.js", "rawSize": 0, "gzippedSize": 0, "brotliSize": 0, "limits": {"max": 20, "warning": 15, "critical": true}, "status": "ok"}], "violations": [], "warnings": [], "recommendations": [], "buildInfo": {"nodeVersion": "v22.17.0", "platform": "darwin", "arch": "arm64", "timestamp": "2025-07-22T14:59:53.993Z", "environment": "development"}}