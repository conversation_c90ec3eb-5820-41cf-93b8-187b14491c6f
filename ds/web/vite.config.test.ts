/**
 * Test Vite Configuration for Optimization Testing
 * Minimal configuration to test optimization features
 */

import type { UserConfig } from 'vite';
import { defineConfig } from 'vite';
import { resolve } from 'path';
import { vanillaExtractPlugin } from '@vanilla-extract/vite-plugin';

export default defineConfig({
	plugins: [
		vanillaExtractPlugin({
			identifiers: process.env.NODE_ENV === 'production' ? 'short' : 'debug',
			esbuildOptions: {
				external: ['@vanilla-extract/css', '@vanilla-extract/recipes'],
				minify: process.env.NODE_ENV === 'production',
				treeShaking: true,
				target: 'es2020',
			},
		}),
	],
	build: {
		lib: {
			entry: {
				// Test entries
				'test-build': resolve(__dirname, 'src/test-build.ts'),
				'factories-light': resolve(__dirname, 'src/factories-light.ts'),
				'factories': resolve(__dirname, 'src/factories.ts'),
				'factories-heavy': resolve(__dirname, 'src/factories-heavy.ts'),
			},
			name: 'MissUIWebTest',
			formats: ['es'],
			fileName: (format, entryName) => `${entryName}.${format}.js`,
		},
		rollupOptions: {
			external: [
				'react',
				'react-dom',
				'react/jsx-runtime',
				'@base-ui-components/react',
				'@lexical/code',
				'@lexical/history',
				'@lexical/html',
				'@lexical/link',
				'@lexical/list',
				'@lexical/markdown',
				'@lexical/plain-text',
				'@lexical/react',
				'@lexical/rich-text',
				'@lexical/selection',
				'@lexical/table',
				'@lexical/utils',
				'@vidstack/react',
				'lexical',
			],
			output: {
				exports: 'named',
				// Enhanced chunk splitting for optimal tree-shaking
				manualChunks: (id) => {
					if (id.includes('Factory.Editor') || id.includes('@lexical')) {
						return 'factory-editor';
					}
					if (id.includes('Factory.Player') || id.includes('@vidstack')) {
						return 'factory-player';
					}
					if (id.includes('Factory.')) {
						return 'factories';
					}
					if (id.includes('constants/')) {
						return 'constants';
					}
					if (id.includes('hooks/')) {
						return 'hooks';
					}
					if (id.includes('utils/')) {
						return 'utils';
					}
				},
				compact: true,
				generatedCode: {
					constBindings: true,
					objectShorthand: true,
					arrowFunctions: true,
				},
				format: 'es',
				assetFileNames: 'assets/[name]-[hash][extname]',
				chunkFileNames: 'chunks/[name]-[hash].js',
				entryFileNames: '[name].js',
			},
			treeshake: {
				moduleSideEffects: (id) => {
					if (id.includes('.css') || id.includes('.css.ts')) {
						return true;
					}
					if (id.includes('theme') || id.includes('globalStyles')) {
						return true;
					}
					return false;
				},
				propertyReadSideEffects: false,
				unknownGlobalSideEffects: false,
				preset: 'smallest',
				annotations: true,
			},
		},
		sourcemap: false,
		target: 'es2020',
		minify: 'esbuild',
		cssCodeSplit: true,
		reportCompressedSize: true,
		chunkSizeWarningLimit: 500,
		assetsInlineLimit: 4096,
	},
	resolve: {
		alias: {
			'@': resolve(__dirname, './src'),
			'@/components': resolve(__dirname, './src/components'),
			'@/constants': resolve(__dirname, './src/constants'),
			'@/hooks': resolve(__dirname, './src/hooks'),
			'@/utils': resolve(__dirname, './src/utils'),
			'@/types': resolve(__dirname, './src/types'),
		},
	},
	esbuild: {
		target: 'es2020',
		treeShaking: true,
		minifyIdentifiers: true,
		minifySyntax: true,
		minifyWhitespace: true,
		drop: process.env.NODE_ENV === 'production' ? ['console', 'debugger'] : [],
		pure: ['console.log', 'console.warn', 'console.error'],
	},
	optimizeDeps: {
		include: [
			'react',
			'react-dom',
			'@base-ui-components/react',
		],
		exclude: [
			'@lexical/code',
			'@lexical/history',
			'@lexical/html',
			'@lexical/link',
			'@lexical/list',
			'@lexical/markdown',
			'@lexical/plain-text',
			'@lexical/react',
			'@lexical/rich-text',
			'@lexical/selection',
			'@lexical/table',
			'@lexical/utils',
			'@vidstack/react',
			'lexical',
		],
	},
} satisfies UserConfig);
