# Miss UI Web - Export Reference

This document provides a comprehensive reference for all available exports from the Miss UI Web library.

## Main Entry Points

### Default Export (`@miss-ui/web`)
The main entry point includes all components except heavy Factory components.

```tsx
import { 
  // Core components
  CoreDocument, CoreView, CoreText,
  
  // Element components  
  ElementButton, ElementCard, ElementIcon,
  
  // Collection components
  CollectionIndicator, CollectionCode,
  
  // Module components
  ModulePopover, ModuleTabs,
  
  // Base components
  BaseFlex, BasePortal,
  
  // Utilities
  lightTheme, darkTheme, useDOMRef
} from '@miss-ui/web';
```

## Category-Specific Exports

### Core Components (`@miss-ui/web/core`)
Document structure and basic layout components.

```tsx
import {
  // Document components
  CoreDocument,
  DocumentHead,
  DocumentMeta,
  DocumentTitle,
  DocumentScript,
  DocumentStyle,
  
  // View components
  CoreView,
  ViewContainer,
  ViewScrollbar,
  ViewDivider,
  ViewSpacer,
  ViewRow,
  ViewColumn,
  
  // Text components
  CoreText,
  TextHeading,
  TextParagraph,
  TextLink,
  TextLabel,
  TextAbbr,
  TextKbd,
  TextMark,
  TextSub,
  TextSup,
  TextTime,
  TextQuote
} from '@miss-ui/web/core';
```

### Element Components (`@miss-ui/web/elements`)
Interactive and display elements.

```tsx
import {
  // Layout elements
  ElementRow,
  ElementColumn,
  
  // Interactive elements
  ElementButton,
  ElementMenu,
  ElementMenuItem,
  ElementList,
  ElementListItem,
  
  // Display elements
  ElementTable,
  ElementTableHeader,
  ElementTableBody,
  ElementTableFooter,
  ElementTableRow,
  ElementTableHeaderCell,
  ElementTableCell,
  ElementIcon,
  ElementCard,
  ElementCardHeader,
  ElementCardBody,
  ElementCardFooter,
  ElementMessage,
  ElementMessageIcon,
  ElementMessageCloseButton,
  
  // Media elements
  ElementMedia,
  ElementMediaImage,
  ElementMediaVideo,
  ElementMediaAudio,
  ElementMediaTrack,
  ElementMediaEmbed,
  ElementMediaSvg,
  ElementMediaAspectRatio,
  ElementMediaCaption
} from '@miss-ui/web/elements';
```

### Collection Components (`@miss-ui/web/collections`)
Grouped content and indicators.

```tsx
import {
  // Indicator components
  CollectionIndicator,
  CollectionIndicatorLoading,
  CollectionIndicatorProgress,
  CollectionIndicatorPlaceholder,
  CollectionIndicatorOverlay,
  
  // Content organization
  CollectionDivider,
  
  // Code components
  CollectionCode,
  CollectionCodeBase,
  CollectionCodeBlock,
  CollectionCodeHighlight,
  CollectionCodePreview
} from '@miss-ui/web/collections';
```

### Module Components (`@miss-ui/web/modules`)
Complex interactive patterns.

```tsx
import {
  // Navigation
  ModuleTree,
  ModuleTabs,
  ModulePagination,
  
  // Overlays
  ModulePopover,
  ModulePopoverTrigger,
  ModulePopoverContent,
  ModuleTooltip,
  ModuleDropdown,
  
  // Interactive controls
  ModuleSticky
} from '@miss-ui/web/modules';
```

### Base Components (`@miss-ui/web/base`)
Internal utilities and primitives.

```tsx
import {
  // Utilities
  BasePortal,
  BaseTransition,
  BaseOverlay,
  BaseDrip,
  BaseClone,
  BaseLink,
  BaseFlex,
  BasePre,
  BaseCode,
  BaseScrollArea,
  
  // Slot components
  Slot,
  Slottable,
  Clone,
  
  // Field primitives
  BaseField,
  FieldText,
  FieldSelect,
  FieldCheckbox,
  FieldSwitch,
  FieldRadio,
  FieldFile,
  FieldPassword,
  FieldHidden,
  FieldDate,
  FieldRange,
  FieldColor
} from '@miss-ui/web/base';
```

## Factory Components

### Light Factories (`@miss-ui/web/factories-light`)
Minimal dependencies, suitable for main bundle.

```tsx
import {
  // Layout factory
  FactoryLayout,
  FactoryLayoutBase,
  FactoryLayoutHeader,
  FactoryLayoutMain,
  FactoryLayoutSidebar,
  FactoryLayoutContent,
  FactoryLayoutFooter,
  
  // Toast factory
  FactoryToast,
  FactoryToastProvider,
  FactoryToastContainer,
  useFactoryToast
} from '@miss-ui/web/factories-light';
```

### Standard Factories (`@miss-ui/web/factories`)
Medium-weight dependencies, good for code splitting.

```tsx
import {
  // All light factories plus:
  
  // Modal factory
  FactoryModal,
  FactoryModalHeader,
  FactoryModalBody,
  FactoryModalFooter,
  FactoryModalBackdrop,
  FactoryModalTrigger,
  useFactoryModal,
  factoryModalManager,
  
  // Form factory
  FactoryForm,
  FactoryFormField,
  FactoryFormGroup,
  useFactoryForm,
  validateFactoryFormField,
  
  // Search factory
  FactorySearch,
  useFactorySearchContext,
  
  // Select factory
  FactorySelect,
  
  // DataTable factory
  FactoryDataTable,
  useFactoryDataTable,
  
  // Stepper factory
  FactoryStepper,
  FactoryStepHeader,
  FactoryStepContent,
  FactoryStepProgress,
  FactoryStepNavigation,
  useFactoryStepper,
  
  // Tabber factory
  FactoryTabber,
  FactoryTabberTab,
  FactoryTabberPanel,
  useFactoryTabber,
  
  // Kbar factory
  FactoryKbar,
  FactoryKbarProvider,
  FactoryKbarSearch,
  FactoryKbarResults,
  FactoryKbarActionItem,
  useFactoryKbar
} from '@miss-ui/web/factories';
```

### Heavy Factories (`@miss-ui/web/factories-heavy`)
Large dependencies, should be loaded lazily.

```tsx
import {
  // Editor factory (Lexical-based)
  FactoryEditor,
  useFactoryEditor,
  
  // Player factory (Vidstack-based)
  FactoryPlayer
} from '@miss-ui/web/factories-heavy';

// Or load dynamically
const { FactoryEditor } = await import('@miss-ui/web/factories-heavy');
```

### Dynamic Factory Loading (`@miss-ui/web/factories-dynamic`)
Utilities for dynamic component loading.

```tsx
import {
  // Dynamic loaders
  FactoryLoaders,
  FactoryRegistry,
  createLazyFactory,
  FactorySuspense,
  useFactoryComponent,
  
  // Individual loaders
  loadLayoutFactory,
  loadToastFactory,
  loadFormFactory,
  loadEditorFactory,
  loadPlayerFactory
} from '@miss-ui/web/factories-dynamic';

// Usage
const LazyEditor = createLazyFactory(() => FactoryLoaders.Editor());

function MyComponent() {
  return (
    <FactorySuspense fallback={<div>Loading...</div>}>
      <LazyEditor />
    </FactorySuspense>
  );
}
```

## Utility Exports

### Constants (`@miss-ui/web/constants`)
Theme tokens and configuration.

```tsx
import {
  // Theme tokens
  baseThemeTokens,
  lightTheme,
  darkTheme,
  lightThemeCSSVars,
  darkThemeCSSVars,
  
  // Default configurations
  defaultColors,
  globalStyles,
  themeMap,
  themeUtils,
  
  // Keyboard constants
  keyCodes
} from '@miss-ui/web/constants';
```

### Hooks (`@miss-ui/web/hooks`)
Custom React hooks.

```tsx
import {
  useDOMRef,
  useDrip
} from '@miss-ui/web/hooks';
```

### Utils (`@miss-ui/web/utils`)
Utility functions.

```tsx
import {
  css,
  cssOptimization,
  themeProvider,
  composeRefs,
  componentHelpers
} from '@miss-ui/web/utils';
```

### Types (`@miss-ui/web/types`)
TypeScript type definitions.

```tsx
import type {
  BaseComponentProps,
  SizeVariant,
  ColorVariant,
  StyleVariant,
  RadiusVariant,
  StateProps,
  LayoutProps,
  VariantProps,
  FormFieldProps,
  ThemeMode,
  ResponsiveValue,
  BreakpointKey
} from '@miss-ui/web/types';
```

## Individual Component Exports

For maximum tree-shaking, you can import individual components:

```tsx
// Core components
import { Document } from '@miss-ui/web/core/Document';
import { View } from '@miss-ui/web/core/View';
import { Text } from '@miss-ui/web/core/Text';

// Element components
import { Button } from '@miss-ui/web/elements/Button';
import { Card } from '@miss-ui/web/elements/Card';
import { Icon } from '@miss-ui/web/elements/Icon';

// Collection components
import { Indicator } from '@miss-ui/web/collections/Indicator';
import { Code } from '@miss-ui/web/collections/Code';
import { Divider } from '@miss-ui/web/collections/Divider';

// Module components
import { Popover } from '@miss-ui/web/modules/Popover';
import { Tabs } from '@miss-ui/web/modules/Tabs';
import { Tree } from '@miss-ui/web/modules/Tree';

// Factory components
import { Layout } from '@miss-ui/web/factories/Layout';
import { Toast } from '@miss-ui/web/factories/Toast';
import { Modal } from '@miss-ui/web/factories/Modal';

// Base components
import { Portal } from '@miss-ui/web/base/Portal';
import { Transition } from '@miss-ui/web/base/Transition';
import { Field } from '@miss-ui/web/base/Field';
```

## Bundle Size Optimization

Choose the import strategy based on your needs:

1. **Full Import** (~120KB): `import { ... } from '@miss-ui/web'`
2. **Category Import** (~45-80KB): `import { ... } from '@miss-ui/web/elements'`
3. **Individual Import** (~2-15KB): `import { ... } from '@miss-ui/web/elements/Button'`
4. **Factory Splitting**: Use separate factory entry points
5. **Dynamic Loading**: Use `factories-dynamic` for lazy loading

## Type-Only Imports

For type definitions without runtime code:

```tsx
import type { 
  ElementButtonProps,
  CoreTextProps,
  FactoryModalProps 
} from '@miss-ui/web';

// Or from specific categories
import type { ElementButtonProps } from '@miss-ui/web/elements';
import type { CoreTextProps } from '@miss-ui/web/core';
```
