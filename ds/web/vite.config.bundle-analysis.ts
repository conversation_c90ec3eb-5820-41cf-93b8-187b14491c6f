/**
 * Vite Configuration for Enhanced Bundle Analysis
 * This configuration provides comprehensive bundle analysis and monitoring
 */

import type { UserConfig } from 'vite';
import { defineConfig } from 'vite';
import { resolve } from 'path';
import { vanillaExtractPlugin } from '@vanilla-extract/vite-plugin';
import { visualizer } from 'rollup-plugin-visualizer';
import { analyzer } from 'vite-bundle-analyzer';

export default defineConfig({
	plugins: [
		vanillaExtractPlugin({
			identifiers: process.env.NODE_ENV === 'production' ? 'short' : 'debug',
		}),
		// Comprehensive bundle visualization
		visualizer({
			filename: 'dist/analysis/bundle-treemap.html',
			open: false,
			gzipSize: true,
			brotliSize: true,
			template: 'treemap',
			title: 'Miss UI Web - Bundle Analysis (Treemap)',
			projectRoot: process.cwd(),
		}),
		visualizer({
			filename: 'dist/analysis/bundle-sunburst.html',
			open: false,
			gzipSize: true,
			brotliSize: true,
			template: 'sunburst',
			title: 'Miss UI Web - Bundle Analysis (Sunburst)',
			projectRoot: process.cwd(),
		}),
		visualizer({
			filename: 'dist/analysis/bundle-network.html',
			open: false,
			gzipSize: true,
			brotliSize: true,
			template: 'network',
			title: 'Miss UI Web - Bundle Analysis (Network)',
			projectRoot: process.cwd(),
		}),
		visualizer({
			filename: 'dist/analysis/bundle-list.html',
			open: false,
			gzipSize: true,
			brotliSize: true,
			template: 'list',
			title: 'Miss UI Web - Bundle Analysis (List)',
			projectRoot: process.cwd(),
		}),
		visualizer({
			filename: 'dist/analysis/bundle-raw-data.json',
			template: 'raw-data',
			gzipSize: true,
			brotliSize: true,
			projectRoot: process.cwd(),
		}),
		// Enhanced bundle analyzer
		analyzer({
			analyzerMode: 'static',
			reportFilename: 'dist/analysis/bundle-analyzer-report.html',
			openAnalyzer: false,
			generateStatsFile: true,
			statsFilename: 'dist/analysis/bundle-stats.json',
			logLevel: 'info',
			analyzerHost: '127.0.0.1',
			analyzerPort: 8888,
			defaultSizes: 'gzip',
			reportTitle: 'Miss UI Web Bundle Analysis',
			template: 'treemap',
		}),
	],
	build: {
		lib: {
			entry: {
				// Main entry point
				index: resolve(__dirname, 'src/index.ts'),
				// Factory entry points for code splitting
				'factories-light': resolve(__dirname, 'src/factories-light.ts'),
				factories: resolve(__dirname, 'src/factories.ts'),
				'factories-heavy': resolve(__dirname, 'src/factories-heavy.ts'),
				// Individual component entries for better tree-shaking
				'core/index': resolve(__dirname, 'src/components/Core.Document/importer.ts'),
				'elements/index': resolve(__dirname, 'src/components/Element.Button/importer.ts'),
				'collections/index': resolve(__dirname, 'src/components/Collection.Code/index.ts'),
				'modules/index': resolve(__dirname, 'src/components/Module.importer.ts'),
				'base/index': resolve(__dirname, 'src/components/Base/importer.ts'),
				// Utilities
				constants: resolve(__dirname, 'src/constants/index.ts'),
				hooks: resolve(__dirname, 'src/hooks/index.ts'),
				utils: resolve(__dirname, 'src/utils/index.ts'),
				types: resolve(__dirname, 'src/types/index.ts'),
			},
			name: 'MissUIWeb',
			formats: ['es', 'cjs'],
			fileName: (format, entryName) => `${entryName}.${format}.js`,
		},
		rollupOptions: {
			external: [
				'react',
				'react-dom',
				'react/jsx-runtime',
				'@base-ui-components/react',
				// External heavy dependencies for Factory components
				'@lexical/code',
				'@lexical/history',
				'@lexical/html',
				'@lexical/link',
				'@lexical/list',
				'@lexical/markdown',
				'@lexical/plain-text',
				'@lexical/react',
				'@lexical/rich-text',
				'@lexical/selection',
				'@lexical/table',
				'@lexical/utils',
				'@vidstack/react',
				'lexical',
			],
			output: {
				globals: {
					'react': 'React',
					'react-dom': 'ReactDOM',
					'react/jsx-runtime': 'jsxRuntime',
					'@base-ui-components/react': 'BaseUI',
					'@lexical/code': 'LexicalCode',
					'@lexical/history': 'LexicalHistory',
					'@lexical/html': 'LexicalHTML',
					'@lexical/link': 'LexicalLink',
					'@lexical/list': 'LexicalList',
					'@lexical/markdown': 'LexicalMarkdown',
					'@lexical/plain-text': 'LexicalPlainText',
					'@lexical/react': 'LexicalReact',
					'@lexical/rich-text': 'LexicalRichText',
					'@lexical/selection': 'LexicalSelection',
					'@lexical/table': 'LexicalTable',
					'@lexical/utils': 'LexicalUtils',
					'@vidstack/react': 'VidstackReact',
					'lexical': 'Lexical',
				},
				preserveModules: true,
				preserveModulesRoot: 'src',
				exports: 'named',
				// Enhanced chunk splitting for detailed analysis
				manualChunks: (id) => {
					// Factory components - detailed splitting for analysis
					if (id.includes('Factory.Editor') || id.includes('@lexical')) {
						return 'factory-editor';
					}
					if (id.includes('Factory.Player') || id.includes('@vidstack')) {
						return 'factory-player';
					}
					if (id.includes('Factory.DataTable')) {
						return 'factory-datatable';
					}
					if (id.includes('Factory.Form')) {
						return 'factory-form';
					}
					if (id.includes('Factory.Kbar')) {
						return 'factory-kbar';
					}
					if (id.includes('Factory.Modal')) {
						return 'factory-modal';
					}
					if (id.includes('Factory.Search')) {
						return 'factory-search';
					}
					if (id.includes('Factory.Select')) {
						return 'factory-select';
					}
					if (id.includes('Factory.Stepper')) {
						return 'factory-stepper';
					}
					if (id.includes('Factory.Tabber')) {
						return 'factory-tabber';
					}
					if (id.includes('Factory.Layout') || id.includes('Factory.Toast')) {
						return 'factories-light';
					}
					if (id.includes('Factory.')) {
						return 'factories';
					}
					// Component sections with detailed splitting
					if (id.includes('Core.Document')) {
						return 'core-document';
					}
					if (id.includes('Core.View')) {
						return 'core-view';
					}
					if (id.includes('Core.Text')) {
						return 'core-text';
					}
					if (id.includes('Core.')) {
						return 'core';
					}
					// Element components by category
					if (id.includes('Element.Button') || id.includes('Element.Menu')) {
						return 'elements-interactive';
					}
					if (id.includes('Element.Table')) {
						return 'elements-table';
					}
					if (id.includes('Element.Media')) {
						return 'elements-media';
					}
					if (id.includes('Element.')) {
						return 'elements';
					}
					// Collection components
					if (id.includes('Collection.Code')) {
						return 'collections-code';
					}
					if (id.includes('Collection.')) {
						return 'collections';
					}
					// Module components by complexity
					if (id.includes('Module.Tabs') || id.includes('Module.Pagination')) {
						return 'modules-navigation';
					}
					if (id.includes('Module.Popover') || id.includes('Module.Dropdown') || id.includes('Module.ContextMenu')) {
						return 'modules-overlay';
					}
					if (id.includes('Module.')) {
						return 'modules';
					}
					// Base components
					if (id.includes('Base/Field')) {
						return 'base-fields';
					}
					if (id.includes('Base/')) {
						return 'base';
					}
					// Utilities - separate chunks for better analysis
					if (id.includes('constants/')) {
						return 'constants';
					}
					if (id.includes('hooks/')) {
						return 'hooks';
					}
					if (id.includes('utils/')) {
						return 'utils';
					}
					// Third-party dependencies with specific grouping
					if (id.includes('node_modules')) {
						// Group React-related dependencies
						if (id.includes('react') || id.includes('react-dom')) {
							return 'vendor-react';
						}
						// Group Base UI dependencies
						if (id.includes('@base-ui-components')) {
							return 'vendor-base-ui';
						}
						// Group Vanilla Extract dependencies
						if (id.includes('@vanilla-extract')) {
							return 'vendor-styling';
						}
						// Other vendor dependencies
						return 'vendor';
					}
				},
				// Optimize output for better compression
				compact: true,
				// Enable advanced minification
				generatedCode: {
					constBindings: true,
					objectShorthand: true,
					arrowFunctions: true,
				},
				// Optimize for modern environments
				format: 'es',
				// Optimize asset handling
				assetFileNames: 'assets/[name]-[hash][extname]',
				chunkFileNames: 'chunks/[name]-[hash].js',
				entryFileNames: '[name].js',
			},
			// Enhanced tree-shaking configuration for analysis
			treeshake: {
				moduleSideEffects: (id) => {
					// Mark CSS files as having side effects
					if (id.includes('.css') || id.includes('.scss') || id.includes('.css.ts')) {
						return true;
					}
					// Mark specific modules as having side effects
					if (id.includes('polyfill') || id.includes('shim')) {
						return true;
					}
					// Mark theme files as having side effects (they register CSS variables)
					if (id.includes('theme') && (id.includes('lightTheme') || id.includes('darkTheme'))) {
						return true;
					}
					// Mark global styles as having side effects
					if (id.includes('globalStyles')) {
						return true;
					}
					// Mark Base UI components as having side effects for proper initialization
					if (id.includes('@base-ui-components/react')) {
						return true;
					}
					return false;
				},
				propertyReadSideEffects: false,
				unknownGlobalSideEffects: false,
				// Enable aggressive tree-shaking
				preset: 'smallest',
				// Enable pure annotations
				annotations: true,
				// Optimize function calls
				correctVarValueBeforeDeclaration: true,
				// Advanced tree-shaking options
				tryCatchDeoptimization: false,
				// Enable more aggressive optimizations for analysis
				mangleProps: {
					regex: /^_/,
				},
			},
			// Enable advanced optimizations
			makeAbsoluteExternalsRelative: true,
			// Optimize module resolution
			preserveEntrySignatures: 'strict',
		},
		sourcemap: true,
		target: 'es2020',
		minify: 'esbuild',
		// Optimize CSS extraction
		cssCodeSplit: true,
		// Enhanced build optimizations
		reportCompressedSize: true,
		chunkSizeWarningLimit: 500,
		// Optimize asset handling
		assetsInlineLimit: 4096,
	},
	resolve: {
		alias: {
			'@': resolve(__dirname, './src'),
			'@/components': resolve(__dirname, './src/components'),
			'@/constants': resolve(__dirname, './src/constants'),
			'@/hooks': resolve(__dirname, './src/hooks'),
			'@/utils': resolve(__dirname, './src/utils'),
			'@/types': resolve(__dirname, './src/types'),
		},
	},
	esbuild: {
		target: 'es2020',
		// Enable tree-shaking for TypeScript
		treeShaking: true,
		// Optimize for production
		minifyIdentifiers: true,
		minifySyntax: true,
		minifyWhitespace: true,
		// Remove console logs in production
		drop: process.env.NODE_ENV === 'production' ? ['console', 'debugger'] : [],
		// Enable pure annotations for better tree-shaking
		pure: ['console.log', 'console.warn', 'console.error'],
		// Enhanced optimization settings
		legalComments: 'none',
		// Optimize JSX for better tree-shaking
		jsx: 'automatic',
		jsxDev: process.env.NODE_ENV === 'development',
		// Enable advanced optimizations
		keepNames: false,
		// Optimize for smaller bundles
		platform: 'browser',
		format: 'esm',
	},
	// Optimize dependencies
	optimizeDeps: {
		include: [
			'react',
			'react-dom',
			'@base-ui-components/react',
		],
		exclude: [
			// Exclude heavy Factory dependencies from optimization
			'@lexical/code',
			'@lexical/history',
			'@lexical/html',
			'@lexical/link',
			'@lexical/list',
			'@lexical/markdown',
			'@lexical/plain-text',
			'@lexical/react',
			'@lexical/rich-text',
			'@lexical/selection',
			'@lexical/table',
			'@lexical/utils',
			'@vidstack/react',
			'lexical',
		],
	},
} satisfies UserConfig);
