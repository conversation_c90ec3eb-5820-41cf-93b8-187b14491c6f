# Miss UI Web

A comprehensive, TypeScript-first UI component library built on top of Base UI, designed to provide a consistent and accessible design system for React web applications.

## Features

- 🎨 **Comprehensive Component Library** - 80+ components across 6 categories
- 🔧 **TypeScript-First** - Complete type definitions and IntelliSense support
- 🌳 **Tree-Shaking Optimized** - Import only what you need
- ♿ **Accessibility Built-In** - WCAG compliant with Base UI foundation
- 🎭 **Theme System** - Light/dark themes with customizable tokens
- 📱 **Responsive Design** - Mobile-first responsive components
- 🧪 **Thoroughly Tested** - Comprehensive test coverage
- 📚 **Well Documented** - Complete documentation and examples

## Installation

```bash
pnpm add @miss-ui/web react react-dom
```

## Quick Start

```tsx
import { ElementButton, CoreText } from '@miss-ui/web';

function App() {
  return (
    <div>
      <CoreText.Heading level={1}>Welcome to Miss UI</CoreText.Heading>
      <ElementButton color="primary" onClick={() => alert('Hello!')}>
        Click me
      </ElementButton>
    </div>
  );
}
```

## Component Categories

### Core Components
Document structure and basic layout components:
- **Document** - HTML document management
- **View** - Layout containers and responsive utilities  
- **Text** - Typography components (Heading, Paragraph, Link, etc.)

### Element Components
Interactive and display elements:
- **Button** - Interactive buttons with variants
- **Card** - Content containers
- **Icon** - SVG icon system
- **Table** - Data tables with sorting/filtering
- **Media** - Image, video, and audio components
- **Menu** - Dropdown menus
- **List** - Ordered and unordered lists
- **Message** - Alerts and notifications

### Collection Components
Grouped content and indicators:
- **Indicator** - Loading states and progress bars
- **Code** - Code blocks with syntax highlighting
- **Divider** - Visual separators

### Module Components
Complex interactive patterns:
- **Popover** - Contextual overlays and tooltips
- **Tabs** - Tab navigation
- **Tree** - Hierarchical navigation
- **Pagination** - Page navigation
- **Dropdown** - Select dropdowns with search
- **Sticky** - Position sticky wrapper

### Factory Components
Advanced application patterns:
- **Layout** - Dynamic layout management
- **Toast** - Notification system
- **Modal** - Dialog system
- **Form** - Form management with validation
- **DataTable** - Advanced data tables
- **Editor** - Rich text editor (Lexical-based)
- **Player** - Video player (Vidstack-based)
- **Search** - Search with filtering
- **Stepper** - Step-by-step navigation
- **Kbar** - Command palette

### Base Components
Internal utilities and primitives:
- **Portal** - Render outside DOM hierarchy
- **Transition** - Animation utilities
- **Field** - Form field primitives
- **Flex** - Flexible layouts

## Import Strategies

### Full Library Import
```tsx
import { ElementButton, CoreText } from '@miss-ui/web';
```

### Category-Specific Imports (Better Tree-Shaking)
```tsx
import { ElementButton } from '@miss-ui/web/elements';
import { CoreText } from '@miss-ui/web/core';
```

### Individual Component Imports (Best Tree-Shaking)
```tsx
import { Button } from '@miss-ui/web/elements/Button';
import { Text } from '@miss-ui/web/core/Text';
```

### Factory Components (Code Splitting)
```tsx
// Light factories (small bundle)
import { FactoryLayout, FactoryToast } from '@miss-ui/web/factories-light';

// Standard factories (medium bundle)
import { FactoryModal, FactoryForm } from '@miss-ui/web/factories';

// Heavy factories (large bundle, lazy load)
import { FactoryEditor, FactoryPlayer } from '@miss-ui/web/factories-heavy';

// Dynamic loading
import('@miss-ui/web/factories-heavy').then(({ FactoryEditor }) => {
  // Use FactoryEditor
});
```

## Theme System

```tsx
import { lightTheme, darkTheme } from '@miss-ui/web/constants';

// Apply theme
document.documentElement.className = lightTheme;

// Or use theme provider
import { ThemeProvider } from '@miss-ui/web/utils';

function App() {
  return (
    <ThemeProvider theme="light">
      {/* Your app */}
    </ThemeProvider>
  );
}
```

## TypeScript Support

The library is built with TypeScript-first approach:

```tsx
import type { ElementButtonProps, CoreTextProps } from '@miss-ui/web';

const MyButton: React.FC<ElementButtonProps> = (props) => {
  return <ElementButton {...props} />;
};
```

## Accessibility

All components follow WCAG guidelines and include:
- Proper ARIA attributes
- Keyboard navigation support
- Screen reader compatibility
- Focus management
- High contrast support

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Bundle Sizes

- **Core + Elements**: ~45KB gzipped
- **Full Library**: ~120KB gzipped
- **Individual Components**: 2-15KB gzipped
- **Factory Components**: 20-80KB gzipped each

## Development

```bash
# Install dependencies
pnpm install

# Start development
pnpm dev

# Run tests
pnpm test

# Build library
pnpm build

# Start Storybook
pnpm storybook
```

## Documentation

- [Component Documentation](https://miss-ui.dev/docs)
- [Storybook Examples](https://miss-ui.dev/storybook)
- [Migration Guide](https://miss-ui.dev/migration)

## Contributing

See [CONTRIBUTING.md](./CONTRIBUTING.md) for development guidelines.

## License

MIT License - see [LICENSE](./LICENSE) for details.
