import type { UserConfig } from 'vite';
import { defineConfig } from 'vite';
import { resolve } from 'path';
import { vanillaExtractPlugin } from '@vanilla-extract/vite-plugin';
import { visualizer } from 'rollup-plugin-visualizer';
import { analyzer } from 'vite-bundle-analyzer';

export default defineConfig({
	plugins: [
		vanillaExtractPlugin(),
		// Enhanced bundle analyzer with multiple visualization formats
		visualizer({
			filename: 'dist/stats-treemap.html',
			open: false,
			gzipSize: true,
			brotliSize: true,
			template: 'treemap',
			title: 'Miss UI Web - Bundle Analysis (Treemap)',
			projectRoot: process.cwd(),
		}),
		visualizer({
			filename: 'dist/stats-sunburst.html',
			open: false,
			gzipSize: true,
			brotliSize: true,
			template: 'sunburst',
			title: 'Miss UI Web - Bundle Analysis (Sunburst)',
			projectRoot: process.cwd(),
		}),
		visualizer({
			filename: 'dist/stats-network.html',
			open: false,
			gzipSize: true,
			brotliSize: true,
			template: 'network',
			title: 'Miss UI Web - Bundle Analysis (Network)',
			projectRoot: process.cwd(),
		}),
		visualizer({
			filename: 'dist/stats-list.html',
			open: false,
			gzipSize: true,
			brotliSize: true,
			template: 'list',
			title: 'Miss UI Web - Bundle Analysis (List)',
			projectRoot: process.cwd(),
		}),
		visualizer({
			filename: 'dist/stats.json',
			template: 'raw-data',
			gzipSize: true,
			brotliSize: true,
			projectRoot: process.cwd(),
		}),
		// Additional bundle analyzer for detailed metrics
		analyzer({
			analyzerMode: 'static',
			reportFilename: 'dist/bundle-analyzer-report.html',
			openAnalyzer: false,
			generateStatsFile: true,
			statsFilename: 'dist/bundle-stats.json',
			logLevel: 'info',
			// Enhanced analysis options
			analyzerHost: '127.0.0.1',
			analyzerPort: 8888,
			defaultSizes: 'gzip',
			// Generate additional reports
			reportTitle: 'Miss UI Web Bundle Analysis',
			template: 'treemap',
		}),
	],
	build: {
		lib: {
			entry: {
				// Main entry point
				index: resolve(__dirname, 'src/index.ts'),
				// Factory entry points for code splitting
				'factories-light': resolve(__dirname, 'src/factories-light.ts'),
				factories: resolve(__dirname, 'src/factories.ts'),
				'factories-heavy': resolve(__dirname, 'src/factories-heavy.ts'),
				// Individual component entries for better tree-shaking
				'core/index': resolve(__dirname, 'src/components/Core.Document/importer.ts'),
				'elements/index': resolve(__dirname, 'src/components/Element.Button/importer.ts'),
				'collections/index': resolve(__dirname, 'src/components/Collection.Code/index.ts'),
				'modules/index': resolve(__dirname, 'src/components/Module.importer.ts'),
				'base/index': resolve(__dirname, 'src/components/Base/importer.ts'),
				// Utilities
				constants: resolve(__dirname, 'src/constants/index.ts'),
				hooks: resolve(__dirname, 'src/hooks/index.ts'),
				utils: resolve(__dirname, 'src/utils/index.ts'),
				types: resolve(__dirname, 'src/types/index.ts'),
			},
			name: 'MissUIWeb',
			formats: ['es', 'cjs'],
			fileName: (format, entryName) => `${entryName}.${format}.js`,
		},
		rollupOptions: {
			external: [
				'react',
				'react-dom',
				'react/jsx-runtime',
				'@base-ui-components/react',
				// External heavy dependencies for Factory components
				'@lexical/code',
				'@lexical/history',
				'@lexical/html',
				'@lexical/link',
				'@lexical/list',
				'@lexical/markdown',
				'@lexical/plain-text',
				'@lexical/react',
				'@lexical/rich-text',
				'@lexical/selection',
				'@lexical/table',
				'@lexical/utils',
				'@vidstack/react',
				'lexical',
			],
			output: {
				globals: {
					'react': 'React',
					'react-dom': 'ReactDOM',
					'react/jsx-runtime': 'jsxRuntime',
					'@base-ui-components/react': 'BaseUI',
					'@lexical/code': 'LexicalCode',
					'@lexical/history': 'LexicalHistory',
					'@lexical/html': 'LexicalHTML',
					'@lexical/link': 'LexicalLink',
					'@lexical/list': 'LexicalList',
					'@lexical/markdown': 'LexicalMarkdown',
					'@lexical/plain-text': 'LexicalPlainText',
					'@lexical/react': 'LexicalReact',
					'@lexical/rich-text': 'LexicalRichText',
					'@lexical/selection': 'LexicalSelection',
					'@lexical/table': 'LexicalTable',
					'@lexical/utils': 'LexicalUtils',
					'@vidstack/react': 'VidstackReact',
					'lexical': 'Lexical',
				},
				preserveModules: true,
				preserveModulesRoot: 'src',
				exports: 'named',
				// Optimize chunk splitting for better tree-shaking
				manualChunks: (id) => {
					// Factory components in separate chunks due to heavy dependencies
					if (id.includes('Factory.Editor') || id.includes('@lexical')) {
						return 'factory-editor';
					}
					if (id.includes('Factory.Player') || id.includes('@vidstack')) {
						return 'factory-player';
					}
					if (id.includes('Factory.')) {
						return 'factories';
					}
					// Core components
					if (id.includes('Core.')) {
						return 'core';
					}
					// Element components
					if (id.includes('Element.')) {
						return 'elements';
					}
					// Collection components
					if (id.includes('Collection.')) {
						return 'collections';
					}
					// Module components
					if (id.includes('Module.')) {
						return 'modules';
					}
					// Base components
					if (id.includes('Base/')) {
						return 'base';
					}
					// Utilities
					if (id.includes('constants/') || id.includes('hooks/') || id.includes('utils/')) {
						return 'utils';
					}
				},
			},
			// Enable tree-shaking optimizations
			treeshake: {
				moduleSideEffects: false,
				propertyReadSideEffects: false,
				unknownGlobalSideEffects: false,
			},
		},
		sourcemap: true,
		target: 'es2020',
		minify: 'esbuild',
		// Optimize CSS extraction
		cssCodeSplit: true,
		// Generate detailed build stats
		reportCompressedSize: true,
		chunkSizeWarningLimit: 500,
	},
	resolve: {
		alias: {
			'@': resolve(__dirname, './src'),
			'@/components': resolve(__dirname, './src/components'),
			'@/constants': resolve(__dirname, './src/constants'),
			'@/hooks': resolve(__dirname, './src/hooks'),
			'@/utils': resolve(__dirname, './src/utils'),
			'@/types': resolve(__dirname, './src/types'),
		},
	},
	esbuild: {
		target: 'es2020',
		// Enable tree-shaking for TypeScript
		treeShaking: true,
	},
	// Optimize dependencies
	optimizeDeps: {
		include: [
			'react',
			'react-dom',
			'@base-ui-components/react',
		],
		exclude: [
			// Exclude heavy Factory dependencies from optimization
			'@lexical/code',
			'@lexical/history',
			'@lexical/html',
			'@lexical/link',
			'@lexical/list',
			'@lexical/markdown',
			'@lexical/plain-text',
			'@lexical/react',
			'@lexical/rich-text',
			'@lexical/selection',
			'@lexical/table',
			'@lexical/utils',
			'@vidstack/react',
			'lexical',
		],
	},
} satisfies UserConfig);
