/**
 * Vite Configuration for Enhanced Code Splitting
 * This configuration provides optimal code splitting for Factory components
 */

import type { UserConfig } from 'vite';
import { defineConfig } from 'vite';
import { resolve } from 'path';
import { vanillaExtractPlugin } from '@vanilla-extract/vite-plugin';

export default defineConfig({
	plugins: [
		vanillaExtractPlugin({
			identifiers: process.env.NODE_ENV === 'production' ? 'short' : 'debug',
			esbuildOptions: {
				minify: process.env.NODE_ENV === 'production',
				target: 'es2020',
				legalComments: 'none',
				treeShaking: true,
			},
		}),
	],
	build: {
		lib: {
			entry: {
				// Main entry point
				index: resolve(__dirname, 'src/index.ts'),

				// Factory components with granular splitting
				'factories/layout': resolve(__dirname, 'src/components/Factory.Layout/importer.ts'),
				'factories/toast': resolve(__dirname, 'src/components/Factory.Toast/importer.ts'),
				'factories/form': resolve(__dirname, 'src/components/Factory.Form/importer.ts'),
				'factories/select': resolve(__dirname, 'src/components/Factory.Select/importer.ts'),
				'factories/search': resolve(__dirname, 'src/components/Factory.Search/importer.ts'),
				'factories/modal': resolve(__dirname, 'src/components/Factory.Modal/importer.ts'),
				'factories/kbar': resolve(__dirname, 'src/components/Factory.Kbar/importer.ts'),
				'factories/stepper': resolve(__dirname, 'src/components/Factory.Stepper/importer.ts'),
				'factories/tabber': resolve(__dirname, 'src/components/Factory.Tabber/importer.ts'),
				'factories/datatable': resolve(__dirname, 'src/components/Factory.DataTable/importer.ts'),

				// Heavy factory components (separate chunks)
				'factories/editor': resolve(__dirname, 'src/components/Factory.Editor/importer.ts'),
				'factories/player': resolve(__dirname, 'src/components/Factory.Player/importer.ts'),

				// Component category bundles
				'core/document': resolve(__dirname, 'src/components/Core.Document/importer.ts'),
				'core/view': resolve(__dirname, 'src/components/Core.View/importer.ts'),
				'core/text': resolve(__dirname, 'src/components/Core.Text/importer.ts'),

				'elements/button': resolve(__dirname, 'src/components/Element.Button/importer.ts'),
				'elements/table': resolve(__dirname, 'src/components/Element.Table/importer.ts'),
				'elements/media': resolve(__dirname, 'src/components/Element.Media/importer.ts'),
				'elements/menu': resolve(__dirname, 'src/components/Element.Menu/importer.ts'),
				'elements/list': resolve(__dirname, 'src/components/Element.List/importer.ts'),
				'elements/card': resolve(__dirname, 'src/components/Element.Card/importer.ts'),
				'elements/icon': resolve(__dirname, 'src/components/Element.Icon/importer.ts'),
				'elements/message': resolve(__dirname, 'src/components/Element.Message/importer.ts'),
				'elements/row': resolve(__dirname, 'src/components/Element.Row/importer.ts'),

				'collections/code': resolve(__dirname, 'src/components/Collection.Code/index.ts'),
				'collections/indicator': resolve(__dirname, 'src/components/Collection.Indicator/importer.ts'),
				'collections/divider': resolve(__dirname, 'src/components/Collection.Divider/importer.ts'),

				'modules/tabs': resolve(__dirname, 'src/components/Module.Tabs/importer.ts'),
				'modules/popover': resolve(__dirname, 'src/components/Module.Popover/importer.ts'),
				'modules/dropdown': resolve(__dirname, 'src/components/Module.Dropdown/importer.ts'),
				'modules/pagination': resolve(__dirname, 'src/components/Module.Pagination/importer.ts'),
				'modules/slider': resolve(__dirname, 'src/components/Module.Slider/importer.ts'),
				'modules/tree': resolve(__dirname, 'src/components/Module.Tree/importer.ts'),
				'modules/collapser': resolve(__dirname, 'src/components/Module.Collapser/importer.ts'),
				'modules/contextmenu': resolve(__dirname, 'src/components/Module.ContextMenu/importer.ts'),
				'modules/drawer': resolve(__dirname, 'src/components/Module.Drawer/importer.ts'),
				'modules/sticky': resolve(__dirname, 'src/components/Module.Sticky/importer.ts'),

				'base/field': resolve(__dirname, 'src/components/Base/Field/importer.ts'),
				'base/portal': resolve(__dirname, 'src/components/Base/Portal/importer.ts'),
				'base/transition': resolve(__dirname, 'src/components/Base/Transition/importer.ts'),
				'base/drip': resolve(__dirname, 'src/components/Base/Drip/importer.ts'),
				'base/clone': resolve(__dirname, 'src/components/Base/Clone/importer.ts'),
				'base/flex': resolve(__dirname, 'src/components/Base/Flex/importer.ts'),
				'base/scrollarea': resolve(__dirname, 'src/components/Base/ScrollArea/importer.ts'),

				// Utilities
				constants: resolve(__dirname, 'src/constants/index.ts'),
				hooks: resolve(__dirname, 'src/hooks/index.ts'),
				utils: resolve(__dirname, 'src/utils/index.ts'),
				types: resolve(__dirname, 'src/types/index.ts'),
			},
			name: 'MissUIWeb',
			formats: ['es', 'cjs'],
			fileName: (format, entryName) => `${entryName}.${format}.js`,
		},
		rollupOptions: {
			external: [
				'react',
				'react-dom',
				'react/jsx-runtime',
				'@base-ui-components/react',
				// External heavy dependencies for Factory components
				'@lexical/code',
				'@lexical/history',
				'@lexical/html',
				'@lexical/link',
				'@lexical/list',
				'@lexical/markdown',
				'@lexical/plain-text',
				'@lexical/react',
				'@lexical/rich-text',
				'@lexical/selection',
				'@lexical/table',
				'@lexical/utils',
				'@vidstack/react',
				'lexical',
			],
			output: {
				globals: {
					'react': 'React',
					'react-dom': 'ReactDOM',
					'react/jsx-runtime': 'jsxRuntime',
					'@base-ui-components/react': 'BaseUI',
					'@lexical/code': 'LexicalCode',
					'@lexical/history': 'LexicalHistory',
					'@lexical/html': 'LexicalHTML',
					'@lexical/link': 'LexicalLink',
					'@lexical/list': 'LexicalList',
					'@lexical/markdown': 'LexicalMarkdown',
					'@lexical/plain-text': 'LexicalPlainText',
					'@lexical/react': 'LexicalReact',
					'@lexical/rich-text': 'LexicalRichText',
					'@lexical/selection': 'LexicalSelection',
					'@lexical/table': 'LexicalTable',
					'@lexical/utils': 'LexicalUtils',
					'@vidstack/react': 'VidstackReact',
					'lexical': 'Lexical',
				},
				preserveModules: false, // Disable for better chunking
				exports: 'named',
				// Advanced chunk splitting strategy
				manualChunks: (id) => {
					// Heavy dependencies get their own chunks
					if (id.includes('@lexical') || id.includes('lexical')) {
						return 'vendor-lexical';
					}
					if (id.includes('@vidstack')) {
						return 'vendor-vidstack';
					}
					if (id.includes('@base-ui-components')) {
						return 'vendor-base-ui';
					}
					if (id.includes('@vanilla-extract')) {
						return 'vendor-styling';
					}

					// Factory components by complexity
					if (id.includes('Factory.Editor')) {
						return 'factory-editor';
					}
					if (id.includes('Factory.Player')) {
						return 'factory-player';
					}
					if (id.includes('Factory.DataTable')) {
						return 'factory-datatable';
					}
					if (id.includes('Factory.Form')) {
						return 'factory-form';
					}
					if (id.includes('Factory.Kbar')) {
						return 'factory-kbar';
					}
					if (id.includes('Factory.Modal')) {
						return 'factory-modal';
					}
					if (id.includes('Factory.Search')) {
						return 'factory-search';
					}
					if (id.includes('Factory.Select')) {
						return 'factory-select';
					}
					if (id.includes('Factory.Stepper')) {
						return 'factory-stepper';
					}
					if (id.includes('Factory.Tabber')) {
						return 'factory-tabber';
					}
					if (id.includes('Factory.Layout') || id.includes('Factory.Toast')) {
						return 'factory-light';
					}

					// Component categories
					if (id.includes('Core.Document')) {
						return 'core-document';
					}
					if (id.includes('Core.View')) {
						return 'core-view';
					}
					if (id.includes('Core.Text')) {
						return 'core-text';
					}

					if (id.includes('Element.Table')) {
						return 'element-table';
					}
					if (id.includes('Element.Media')) {
						return 'element-media';
					}
					if (id.includes('Element.Button') || id.includes('Element.Menu')) {
						return 'element-interactive';
					}
					if (id.includes('Element.')) {
						return 'elements';
					}

					if (id.includes('Collection.Code')) {
						return 'collection-code';
					}
					if (id.includes('Collection.')) {
						return 'collections';
					}

					if (id.includes('Module.Tabs') || id.includes('Module.Pagination')) {
						return 'module-navigation';
					}
					if (id.includes('Module.Popover') || id.includes('Module.Dropdown')) {
						return 'module-overlay';
					}
					if (id.includes('Module.')) {
						return 'modules';
					}

					if (id.includes('Base/Field')) {
						return 'base-field';
					}
					if (id.includes('Base/')) {
						return 'base';
					}

					// Utilities
					if (id.includes('constants/')) {
						return 'constants';
					}
					if (id.includes('hooks/')) {
						return 'hooks';
					}
					if (id.includes('utils/')) {
						return 'utils';
					}

					// Vendor dependencies
					if (id.includes('node_modules')) {
						if (id.includes('react')) {
							return 'vendor-react';
						}
						return 'vendor';
					}
				},
				// Optimize output for better compression
				compact: true,
				// Enable advanced minification
				generatedCode: {
					constBindings: true,
					objectShorthand: true,
					arrowFunctions: true,
				},
				// Optimize for modern environments
				format: 'es',
				// Optimize asset handling
				assetFileNames: 'assets/[name]-[hash][extname]',
				chunkFileNames: 'chunks/[name]-[hash].js',
				entryFileNames: '[name].js',
			},
			// Enhanced tree-shaking configuration
			treeshake: {
				moduleSideEffects: (id) => {
					// Mark CSS files as having side effects
					if (id.includes('.css') || id.includes('.scss') || id.includes('.css.ts')) {
						return true;
					}
					// Mark specific modules as having side effects
					if (id.includes('polyfill') || id.includes('shim')) {
						return true;
					}
					// Mark theme files as having side effects (they register CSS variables)
					if (id.includes('theme') && (id.includes('lightTheme') || id.includes('darkTheme'))) {
						return true;
					}
					// Mark global styles as having side effects
					if (id.includes('globalStyles')) {
						return true;
					}
					// Mark Base UI components as having side effects for proper initialization
					if (id.includes('@base-ui-components/react')) {
						return true;
					}
					// Mark Vanilla Extract files as having side effects
					if (id.includes('@vanilla-extract')) {
						return true;
					}
					return false;
				},
				propertyReadSideEffects: false,
				unknownGlobalSideEffects: false,
				// Enable pure annotations for better tree-shaking
				annotations: true,
				// Optimize function calls and variable declarations
				correctVarValueBeforeDeclaration: true,
				// Advanced tree-shaking optimizations
				tryCatchDeoptimization: false,
				// Enhanced dead code elimination
				pureExternalModules: true,
			},
			// Enable advanced optimizations
			makeAbsoluteExternalsRelative: true,
			// Optimize module resolution
			preserveEntrySignatures: 'strict',
		},
		sourcemap: process.env.NODE_ENV === 'development',
		target: 'es2020',
		minify: 'esbuild',
		// Optimize CSS extraction
		cssCodeSplit: true,
		cssMinify: true,
		cssTarget: 'es2020',
		// Enhanced build optimizations
		reportCompressedSize: true,
		chunkSizeWarningLimit: 500,
		// Optimize asset handling
		assetsInlineLimit: 4096,
	},
	resolve: {
		alias: {
			'@': resolve(__dirname, './src'),
			'@/components': resolve(__dirname, './src/components'),
			'@/constants': resolve(__dirname, './src/constants'),
			'@/hooks': resolve(__dirname, './src/hooks'),
			'@/utils': resolve(__dirname, './src/utils'),
			'@/types': resolve(__dirname, './src/types'),
		},
	},
	esbuild: {
		target: 'es2020',
		// Enable tree-shaking for TypeScript
		treeShaking: true,
		// Optimize for production
		minifyIdentifiers: true,
		minifySyntax: true,
		minifyWhitespace: true,
		// Remove console logs in production
		drop: process.env.NODE_ENV === 'production' ? ['console', 'debugger'] : [],
		// Enable pure annotations for better tree-shaking
		pure: ['console.log', 'console.warn', 'console.error'],
		// Enhanced optimization settings
		legalComments: 'none',
		// Optimize JSX for better tree-shaking
		jsx: 'automatic',
		jsxDev: process.env.NODE_ENV === 'development',
		// Enable advanced optimizations
		keepNames: false,
		// Optimize for smaller bundles
		platform: 'browser',
		format: 'esm',
	},
	// CSS-specific optimizations
	css: {
		devSourcemap: false,
		// CSS modules optimization for better tree-shaking
		modules: {
			localsConvention: 'camelCase',
			generateScopedName: process.env.NODE_ENV === 'production'
				? '[hash:base64:5]'
				: '[name]__[local]___[hash:base64:5]',
		},
	},
	// Optimize dependencies
	optimizeDeps: {
		include: [
			'react',
			'react-dom',
			'@base-ui-components/react',
		],
		exclude: [
			// Exclude heavy Factory dependencies from optimization
			'@lexical/code',
			'@lexical/history',
			'@lexical/html',
			'@lexical/link',
			'@lexical/list',
			'@lexical/markdown',
			'@lexical/plain-text',
			'@lexical/react',
			'@lexical/rich-text',
			'@lexical/selection',
			'@lexical/table',
			'@lexical/utils',
			'@vidstack/react',
			'lexical',
		],
	},
} satisfies UserConfig);
