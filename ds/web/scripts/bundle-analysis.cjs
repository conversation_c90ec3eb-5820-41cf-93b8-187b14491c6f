#!/usr/bin/env node

/**
 * Enhanced Bundle Analysis Script
 * Provides comprehensive bundle analysis with advanced metrics, recommendations, and CI/CD integration
 * Features: Size monitoring, tree-shaking analysis, performance profiling, regression detection
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const DIST_DIR = path.join(__dirname, '../dist');
const ANALYSIS_DIR = path.join(DIST_DIR, 'analysis');
const REPORTS_DIR = path.join(__dirname, '../reports');
const PACKAGE_JSON = path.join(__dirname, '../package.json');

// Ensure directories exist
[ANALYSIS_DIR, REPORTS_DIR].forEach(dir => {
	if (!fs.existsSync(dir)) {
		fs.mkdirSync(dir, { recursive: true });
	}
});

/**
 * Enhanced file size utilities
 */
const FileUtils = {
	getSize: (filePath) => {
		try {
			const stats = fs.statSync(filePath);
			return stats.size;
		} catch (error) {
			return 0;
		}
	},

	getSizeKB: (filePath) => {
		return (FileUtils.getSize(filePath) / 1024).toFixed(2);
	},

	getGzippedSize: (filePath) => {
		try {
			const command = process.platform === 'win32'
				? `powershell -command "& {[System.IO.Compression.GzipStream]::new([System.IO.File]::OpenRead('${filePath}'), [System.IO.Compression.CompressionMode]::Compress) | ForEach-Object {$_.Length}}"`
				: `gzip -c "${filePath}" | wc -c`;
			const size = execSync(command, { encoding: 'utf8' });
			return (parseInt(size.trim()) / 1024).toFixed(2);
		} catch (error) {
			// Fallback: estimate gzip size as ~30% of original
			return (FileUtils.getSize(filePath) * 0.3 / 1024).toFixed(2);
		}
	},

	getBrotliSize: (filePath) => {
		try {
			const command = process.platform === 'win32'
				? `powershell -command "& {[System.IO.File]::ReadAllBytes('${filePath}') | ForEach-Object {$_} | Measure-Object -Sum | Select-Object -ExpandProperty Sum}"`
				: `brotli -c "${filePath}" | wc -c`;
			const size = execSync(command, { encoding: 'utf8' });
			return (parseInt(size.trim()) / 1024).toFixed(2);
		} catch (error) {
			// Fallback: estimate brotli size as ~25% of original
			return (FileUtils.getSize(filePath) * 0.25 / 1024).toFixed(2);
		}
	},
};

/**
 * Bundle configuration with enhanced limits and categorization
 */
const BUNDLE_CONFIG = {
	'index.es.js': {
		name: 'Main Bundle',
		maxSize: 120,
		warningSize: 100,
		critical: true,
		category: 'core',
		description: 'Main library entry point',
	},
	'factories-light.es.js': {
		name: 'Light Factories',
		maxSize: 60,
		warningSize: 45,
		critical: false,
		category: 'factory',
		description: 'Lightweight factory components',
	},
	'factories.es.js': {
		name: 'Standard Factories',
		maxSize: 80,
		warningSize: 60,
		critical: false,
		category: 'factory',
		description: 'Standard factory components',
	},
	'factories-heavy.es.js': {
		name: 'Heavy Factories',
		maxSize: 250,
		warningSize: 200,
		critical: false,
		category: 'factory',
		description: 'Heavy factory components with large dependencies',
	},
	'core/index.es.js': {
		name: 'Core Components',
		maxSize: 40,
		warningSize: 30,
		critical: true,
		category: 'component',
		description: 'Core document and layout components',
	},
	'elements/index.es.js': {
		name: 'Element Components',
		maxSize: 60,
		warningSize: 45,
		critical: true,
		category: 'component',
		description: 'Interactive and display elements',
	},
	'collections/index.es.js': {
		name: 'Collection Components',
		maxSize: 35,
		warningSize: 25,
		critical: false,
		category: 'component',
		description: 'Grouped content and indicators',
	},
	'modules/index.es.js': {
		name: 'Module Components',
		maxSize: 50,
		warningSize: 40,
		critical: false,
		category: 'component',
		description: 'Complex interactive patterns',
	},
	'base/index.es.js': {
		name: 'Base Components',
		maxSize: 25,
		warningSize: 20,
		critical: false,
		category: 'utility',
		description: 'Low-level building blocks',
	},
	'constants.es.js': {
		name: 'Constants',
		maxSize: 15,
		warningSize: 10,
		critical: true,
		category: 'utility',
		description: 'Theme tokens and constants',
	},
	'hooks.es.js': {
		name: 'Hooks',
		maxSize: 10,
		warningSize: 8,
		critical: true,
		category: 'utility',
		description: 'React hooks and utilities',
	},
	'utils.es.js': {
		name: 'Utils',
		maxSize: 20,
		warningSize: 15,
		critical: true,
		category: 'utility',
		description: 'Utility functions and helpers',
	},
};

/**
 * Comprehensive bundle analysis
 */
function analyzeBundleMetrics() {
	console.log('📊 Comprehensive Bundle Analysis');
	console.log('='.repeat(100));
	console.log(`Generated: ${new Date().toISOString()}`);
	console.log('='.repeat(100));

	const results = [];
	let totalRaw = 0;
	let totalGzipped = 0;
	let totalBrotli = 0;
	let criticalPathSize = 0;

	console.log(`${'Bundle'.padEnd(25)} ${'Category'.padEnd(12)} ${'Raw'.padStart(10)} ${'Gzipped'.padStart(10)} ${'Brotli'.padStart(10)} ${'Ratio'.padStart(8)} ${'Status'.padStart(10)}`);
	console.log('='.repeat(100));

	Object.entries(BUNDLE_CONFIG).forEach(([filename, config]) => {
		const filePath = path.join(DIST_DIR, filename);
		const rawSize = parseFloat(FileUtils.getSizeKB(filePath));
		const gzippedSize = parseFloat(FileUtils.getGzippedSize(filePath));
		const brotliSize = parseFloat(FileUtils.getBrotliSize(filePath));
		const compressionRatio = rawSize > 0 ? ((gzippedSize / rawSize) * 100).toFixed(1) : '0.0';

		totalRaw += rawSize;
		totalGzipped += gzippedSize;
		totalBrotli += brotliSize;

		if (config.critical) {
			criticalPathSize += gzippedSize;
		}

		// Determine status
		let status = '✅ OK';
		if (gzippedSize > config.maxSize) {
			status = '❌ FAIL';
		} else if (gzippedSize > config.warningSize) {
			status = '⚠️  WARN';
		}

		console.log(`${config.name.padEnd(25)} ${config.category.padEnd(12)} ${rawSize.toFixed(2).padStart(8)} KB ${gzippedSize.toFixed(2).padStart(8)} KB ${brotliSize.toFixed(2).padStart(8)} KB ${compressionRatio.padStart(6)}% ${status.padStart(10)}`);

		results.push({
			filename,
			config,
			metrics: {
				rawSize,
				gzippedSize,
				brotliSize,
				compressionRatio: parseFloat(compressionRatio),
			},
			status: status.includes('FAIL') ? 'fail' : status.includes('WARN') ? 'warn' : 'ok',
		});
	});

	console.log('='.repeat(100));
	console.log(`${'TOTAL'.padEnd(25)} ${'ALL'.padEnd(12)} ${totalRaw.toFixed(2).padStart(8)} KB ${totalGzipped.toFixed(2).padStart(8)} KB ${totalBrotli.toFixed(2).padStart(8)} KB ${((totalGzipped / totalRaw) * 100).toFixed(1).padStart(6)}%`);
	console.log(`${'Critical Path'.padEnd(25)} ${'CRITICAL'.padEnd(12)} ${'-'.padStart(8)}    ${criticalPathSize.toFixed(2).padStart(8)} KB ${'-'.padStart(8)}    ${'-'.padStart(6)}  `);
	console.log();

	return {
		results,
		totals: {
			rawSize: totalRaw,
			gzippedSize: totalGzipped,
			brotliSize: totalBrotli,
			criticalPathSize,
		},
		analysis: {
			compressionRatio: (totalGzipped / totalRaw) * 100,
			brotliImprovement: ((totalGzipped - totalBrotli) / totalGzipped) * 100,
		},
	};
}

/**
 * Analyze bundle composition from stats
 */
function analyzeBundleComposition() {
	console.log('📦 Bundle Composition Analysis');
	console.log('='.repeat(80));

	const statsFiles = [
		path.join(ANALYSIS_DIR, 'bundle-raw-data.json'),
		path.join(DIST_DIR, 'stats.json'),
		path.join(ANALYSIS_DIR, 'bundle-stats.json'),
	];

	let statsData = null;

	// Try to find stats file
	for (const statsFile of statsFiles) {
		if (fs.existsSync(statsFile)) {
			try {
				statsData = JSON.parse(fs.readFileSync(statsFile, 'utf8'));
				console.log(`📄 Using stats from: ${path.basename(statsFile)}`);
				break;
			} catch (error) {
				continue;
			}
		}
	}

	if (!statsData) {
		console.log('⚠️  No bundle stats found. Run build with analysis mode:');
		console.log('   pnpm build:analyze');
		console.log();
		return null;
	}

	// Analyze module categories
	const moduleCategories = {
		'Factory Components': { modules: [], totalSize: 0 },
		'Core Components': { modules: [], totalSize: 0 },
		'Element Components': { modules: [], totalSize: 0 },
		'Collection Components': { modules: [], totalSize: 0 },
		'Module Components': { modules: [], totalSize: 0 },
		'Base Components': { modules: [], totalSize: 0 },
		'Third Party': { modules: [], totalSize: 0 },
		'Utilities': { modules: [], totalSize: 0 },
		'Styling': { modules: [], totalSize: 0 },
		'Other': { modules: [], totalSize: 0 },
	};

	if (statsData.modules) {
		statsData.modules.forEach(module => {
			const name = module.name || module.identifier || 'Unknown';
			const size = (module.size || 0) / 1024;

			let category = 'Other';
			if (name.includes('Factory.')) category = 'Factory Components';
			else if (name.includes('Core.')) category = 'Core Components';
			else if (name.includes('Element.')) category = 'Element Components';
			else if (name.includes('Collection.')) category = 'Collection Components';
			else if (name.includes('Module.')) category = 'Module Components';
			else if (name.includes('Base/')) category = 'Base Components';
			else if (name.includes('node_modules')) category = 'Third Party';
			else if (name.includes('utils/') || name.includes('hooks/') || name.includes('constants/')) category = 'Utilities';
			else if (name.includes('.css') || name.includes('styles') || name.includes('@vanilla-extract')) category = 'Styling';

			moduleCategories[category].modules.push({ name, size });
			moduleCategories[category].totalSize += size;
		});

		// Display categorized results
		console.log('\n🔍 Module Distribution by Category:');
		Object.entries(moduleCategories)
			.filter(([, data]) => data.modules.length > 0)
			.sort((a, b) => b[1].totalSize - a[1].totalSize)
			.forEach(([category, data]) => {
				console.log(`\n   ${category} (${data.totalSize.toFixed(2)} KB, ${data.modules.length} modules):`);

				data.modules
					.sort((a, b) => b.size - a.size)
					.slice(0, 5)
					.forEach((module, index) => {
						const displayName = module.name.length > 70
							? module.name.substring(0, 67) + '...'
							: module.name;
						console.log(`     ${index + 1}. ${displayName} (${module.size.toFixed(2)} KB)`);
					});

				if (data.modules.length > 5) {
					console.log(`     ... and ${data.modules.length - 5} more modules`);
				}
			});
	}

	console.log();
	return moduleCategories;
}

/**
 * Analyze tree-shaking effectiveness
 */
function analyzeTreeShakingEffectiveness() {
	console.log('🌳 Tree-Shaking Effectiveness Analysis');
	console.log('='.repeat(80));

	const srcDir = path.join(__dirname, '../src');
	const analysis = {
		totalFiles: 0,
		totalExports: 0,
		barrelExports: 0,
		namespaceImports: 0,
		sideEffectFiles: 0,
		issues: [],
	};

	function analyzeFile(filePath) {
		try {
			const content = fs.readFileSync(filePath, 'utf8');
			const relativePath = path.relative(srcDir, filePath);

			analysis.totalFiles++;

			// Count exports
			const exportMatches = content.match(/export\s+(?:const|function|class|interface|type|default)/g) || [];
			analysis.totalExports += exportMatches.length;

			// Check for barrel exports
			if (content.includes('export * from') || content.includes('export {') && content.includes('} from')) {
				analysis.barrelExports++;
				analysis.issues.push({
					file: relativePath,
					type: 'barrel-export',
					severity: 'warning',
					message: 'Barrel exports may prevent tree-shaking',
					suggestion: 'Use direct imports instead of re-exports',
				});
			}

			// Check for namespace imports
			if (content.includes('import * as')) {
				analysis.namespaceImports++;
				analysis.issues.push({
					file: relativePath,
					type: 'namespace-import',
					severity: 'warning',
					message: 'Namespace imports prevent tree-shaking',
					suggestion: 'Use named imports: import { specific } from "module"',
				});
			}

			// Check for potential side effects
			const sideEffectPatterns = [
				/console\./,
				/window\./,
				/document\./,
				/global\./,
				/process\.env/,
			];

			if (sideEffectPatterns.some(pattern => pattern.test(content))) {
				analysis.sideEffectFiles++;
			}

		} catch (error) {
			// Skip files that can't be read
		}
	}

	function scanDirectory(dir) {
		try {
			const files = fs.readdirSync(dir);

			files.forEach(file => {
				const filePath = path.join(dir, file);
				const stat = fs.statSync(filePath);

				if (stat.isDirectory()) {
					scanDirectory(filePath);
				} else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
					analyzeFile(filePath);
				}
			});
		} catch (error) {
			// Directory might not exist, skip
		}
	}

	scanDirectory(srcDir);

	// Report findings
	console.log(`📊 Tree-shaking Analysis Results:`);
	console.log(`   - Total files analyzed: ${analysis.totalFiles}`);
	console.log(`   - Total exports: ${analysis.totalExports}`);
	console.log(`   - Barrel exports: ${analysis.barrelExports}`);
	console.log(`   - Namespace imports: ${analysis.namespaceImports}`);
	console.log(`   - Files with side effects: ${analysis.sideEffectFiles}`);

	const effectiveness = Math.max(0, 100 - (analysis.barrelExports + analysis.namespaceImports) * 5);
	console.log(`   - Tree-shaking effectiveness: ${effectiveness.toFixed(1)}%`);

	if (analysis.issues.length > 0) {
		console.log('\n⚠️  Tree-shaking Issues:');
		const groupedIssues = analysis.issues.reduce((acc, issue) => {
			if (!acc[issue.type]) acc[issue.type] = [];
			acc[issue.type].push(issue);
			return acc;
		}, {});

		Object.entries(groupedIssues).forEach(([type, issues]) => {
			console.log(`\n   ${type.replace('-', ' ').toUpperCase()} (${issues.length} files):`);
			issues.slice(0, 5).forEach(issue => {
				console.log(`     - ${issue.file}: ${issue.message}`);
				console.log(`       💡 ${issue.suggestion}`);
			});
			if (issues.length > 5) {
				console.log(`     ... and ${issues.length - 5} more files`);
			}
		});
	} else {
		console.log('\n✅ No major tree-shaking issues detected');
	}

	console.log();
	return analysis;
}

/**
 * Generate performance recommendations
 */
function generateRecommendations(bundleAnalysis, compositionAnalysis, treeShakingAnalysis) {
	console.log('💡 Performance Optimization Recommendations');
	console.log('='.repeat(80));

	const recommendations = [];

	// Bundle size recommendations
	const failedBundles = bundleAnalysis.results.filter(r => r.status === 'fail');
	const warningBundles = bundleAnalysis.results.filter(r => r.status === 'warn');

	if (failedBundles.length > 0) {
		recommendations.push({
			priority: 'critical',
			category: 'Bundle Size',
			title: 'Bundle Size Violations',
			description: `${failedBundles.length} bundles exceed size limits`,
			actions: [
				'Move heavy components to separate bundles',
				'Optimize imports and remove unused code',
				'Use dynamic imports for large dependencies',
				'Enable more aggressive tree-shaking',
			],
		});
	}

	if (warningBundles.length > 0) {
		recommendations.push({
			priority: 'warning',
			category: 'Bundle Size',
			title: 'Bundle Size Warnings',
			description: `${warningBundles.length} bundles approaching size limits`,
			actions: [
				'Monitor bundle growth closely',
				'Consider preemptive optimization',
				'Review recent changes for size impact',
			],
		});
	}

	// Compression recommendations
	if (bundleAnalysis.analysis.compressionRatio > 40) {
		recommendations.push({
			priority: 'optimization',
			category: 'Compression',
			title: 'Improve Compression Ratio',
			description: `Current compression ratio: ${bundleAnalysis.analysis.compressionRatio.toFixed(1)}%`,
			actions: [
				'Enable Brotli compression on server',
				'Optimize CSS output for better compression',
				'Remove unnecessary whitespace and comments',
				'Use shorter variable names in production',
			],
		});
	}

	// Tree-shaking recommendations
	if (treeShakingAnalysis && treeShakingAnalysis.issues.length > 0) {
		recommendations.push({
			priority: 'optimization',
			category: 'Tree-shaking',
			title: 'Tree-shaking Optimization',
			description: `${treeShakingAnalysis.issues.length} tree-shaking issues detected`,
			actions: [
				'Replace barrel exports with direct imports',
				'Use named imports instead of namespace imports',
				'Mark side-effect modules appropriately',
				'Optimize import/export patterns',
			],
		});
	}

	// Factory component recommendations
	const factoryBundles = bundleAnalysis.results.filter(r => r.config.category === 'factory');
	const totalFactorySize = factoryBundles.reduce((sum, b) => sum + b.metrics.gzippedSize, 0);

	if (totalFactorySize > 200) {
		recommendations.push({
			priority: 'optimization',
			category: 'Code Splitting',
			title: 'Factory Component Optimization',
			description: `Factory components total: ${totalFactorySize.toFixed(2)} KB`,
			actions: [
				'Implement lazy loading for factory components',
				'Use dynamic imports for heavy dependencies',
				'Consider splitting heavy factories further',
				'Optimize factory component dependencies',
			],
		});
	}

	// Critical path recommendations
	if (bundleAnalysis.totals.criticalPathSize > 150) {
		recommendations.push({
			priority: 'performance',
			category: 'Critical Path',
			title: 'Critical Path Optimization',
			description: `Critical path size: ${bundleAnalysis.totals.criticalPathSize.toFixed(2)} KB`,
			actions: [
				'Move non-critical components out of main bundle',
				'Implement progressive loading',
				'Use service workers for caching',
				'Consider lazy loading for non-essential features',
			],
		});
	}

	// Display recommendations
	if (recommendations.length === 0) {
		console.log('✅ No major optimization issues detected!');
		console.log('   Your bundle configuration appears to be well optimized.');
	} else {
		recommendations
			.sort((a, b) => {
				const priorityOrder = { critical: 0, warning: 1, performance: 2, optimization: 3 };
				return priorityOrder[a.priority] - priorityOrder[b.priority];
			})
			.forEach((rec, index) => {
				const priorityIcon = {
					critical: '🔴',
					warning: '🟡',
					performance: '🔵',
					optimization: '🟢',
				}[rec.priority];

				console.log(`\n${index + 1}. ${priorityIcon} ${rec.title} (${rec.category})`);
				console.log(`   ${rec.description}`);
				console.log('   Actions:');
				rec.actions.forEach(action => {
					console.log(`     - ${action}`);
				});
			});
	}

	console.log();
	return recommendations;
}

/**
 * Generate comprehensive report
 */
function generateComprehensiveReport(bundleAnalysis, compositionAnalysis, treeShakingAnalysis, recommendations) {
	const report = {
		timestamp: new Date().toISOString(),
		version: getPackageVersion(),
		summary: {
			totalBundles: bundleAnalysis.results.length,
			failedBundles: bundleAnalysis.results.filter(r => r.status === 'fail').length,
			warningBundles: bundleAnalysis.results.filter(r => r.status === 'warn').length,
			totalSize: bundleAnalysis.totals,
			analysis: bundleAnalysis.analysis,
			treeShakingEffectiveness: treeShakingAnalysis ? (100 - (treeShakingAnalysis.barrelExports + treeShakingAnalysis.namespaceImports) * 5) : null,
		},
		bundles: bundleAnalysis.results,
		composition: compositionAnalysis,
		treeShaking: treeShakingAnalysis,
		recommendations,
		buildInfo: {
			nodeVersion: process.version,
			platform: process.platform,
			arch: process.arch,
			environment: process.env.NODE_ENV || 'development',
		},
	};

	// Save detailed report
	const reportPath = path.join(REPORTS_DIR, `bundle-analysis-${Date.now()}.json`);
	fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

	// Save latest report
	const latestReportPath = path.join(REPORTS_DIR, 'bundle-analysis-latest.json');
	fs.writeFileSync(latestReportPath, JSON.stringify(report, null, 2));

	console.log('📄 Analysis Reports Generated:');
	console.log(`   - Detailed: ${reportPath}`);
	console.log(`   - Latest: ${latestReportPath}`);
	console.log();

	return report;
}

/**
 * Get package version
 */
function getPackageVersion() {
	try {
		const packageJson = JSON.parse(fs.readFileSync(PACKAGE_JSON, 'utf8'));
		return packageJson.version;
	} catch (error) {
		return 'unknown';
	}
}

/**
 * Main execution
 */
function main() {
	const args = process.argv.slice(2);

	console.log('🚀 Miss UI Web - Enhanced Bundle Analysis');
	console.log(`Version: ${getPackageVersion()}`);
	console.log(`Node: ${process.version} | Platform: ${process.platform}`);
	console.log();

	// Run comprehensive analysis
	const bundleAnalysis = analyzeBundleMetrics();
	const compositionAnalysis = analyzeBundleComposition();
	const treeShakingAnalysis = analyzeTreeShakingEffectiveness();
	const recommendations = generateRecommendations(bundleAnalysis, compositionAnalysis, treeShakingAnalysis);

	// Generate comprehensive report
	const report = generateComprehensiveReport(bundleAnalysis, compositionAnalysis, treeShakingAnalysis, recommendations);

	// Exit with appropriate code
	const hasFailures = bundleAnalysis.results.some(r => r.status === 'fail');
	const hasWarnings = bundleAnalysis.results.some(r => r.status === 'warn');

	if (hasFailures) {
		console.log('❌ Bundle analysis failed due to size violations');
		process.exit(1);
	} else if (hasWarnings) {
		console.log('⚠️  Bundle analysis completed with warnings');
		process.exit(0);
	} else {
		console.log('✅ Bundle analysis passed successfully');
		process.exit(0);
	}
}

// Run if called directly
if (require.main === module) {
	main();
}

module.exports = {
	analyzeBundleMetrics,
	analyzeBundleComposition,
	analyzeTreeShakingEffectiveness,
	generateRecommendations,
	generateComprehensiveReport,
};
