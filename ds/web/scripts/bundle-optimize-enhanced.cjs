#!/usr/bin/env node

/**
 * Enhanced Bundle Optimization Script
 * Comprehensive bundle analysis, optimization, and monitoring with CI/CD integration
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const {
	BUNDLE_SIZE_CONFIG,
	PERFORMANCE_BUDGETS,
	CI_CONFIG,
	getBundleConfig,
	checkSizeViolation,
	calculateCriticalPathSize,
	generateSizeRecommendations,
} = require('./bundle-size-config.cjs');

const DIST_DIR = path.join(__dirname, '../dist');
const REPORTS_DIR = path.join(__dirname, '../reports');
const PACKAGE_JSON = path.join(__dirname, '../package.json');

// Ensure reports directory exists
if (!fs.existsSync(REPORTS_DIR)) {
	fs.mkdirSync(REPORTS_DIR, { recursive: true });
}

/**
 * Enhanced file size utilities with compression analysis
 */
const FileUtils = {
	getSize: (filePath) => {
		try {
			const stats = fs.statSync(filePath);
			return stats.size;
		} catch (error) {
			return 0;
		}
	},

	getSizeKB: (filePath) => {
		return (FileUtils.getSize(filePath) / 1024).toFixed(2);
	},

	getGzippedSize: (filePath) => {
		try {
			const command = process.platform === 'win32'
				? `powershell -command "& {[System.IO.File]::ReadAllBytes('${filePath}') | ForEach-Object {$_} | Measure-Object -Sum | Select-Object -ExpandProperty Sum}"`
				: `gzip -c "${filePath}" | wc -c`;
			const size = execSync(command, { encoding: 'utf8' });
			return (parseInt(size.trim()) / 1024).toFixed(2);
		} catch (error) {
			// Fallback: estimate gzip size as ~30% of original
			return (FileUtils.getSize(filePath) * 0.3 / 1024).toFixed(2);
		}
	},

	getBrotliSize: (filePath) => {
		try {
			const command = process.platform === 'win32'
				? `powershell -command "& {[System.IO.File]::ReadAllBytes('${filePath}') | ForEach-Object {$_} | Measure-Object -Sum | Select-Object -ExpandProperty Sum}"`
				: `brotli -c "${filePath}" | wc -c`;
			const size = execSync(command, { encoding: 'utf8' });
			return (parseInt(size.trim()) / 1024).toFixed(2);
		} catch (error) {
			// Fallback: estimate brotli size as ~25% of original
			return (FileUtils.getSize(filePath) * 0.25 / 1024).toFixed(2);
		}
	},

	analyzeCompression: (filePath) => {
		const rawSize = parseFloat(FileUtils.getSizeKB(filePath));
		const gzippedSize = parseFloat(FileUtils.getGzippedSize(filePath));
		const brotliSize = parseFloat(FileUtils.getBrotliSize(filePath));

		return {
			raw: rawSize,
			gzipped: gzippedSize,
			brotli: brotliSize,
			gzipRatio: rawSize > 0 ? ((gzippedSize / rawSize) * 100) : 0,
			brotliRatio: rawSize > 0 ? ((brotliSize / rawSize) * 100) : 0,
			brotliImprovement: gzippedSize > 0 ? (((gzippedSize - brotliSize) / gzippedSize) * 100) : 0,
		};
	},
};

/**
 * Comprehensive bundle analysis with enhanced metrics
 */
function analyzeBundleOptimization() {
	console.log('🚀 Enhanced Bundle Optimization Analysis');
	console.log('='.repeat(100));
	console.log(`Generated: ${new Date().toISOString()}`);
	console.log(`Version: ${getPackageVersion()}`);
	console.log('='.repeat(100));

	const bundleConfig = getBundleConfig();
	const results = [];
	let totalRaw = 0;
	let totalGzipped = 0;
	let totalBrotli = 0;
	let violations = [];
	let warnings = [];

	console.log(`${'Bundle'.padEnd(30)} ${'Category'.padEnd(12)} ${'Raw'.padStart(10)} ${'Gzipped'.padStart(10)} ${'Brotli'.padStart(10)} ${'Compression'.padStart(12)} ${'Status'.padStart(10)}`);
	console.log('='.repeat(100));

	Object.entries(bundleConfig).forEach(([filename, config]) => {
		const filePath = path.join(DIST_DIR, filename);
		const compression = FileUtils.analyzeCompression(filePath);
		const sizeCheck = checkSizeViolation(filename, compression.gzipped);

		totalRaw += compression.raw;
		totalGzipped += compression.gzipped;
		totalBrotli += compression.brotli;

		// Determine status
		let status = '✅ OK';
		if (sizeCheck.violation) {
			status = '❌ FAIL';
			violations.push({ filename, config, ...sizeCheck, compression });
		} else if (sizeCheck.warning) {
			status = '⚠️  WARN';
			warnings.push({ filename, config, ...sizeCheck, compression });
		}

		const compressionStr = `${compression.gzipRatio.toFixed(1)}%/${compression.brotliRatio.toFixed(1)}%`;

		console.log(`${config.name.padEnd(30)} ${config.priority.padEnd(12)} ${compression.raw.toFixed(2).padStart(8)} KB ${compression.gzipped.toFixed(2).padStart(8)} KB ${compression.brotli.toFixed(2).padStart(8)} KB ${compressionStr.padStart(12)} ${status.padStart(10)}`);

		results.push({
			filename,
			config,
			compression,
			sizeCheck,
			status: sizeCheck.violation ? 'fail' : sizeCheck.warning ? 'warn' : 'ok',
		});
	});

	console.log('='.repeat(100));
	console.log(`${'TOTAL'.padEnd(30)} ${'ALL'.padEnd(12)} ${totalRaw.toFixed(2).padStart(8)} KB ${totalGzipped.toFixed(2).padStart(8)} KB ${totalBrotli.toFixed(2).padStart(8)} KB ${((totalGzipped / totalRaw) * 100).toFixed(1).padStart(6)}%/${((totalBrotli / totalRaw) * 100).toFixed(1)}%`);

	// Calculate critical path size
	const criticalPathSize = calculateCriticalPathSize(
		Object.fromEntries(results.map(r => [r.filename, r.compression.gzipped]))
	);
	const criticalPathBudget = PERFORMANCE_BUDGETS.totalSize.critical;

	console.log(`${'Critical Path'.padEnd(30)} ${'CRITICAL'.padEnd(12)} ${'-'.padStart(8)}    ${criticalPathSize.toFixed(2).padStart(8)} KB ${'-'.padStart(8)}    ${criticalPathSize > criticalPathBudget ? '❌ OVER' : '✅ OK'.padStart(6)}  `);
	console.log();

	return {
		results,
		violations,
		warnings,
		totals: {
			rawSize: totalRaw,
			gzippedSize: totalGzipped,
			brotliSize: totalBrotli,
			criticalPathSize,
			criticalPathBudget,
		},
		analysis: {
			compressionRatio: (totalGzipped / totalRaw) * 100,
			brotliImprovement: ((totalGzipped - totalBrotli) / totalGzipped) * 100,
			criticalPathExceeded: criticalPathSize > criticalPathBudget,
		},
	};
}

/**
 * Advanced tree-shaking effectiveness analysis
 */
function analyzeTreeShakingOptimization() {
	console.log('🌳 Advanced Tree-Shaking Optimization Analysis');
	console.log('='.repeat(80));

	const srcDir = path.join(__dirname, '../src');
	const analysis = {
		totalFiles: 0,
		totalExports: 0,
		barrelExports: 0,
		namespaceImports: 0,
		sideEffectFiles: 0,
		dynamicImports: 0,
		issues: [],
		effectiveness: 0,
	};

	function analyzeFile(filePath) {
		try {
			const content = fs.readFileSync(filePath, 'utf8');
			const relativePath = path.relative(srcDir, filePath);

			analysis.totalFiles++;

			// Count exports
			const exportMatches = content.match(/export\s+(?:const|function|class|interface|type|default)/g) || [];
			analysis.totalExports += exportMatches.length;

			// Check for barrel exports
			if (content.includes('export * from') || (content.includes('export {') && content.includes('} from'))) {
				analysis.barrelExports++;
				analysis.issues.push({
					file: relativePath,
					type: 'barrel-export',
					severity: 'warning',
					message: 'Barrel exports may prevent tree-shaking',
					suggestion: 'Use direct imports instead of re-exports',
					impact: 'medium',
				});
			}

			// Check for namespace imports
			if (content.includes('import * as')) {
				analysis.namespaceImports++;
				analysis.issues.push({
					file: relativePath,
					type: 'namespace-import',
					severity: 'warning',
					message: 'Namespace imports prevent tree-shaking',
					suggestion: 'Use named imports: import { specific } from "module"',
					impact: 'high',
				});
			}

			// Check for dynamic imports (positive indicator)
			const dynamicImportMatches = content.match(/import\s*\(/g) || [];
			analysis.dynamicImports += dynamicImportMatches.length;

			// Check for potential side effects
			const sideEffectPatterns = [
				/console\./,
				/window\./,
				/document\./,
				/global\./,
				/process\.env/,
			];

			if (sideEffectPatterns.some(pattern => pattern.test(content))) {
				analysis.sideEffectFiles++;
			}

			// Check for unused exports (simplified heuristic)
			const exportNames = (content.match(/export\s+(?:const|function|class)\s+(\w+)/g) || [])
				.map(match => match.split(/\s+/).pop());

			exportNames.forEach(exportName => {
				// Simple check if export is used elsewhere (this is a basic heuristic)
				const usagePattern = new RegExp(`\\b${exportName}\\b`, 'g');
				const usageCount = (content.match(usagePattern) || []).length;

				if (usageCount <= 1) { // Only the export declaration itself
					analysis.issues.push({
						file: relativePath,
						type: 'unused-export',
						severity: 'info',
						message: `Potentially unused export: ${exportName}`,
						suggestion: 'Remove unused exports to improve tree-shaking',
						impact: 'low',
					});
				}
			});

		} catch (error) {
			// Skip files that can't be read
		}
	}

	function scanDirectory(dir) {
		try {
			const files = fs.readdirSync(dir);

			files.forEach(file => {
				const filePath = path.join(dir, file);
				const stat = fs.statSync(filePath);

				if (stat.isDirectory()) {
					scanDirectory(filePath);
				} else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
					analyzeFile(filePath);
				}
			});
		} catch (error) {
			// Directory might not exist, skip
		}
	}

	scanDirectory(srcDir);

	// Calculate tree-shaking effectiveness
	const penaltyScore = (analysis.barrelExports * 10) + (analysis.namespaceImports * 15);
	const bonusScore = (analysis.dynamicImports * 5);
	analysis.effectiveness = Math.max(0, Math.min(100, 100 - penaltyScore + bonusScore));

	// Report findings
	console.log(`📊 Tree-shaking Analysis Results:`);
	console.log(`   - Total files analyzed: ${analysis.totalFiles}`);
	console.log(`   - Total exports: ${analysis.totalExports}`);
	console.log(`   - Barrel exports: ${analysis.barrelExports} ${analysis.barrelExports > 0 ? '⚠️' : '✅'}`);
	console.log(`   - Namespace imports: ${analysis.namespaceImports} ${analysis.namespaceImports > 0 ? '⚠️' : '✅'}`);
	console.log(`   - Dynamic imports: ${analysis.dynamicImports} ${analysis.dynamicImports > 0 ? '✅' : '⚠️'}`);
	console.log(`   - Files with side effects: ${analysis.sideEffectFiles}`);
	console.log(`   - Tree-shaking effectiveness: ${analysis.effectiveness.toFixed(1)}%`);

	// Effectiveness rating
	let effectivenessRating = '❌ Poor';
	if (analysis.effectiveness >= PERFORMANCE_BUDGETS.treeShaking.excellent) {
		effectivenessRating = '✅ Excellent';
	} else if (analysis.effectiveness >= PERFORMANCE_BUDGETS.treeShaking.target) {
		effectivenessRating = '🟢 Good';
	} else if (analysis.effectiveness >= PERFORMANCE_BUDGETS.treeShaking.minimum) {
		effectivenessRating = '🟡 Fair';
	}

	console.log(`   - Rating: ${effectivenessRating}`);

	if (analysis.issues.length > 0) {
		console.log('\n⚠️  Tree-shaking Issues by Priority:');
		const groupedIssues = analysis.issues.reduce((acc, issue) => {
			const key = `${issue.impact}-${issue.type}`;
			if (!acc[key]) acc[key] = { ...issue, files: [] };
			acc[key].files.push(issue.file);
			return acc;
		}, {});

		Object.values(groupedIssues)
			.sort((a, b) => {
				const impactOrder = { high: 0, medium: 1, low: 2 };
				return impactOrder[a.impact] - impactOrder[b.impact];
			})
			.forEach(issue => {
				console.log(`\n   ${issue.type.replace('-', ' ').toUpperCase()} (${issue.files.length} files, ${issue.impact} impact):`);
				console.log(`     ${issue.message}`);
				console.log(`     💡 ${issue.suggestion}`);
				issue.files.slice(0, 3).forEach(file => {
					console.log(`     - ${file}`);
				});
				if (issue.files.length > 3) {
					console.log(`     ... and ${issue.files.length - 3} more files`);
				}
			});
	} else {
		console.log('\n✅ No major tree-shaking issues detected');
	}

	console.log();
	return analysis;
}

/**
 * Code splitting effectiveness analysis
 */
function analyzeCodeSplittingOptimization() {
	console.log('✂️  Code Splitting Optimization Analysis');
	console.log('='.repeat(80));

	const distFiles = [];

	// Find all built files
	function findDistFiles(dir) {
		try {
			const files = fs.readdirSync(dir);

			files.forEach(file => {
				const filePath = path.join(dir, file);
				const stat = fs.statSync(filePath);

				if (stat.isDirectory()) {
					findDistFiles(filePath);
				} else if (file.endsWith('.js')) {
					const compression = FileUtils.analyzeCompression(filePath);
					distFiles.push({
						path: filePath,
						name: file,
						...compression,
					});
				}
			});
		} catch (error) {
			// Directory might not exist, skip
		}
	}

	findDistFiles(DIST_DIR);

	// Categorize chunks
	const chunkCategories = {
		main: distFiles.filter(f => f.name.includes('index') && !f.name.includes('factories')),
		factoriesLight: distFiles.filter(f => f.name.includes('factories-light')),
		factories: distFiles.filter(f => f.name.includes('factories') && !f.name.includes('light') && !f.name.includes('heavy') && !f.name.includes('dynamic')),
		factoriesHeavy: distFiles.filter(f => f.name.includes('factories-heavy')),
		factoriesDynamic: distFiles.filter(f => f.name.includes('factories-dynamic')),
		components: distFiles.filter(f => ['core', 'elements', 'collections', 'modules', 'base'].some(cat => f.name.includes(cat))),
		utilities: distFiles.filter(f => ['constants', 'hooks', 'utils'].some(util => f.name.includes(util))),
		chunks: distFiles.filter(f => f.name.includes('chunk')),
	};

	console.log('📊 Code Splitting Distribution:');
	let totalSize = 0;
	let factorySize = 0;

	Object.entries(chunkCategories).forEach(([category, files]) => {
		if (files.length > 0) {
			const categorySize = files.reduce((sum, f) => sum + f.gzipped, 0);
			totalSize += categorySize;

			if (category.includes('factories')) {
				factorySize += categorySize;
			}

			console.log(`   ${category.charAt(0).toUpperCase() + category.slice(1)}: ${files.length} files (${categorySize.toFixed(2)} KB gzipped)`);

			if (files.length <= 3) {
				files.forEach(file => {
					console.log(`     - ${file.name}: ${file.gzipped.toFixed(2)} KB (${file.gzipRatio.toFixed(1)}% compression)`);
				});
			} else {
				// Show largest files
				files.sort((a, b) => b.gzipped - a.gzipped).slice(0, 2).forEach(file => {
					console.log(`     - ${file.name}: ${file.gzipped.toFixed(2)} KB (${file.gzipRatio.toFixed(1)}% compression)`);
				});
				console.log(`     ... and ${files.length - 2} more files`);
			}
		}
	});

	// Calculate splitting effectiveness
	const mainSize = chunkCategories.main.reduce((sum, f) => sum + f.gzipped, 0);
	const splittingRatio = totalSize > 0 ? (factorySize / totalSize) * 100 : 0;
	const mainBundleRatio = totalSize > 0 ? (mainSize / totalSize) * 100 : 0;

	console.log('\n📈 Code Splitting Effectiveness:');
	console.log(`   - Total library size: ${totalSize.toFixed(2)} KB`);
	console.log(`   - Main bundle size: ${mainSize.toFixed(2)} KB (${mainBundleRatio.toFixed(1)}%)`);
	console.log(`   - Factory components: ${factorySize.toFixed(2)} KB (${splittingRatio.toFixed(1)}%)`);
	console.log(`   - Splitting effectiveness: ${splittingRatio > 40 ? '✅ Good' : splittingRatio > 25 ? '🟡 Fair' : '❌ Poor'}`);

	// Recommendations
	const recommendations = [];

	if (mainSize > 100) {
		recommendations.push('Main bundle is large - consider moving more components to separate chunks');
	}

	if (splittingRatio < 25) {
		recommendations.push('Low code splitting ratio - move more components to factory bundles');
	}

	if (chunkCategories.chunks.length === 0) {
		recommendations.push('No dynamic chunks detected - implement lazy loading for better performance');
	}

	if (factorySize > 300) {
		recommendations.push('Factory components are heavy - ensure they are loaded on demand only');
	}

	if (recommendations.length > 0) {
		console.log('\n💡 Code Splitting Recommendations:');
		recommendations.forEach(rec => {
			console.log(`   - ${rec}`);
		});
	} else {
		console.log('\n✅ Code splitting configuration looks optimal');
	}

	console.log();
	return {
		chunkCategories,
		totalSize,
		mainSize,
		factorySize,
		splittingRatio,
		mainBundleRatio,
		recommendations,
	};
}

/**
 * Generate comprehensive optimization report
 */
function generateOptimizationReport(bundleAnalysis, treeShakingAnalysis, codeSplittingAnalysis) {
	console.log('📄 Comprehensive Optimization Report');
	console.log('='.repeat(80));

	const report = {
		timestamp: new Date().toISOString(),
		version: getPackageVersion(),
		summary: {
			totalBundles: bundleAnalysis.results.length,
			failedBundles: bundleAnalysis.violations.length,
			warningBundles: bundleAnalysis.warnings.length,
			totalSize: bundleAnalysis.totals,
			treeShakingEffectiveness: treeShakingAnalysis.effectiveness,
			codeSplittingRatio: codeSplittingAnalysis.splittingRatio,
		},
		bundleAnalysis,
		treeShakingAnalysis,
		codeSplittingAnalysis,
		recommendations: [],
		performance: {
			compressionRatio: bundleAnalysis.analysis.compressionRatio,
			brotliImprovement: bundleAnalysis.analysis.brotliImprovement,
			criticalPathOptimal: !bundleAnalysis.analysis.criticalPathExceeded,
		},
		buildInfo: {
			nodeVersion: process.version,
			platform: process.platform,
			arch: process.arch,
			environment: process.env.NODE_ENV || 'development',
		},
	};

	// Generate comprehensive recommendations
	report.recommendations = generateSizeRecommendations(
		bundleAnalysis.violations,
		bundleAnalysis.warnings,
		{
			...bundleAnalysis.analysis,
			treeShakingEffectiveness: treeShakingAnalysis.effectiveness,
		}
	);

	// Add tree-shaking recommendations
	if (treeShakingAnalysis.effectiveness < PERFORMANCE_BUDGETS.treeShaking.target) {
		report.recommendations.push({
			priority: 'medium',
			category: 'Tree-shaking',
			title: 'Tree-shaking Optimization Opportunity',
			description: `Current effectiveness: ${treeShakingAnalysis.effectiveness.toFixed(1)}% (target: ${PERFORMANCE_BUDGETS.treeShaking.target}%)`,
			impact: 'Medium - Reduces bundle size through dead code elimination',
			actions: [
				'Replace barrel exports with direct imports',
				'Use named imports instead of namespace imports',
				'Implement more dynamic imports for code splitting',
				'Remove unused exports and dependencies',
			],
		});
	}

	// Add code splitting recommendations
	if (codeSplittingAnalysis.splittingRatio < 40) {
		report.recommendations.push({
			priority: 'medium',
			category: 'Code Splitting',
			title: 'Code Splitting Optimization',
			description: `Current splitting ratio: ${codeSplittingAnalysis.splittingRatio.toFixed(1)}% (target: >40%)`,
			impact: 'Medium - Improves initial load performance',
			actions: [
				'Move more components to factory bundles',
				'Implement lazy loading for heavy components',
				'Use dynamic imports for conditional features',
				'Optimize chunk splitting configuration',
			],
		});
	}

	// Save comprehensive report
	const reportPath = path.join(REPORTS_DIR, `optimization-report-${Date.now()}.json`);
	fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

	// Save latest report
	const latestReportPath = path.join(REPORTS_DIR, 'optimization-report-latest.json');
	fs.writeFileSync(latestReportPath, JSON.stringify(report, null, 2));

	console.log(`📄 Optimization report saved: ${reportPath}`);
	console.log(`📄 Latest report updated: ${latestReportPath}`);
	console.log();

	return report;
}

/**
 * Display final recommendations
 */
function displayOptimizationRecommendations(report) {
	console.log('💡 Final Optimization Recommendations');
	console.log('='.repeat(80));

	if (report.recommendations.length === 0) {
		console.log('✅ No major optimization issues detected!');
		console.log('   Your bundle configuration appears to be well optimized.');
		console.log();
		return;
	}

	report.recommendations
		.sort((a, b) => {
			const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
			return priorityOrder[a.priority] - priorityOrder[b.priority];
		})
		.forEach((rec, index) => {
			const priorityIcon = {
				critical: '🔴',
				high: '🟠',
				medium: '🟡',
				low: '🟢',
			}[rec.priority];

			console.log(`\n${index + 1}. ${priorityIcon} ${rec.title} (${rec.category})`);
			console.log(`   ${rec.description}`);
			if (rec.impact) {
				console.log(`   Impact: ${rec.impact}`);
			}
			console.log('   Actions:');
			rec.actions.forEach(action => {
				console.log(`     - ${action}`);
			});
		});

	console.log();
}

/**
 * Get package version
 */
function getPackageVersion() {
	try {
		const packageJson = JSON.parse(fs.readFileSync(PACKAGE_JSON, 'utf8'));
		return packageJson.version;
	} catch (error) {
		return 'unknown';
	}
}

/**
 * Main execution
 */
function main() {
	const args = process.argv.slice(2);

	console.log('🚀 Miss UI Web - Enhanced Bundle Optimization');
	console.log(`Version: ${getPackageVersion()}`);
	console.log(`Node: ${process.version} | Platform: ${process.platform}`);
	console.log();

	// Run comprehensive optimization analysis
	const bundleAnalysis = analyzeBundleOptimization();
	const treeShakingAnalysis = analyzeTreeShakingOptimization();
	const codeSplittingAnalysis = analyzeCodeSplittingOptimization();

	// Generate comprehensive report
	const report = generateOptimizationReport(bundleAnalysis, treeShakingAnalysis, codeSplittingAnalysis);

	// Display final recommendations
	displayOptimizationRecommendations(report);

	// Exit with appropriate code based on CI configuration
	const hasFailures = bundleAnalysis.violations.length > 0;
	const hasCriticalIssues = report.recommendations.some(r => r.priority === 'critical');

	if (hasFailures && CI_CONFIG.failOn.sizeViolations) {
		console.log('❌ Bundle optimization failed due to size violations');
		process.exit(1);
	} else if (hasCriticalIssues && CI_CONFIG.failOn.criticalPathViolations) {
		console.log('❌ Bundle optimization failed due to critical path issues');
		process.exit(1);
	} else if (bundleAnalysis.warnings.length > 0) {
		console.log('⚠️  Bundle optimization completed with warnings');
		process.exit(0);
	} else {
		console.log('✅ Bundle optimization analysis completed successfully');
		process.exit(0);
	}
}

// Run if called directly
if (require.main === module) {
	main();
}

module.exports = {
	analyzeBundleOptimization,
	analyzeTreeShakingOptimization,
	analyzeCodeSplittingOptimization,
	generateOptimizationReport,
	displayOptimizationRecommendations,
};
