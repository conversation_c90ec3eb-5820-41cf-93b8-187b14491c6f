#!/usr/bin/env node

/**
 * Comprehensive Build Optimization Script
 * Orchestrates all build optimization tasks for optimal bundle output
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const REPORTS_DIR = path.join(__dirname, '../reports');

// Ensure reports directory exists
if (!fs.existsSync(REPORTS_DIR)) {
	fs.mkdirSync(REPORTS_DIR, { recursive: true });
}

/**
 * Execute command with proper error handling
 */
function executeCommand(command, description) {
	console.log(`\n🔄 ${description}...`);
	try {
		const output = execSync(command, {
			encoding: 'utf8',
			cwd: path.join(__dirname, '..'),
		});
		console.log(`✅ ${description} completed`);
		return output;
	} catch (error) {
		console.error(`❌ ${description} failed:`, error.message);
		return null;
	}
}

/**
 * Main optimization workflow
 */
async function main() {
	console.log('🚀 Miss UI Web - Comprehensive Build Optimization');
	console.log('='.repeat(80));
	console.log(`Started: ${new Date().toISOString()}`);
	console.log('='.repeat(80));

	const args = process.argv.slice(2);
	const skipBuild = args.includes('--skip-build');
	const analyzeOnly = args.includes('--analyze-only');

	// Step 1: Clean previous build
	if (!skipBuild) {
		executeCommand('pnpm clean', 'Cleaning previous build');
	}

	// Step 2: Build with different configurations
	if (!skipBuild && !analyzeOnly) {
		// Try regular build first
		const buildResult = executeCommand('pnpm build', 'Building library');

		if (!buildResult) {
			console.log('\n⚠️  Regular build failed, trying CSS-optimized build...');
			executeCommand('pnpm build:css-optimize', 'Building with CSS optimization');
		}
	}

	// Step 3: Run CSS optimization if dist exists
	const distExists = fs.existsSync(path.join(__dirname, '../dist'));
	if (distExists) {
		executeCommand('node scripts/css-optimizer.cjs', 'Optimizing CSS output');
	}

	// Step 4: Bundle analysis and monitoring
	executeCommand('node scripts/bundle-monitor.cjs', 'Monitoring bundle sizes');
	executeCommand('node scripts/bundle-optimize-enhanced.cjs', 'Running enhanced bundle optimization');

	// Step 5: Generate comprehensive report
	console.log('\n📊 Generating Comprehensive Optimization Report');
	console.log('='.repeat(80));

	const reportData = {
		timestamp: new Date().toISOString(),
		buildStatus: distExists ? 'success' : 'failed',
		optimizations: {
			viteTreeShaking: 'configured',
			cssOptimization: 'configured',
			codeSplitting: 'configured',
			bundleAnalysis: 'configured',
		},
		configurations: {
			mainConfig: 'vite.config.ts',
			analyzeConfig: 'vite.config.analyze.ts',
			chunksConfig: 'vite.config.chunks.ts',
			cssOptimizeConfig: 'vite.css-optimize.config.ts',
		},
		scripts: {
			bundleMonitor: 'scripts/bundle-monitor.cjs',
			bundleOptimize: 'scripts/bundle-optimize-enhanced.cjs',
			bundleSizeConfig: 'scripts/bundle-size-config.cjs',
			cssOptimizer: 'scripts/css-optimizer.cjs',
		},
		recommendations: [
			'Use pnpm build:analyze for detailed bundle analysis',
			'Use pnpm build:chunks for optimal code splitting',
			'Use pnpm build:css-optimize for CSS optimization',
			'Monitor bundle sizes with pnpm bundle:monitor',
			'Run comprehensive analysis with pnpm bundle:optimize-enhanced',
		],
	};

	// Save optimization report
	const reportPath = path.join(REPORTS_DIR, `build-optimization-${Date.now()}.json`);
	fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));

	const latestReportPath = path.join(REPORTS_DIR, 'build-optimization-latest.json');
	fs.writeFileSync(latestReportPath, JSON.stringify(reportData, null, 2));

	console.log(`📄 Build optimization report saved: ${reportPath}`);
	console.log(`📄 Latest report updated: ${latestReportPath}`);

	// Step 6: Summary
	console.log('\n📋 Build Optimization Summary');
	console.log('='.repeat(80));
	console.log(`Build Status: ${distExists ? '✅ Success' : '❌ Failed'}`);
	console.log('Optimizations Applied:');
	console.log('  ✅ Vite tree-shaking configuration');
	console.log('  ✅ CSS optimization with Vanilla Extract');
	console.log('  ✅ Code splitting for Factory components');
	console.log('  ✅ Bundle analysis and monitoring');
	console.log('  ✅ Comprehensive reporting');

	console.log('\nAvailable Commands:');
	console.log('  pnpm build:analyze     - Build with detailed analysis');
	console.log('  pnpm build:chunks      - Build with optimal chunking');
	console.log('  pnpm build:css-optimize - Build with CSS optimization');
	console.log('  pnpm bundle:monitor    - Monitor bundle sizes');
	console.log('  pnpm bundle:optimize-enhanced - Enhanced optimization analysis');

	console.log('\nNext Steps:');
	if (!distExists) {
		console.log('  1. Fix build issues to enable full optimization');
		console.log('  2. Check component export patterns');
		console.log('  3. Verify import/export consistency');
	} else {
		console.log('  1. Review bundle analysis reports');
		console.log('  2. Implement recommended optimizations');
		console.log('  3. Monitor bundle size changes over time');
	}

	console.log('\n🎉 Build optimization setup completed!');
	console.log('='.repeat(80));
}

// Run if called directly
if (require.main === module) {
	main().catch(error => {
		console.error('❌ Build optimization failed:', error);
		process.exit(1);
	});
}

module.exports = { main };
