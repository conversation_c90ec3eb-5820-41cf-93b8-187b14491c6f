#!/usr/bin/env node

/**
 * Bundle Size Configuration and Monitoring Setup
 * Centralized configuration for bundle size limits, monitoring, and CI/CD integration
 */

const path = require('path');

/**
 * Enhanced bundle size configuration with detailed categorization
 */
const BUNDLE_SIZE_CONFIG = {
	// Critical path bundles - loaded immediately
	critical: {
		'index.es.js': {
			name: 'Main Bundle',
			maxSize: 120,
			warningSize: 100,
			description: 'Main library entry point with core components',
			priority: 'critical',
			loadTiming: 'immediate',
			cacheStrategy: 'aggressive',
		},
		'core/index.es.js': {
			name: 'Core Components',
			maxSize: 40,
			warningSize: 30,
			description: 'Document structure and basic layout components',
			priority: 'critical',
			loadTiming: 'immediate',
			cacheStrategy: 'aggressive',
		},
		'elements/index.es.js': {
			name: 'Element Components',
			maxSize: 60,
			warningSize: 45,
			description: 'Interactive and display elements',
			priority: 'critical',
			loadTiming: 'immediate',
			cacheStrategy: 'aggressive',
		},
		'constants.es.js': {
			name: 'Constants',
			maxSize: 15,
			warningSize: 10,
			description: 'Theme tokens and design constants',
			priority: 'critical',
			loadTiming: 'immediate',
			cacheStrategy: 'aggressive',
		},
		'hooks.es.js': {
			name: 'Hooks',
			maxSize: 10,
			warningSize: 8,
			description: 'React hooks and utilities',
			priority: 'critical',
			loadTiming: 'immediate',
			cacheStrategy: 'aggressive',
		},
		'utils.es.js': {
			name: 'Utils',
			maxSize: 20,
			warningSize: 15,
			description: 'Utility functions and helpers',
			priority: 'critical',
			loadTiming: 'immediate',
			cacheStrategy: 'aggressive',
		},
	},

	// Standard bundles - loaded on demand
	standard: {
		'collections/index.es.js': {
			name: 'Collection Components',
			maxSize: 35,
			warningSize: 25,
			description: 'Grouped content and status indicators',
			priority: 'standard',
			loadTiming: 'on-demand',
			cacheStrategy: 'standard',
		},
		'modules/index.es.js': {
			name: 'Module Components',
			maxSize: 50,
			warningSize: 40,
			description: 'Complex interactive patterns',
			priority: 'standard',
			loadTiming: 'on-demand',
			cacheStrategy: 'standard',
		},
		'base/index.es.js': {
			name: 'Base Components',
			maxSize: 25,
			warningSize: 20,
			description: 'Low-level building blocks',
			priority: 'standard',
			loadTiming: 'on-demand',
			cacheStrategy: 'standard',
		},
	},

	// Factory bundles - heavy components with external dependencies
	factories: {
		'factories-light.es.js': {
			name: 'Light Factories',
			maxSize: 60,
			warningSize: 45,
			description: 'Lightweight factory components (Layout, Toast)',
			priority: 'factory',
			loadTiming: 'lazy',
			cacheStrategy: 'long-term',
			dependencies: ['minimal'],
		},
		'factories.es.js': {
			name: 'Standard Factories',
			maxSize: 80,
			warningSize: 60,
			description: 'Standard factory components with moderate dependencies',
			priority: 'factory',
			loadTiming: 'lazy',
			cacheStrategy: 'long-term',
			dependencies: ['moderate'],
		},
		'factories-heavy.es.js': {
			name: 'Heavy Factories',
			maxSize: 250,
			warningSize: 200,
			description: 'Heavy factory components (Editor, Player) with large dependencies',
			priority: 'factory',
			loadTiming: 'lazy',
			cacheStrategy: 'long-term',
			dependencies: ['@lexical/*', '@vidstack/react'],
		},
	},
};

/**
 * Performance budgets and thresholds
 */
const PERFORMANCE_BUDGETS = {
	// Total bundle size budgets
	totalSize: {
		critical: 200, // KB - Critical path budget
		standard: 150, // KB - Standard components budget
		factories: 400, // KB - Factory components budget
		overall: 750, // KB - Total library budget
	},

	// Compression ratio targets
	compression: {
		minimum: 25, // % - Minimum acceptable compression
		target: 35, // % - Target compression ratio
		excellent: 45, // % - Excellent compression ratio
	},

	// Loading performance targets
	loadTime: {
		critical: 100, // ms - Critical path load time
		standard: 200, // ms - Standard components load time
		factories: 500, // ms - Factory components load time
	},

	// Tree-shaking effectiveness targets
	treeShaking: {
		minimum: 70, // % - Minimum tree-shaking effectiveness
		target: 85, // % - Target tree-shaking effectiveness
		excellent: 95, // % - Excellent tree-shaking effectiveness
	},
};

/**
 * CI/CD integration settings
 */
const CI_CONFIG = {
	// Failure conditions
	failOn: {
		sizeViolations: true, // Fail if any bundle exceeds max size
		criticalPathViolations: true, // Fail if critical path exceeds budget
		regressions: true, // Fail on significant size regressions
		treeShakingIssues: false, // Warning only for tree-shaking issues
	},

	// Regression detection
	regression: {
		threshold: 10, // % - Size increase threshold for regression detection
		criticalThreshold: 5, // % - Critical path regression threshold
		compareWith: 'previous', // 'previous' | 'baseline' | 'main'
		maxHistory: 10, // Number of builds to keep for comparison
	},

	// Reporting
	reporting: {
		format: 'json', // 'json' | 'junit' | 'markdown'
		includeRecommendations: true,
		includeTreeShaking: true,
		includeComposition: true,
		generateBadges: true,
	},

	// Notifications
	notifications: {
		slack: {
			enabled: false,
			webhook: process.env.SLACK_WEBHOOK_URL,
			channel: '#build-notifications',
		},
		github: {
			enabled: true,
			token: process.env.GITHUB_TOKEN,
			prComments: true,
			statusChecks: true,
		},
	},
};

/**
 * Monitoring configuration
 */
const MONITORING_CONFIG = {
	// Continuous monitoring settings
	continuous: {
		enabled: false, // Enable continuous monitoring
		interval: 300000, // 5 minutes
		alertThreshold: 15, // % size increase for alerts
		historyRetention: 30, // days
	},

	// Metrics collection
	metrics: {
		collectBuildTime: true,
		collectCompressionRatio: true,
		collectTreeShakingEffectiveness: true,
		collectDependencyAnalysis: true,
		collectPerformanceProfile: true,
	},

	// Storage configuration
	storage: {
		type: 'filesystem', // 'filesystem' | 'database' | 's3'
		path: './reports',
		retention: 90, // days
		compression: true,
	},
};

/**
 * Get bundle configuration by category
 */
function getBundleConfig(category = 'all') {
	if (category === 'all') {
		return {
			...BUNDLE_SIZE_CONFIG.critical,
			...BUNDLE_SIZE_CONFIG.standard,
			...BUNDLE_SIZE_CONFIG.factories,
		};
	}
	return BUNDLE_SIZE_CONFIG[category] || {};
}

/**
 * Get performance budget for category
 */
function getPerformanceBudget(category) {
	return PERFORMANCE_BUDGETS.totalSize[category] || PERFORMANCE_BUDGETS.totalSize.overall;
}

/**
 * Check if bundle violates size limits
 */
function checkSizeViolation(bundleName, actualSize) {
	const allConfigs = getBundleConfig();
	const config = allConfigs[bundleName];

	if (!config) {
		return { violation: false, warning: false };
	}

	return {
		violation: actualSize > config.maxSize,
		warning: actualSize > config.warningSize && actualSize <= config.maxSize,
		config,
		actualSize,
		excess: Math.max(0, actualSize - config.maxSize),
		remaining: Math.max(0, config.maxSize - actualSize),
	};
}

/**
 * Calculate critical path size
 */
function calculateCriticalPathSize(bundleSizes) {
	const criticalBundles = Object.keys(BUNDLE_SIZE_CONFIG.critical);
	return criticalBundles.reduce((total, bundleName) => {
		return total + (bundleSizes[bundleName] || 0);
	}, 0);
}

/**
 * Generate size recommendations
 */
function generateSizeRecommendations(violations, warnings, budgetAnalysis) {
	const recommendations = [];

	// Critical violations
	if (violations.length > 0) {
		const criticalViolations = violations.filter(v => v.config.priority === 'critical');
		if (criticalViolations.length > 0) {
			recommendations.push({
				priority: 'critical',
				category: 'Bundle Size',
				title: 'Critical Path Size Violations',
				description: `${criticalViolations.length} critical bundles exceed size limits`,
				impact: 'High - Affects initial page load performance',
				actions: [
					'Move non-essential components out of critical path',
					'Implement code splitting for large features',
					'Optimize imports and remove unused code',
					'Use dynamic imports for conditional features',
				],
				bundles: criticalViolations.map(v => ({
					name: v.config.name,
					excess: v.excess,
					recommendation: `Reduce by ${v.excess.toFixed(2)} KB`,
				})),
			});
		}
	}

	// Budget violations
	if (budgetAnalysis.criticalPathExceeded) {
		recommendations.push({
			priority: 'high',
			category: 'Performance Budget',
			title: 'Critical Path Budget Exceeded',
			description: `Critical path size: ${budgetAnalysis.criticalPathSize.toFixed(2)} KB (budget: ${budgetAnalysis.criticalPathBudget} KB)`,
			impact: 'High - Affects Time to Interactive (TTI)',
			actions: [
				'Defer non-critical components to separate chunks',
				'Implement progressive loading strategy',
				'Optimize critical path dependencies',
				'Use service worker for aggressive caching',
			],
		});
	}

	// Tree-shaking opportunities
	if (budgetAnalysis.treeShakingEffectiveness < PERFORMANCE_BUDGETS.treeShaking.target) {
		recommendations.push({
			priority: 'medium',
			category: 'Tree-shaking',
			title: 'Tree-shaking Optimization Opportunity',
			description: `Current effectiveness: ${budgetAnalysis.treeShakingEffectiveness.toFixed(1)}% (target: ${PERFORMANCE_BUDGETS.treeShaking.target}%)`,
			impact: 'Medium - Reduces bundle size through dead code elimination',
			actions: [
				'Replace barrel exports with direct imports',
				'Use named imports instead of namespace imports',
				'Mark side-effect modules appropriately',
				'Optimize import/export patterns',
			],
		});
	}

	return recommendations;
}

/**
 * Export configuration
 */
module.exports = {
	BUNDLE_SIZE_CONFIG,
	PERFORMANCE_BUDGETS,
	CI_CONFIG,
	MONITORING_CONFIG,
	getBundleConfig,
	getPerformanceBudget,
	checkSizeViolation,
	calculateCriticalPathSize,
	generateSizeRecommendations,
};
