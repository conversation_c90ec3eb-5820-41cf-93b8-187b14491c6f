#!/usr/bin/env node

/**
 * Enhanced Bundle Size Monitoring Script with Real-time Alerts
 * Provides comprehensive bundle analysis, performance budgets, and automated alerts
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const chokidar = require('chokidar');

const DIST_DIR = path.join(__dirname, '../dist');
const REPORTS_DIR = path.join(__dirname, '../reports');
const PACKAGE_JSON = path.join(__dirname, '../package.json');

// Ensure reports directory exists
if (!fs.existsSync(REPORTS_DIR)) {
	fs.mkdirSync(REPORTS_DIR, { recursive: true });
}

/**
 * Enhanced performance budgets with detailed configuration
 */
const PERFORMANCE_BUDGETS = {
	'index.es.js': {
		max: 120, // KB (gzipped)
		warning: 100,
		critical: true,
		description: 'Main library bundle - critical for initial load',
		loadTime: 2000, // ms target for 3G
		priority: 'high'
	},
	'factories-light.es.js': {
		max: 60,
		warning: 45,
		critical: false,
		description: 'Light factory components',
		loadTime: 1000,
		priority: 'medium'
	},
	'factories.es.js': {
		max: 80,
		warning: 60,
		critical: false,
		description: 'Standard factory components',
		loadTime: 1500,
		priority: 'medium'
	},
	'factories-heavy.es.js': {
		max: 250,
		warning: 200,
		critical: false,
		description: 'Heavy factory components (lazy loaded)',
		loadTime: 5000,
		priority: 'low'
	},
	'core/index.es.js': {
		max: 40,
		warning: 30,
		critical: true,
		description: 'Core components - essential functionality',
		loadTime: 800,
		priority: 'high'
	},
	'elements/index.es.js': {
		max: 60,
		warning: 45,
		critical: true,
		description: 'Element components - commonly used',
		loadTime: 1200,
		priority: 'high'
	},
	'collections/index.es.js': {
		max: 35,
		warning: 25,
		critical: false,
		description: 'Collection components',
		loadTime: 700,
		priority: 'medium'
	},
	'modules/index.es.js': {
		max: 50,
		warning: 40,
		critical: false,
		description: 'Module components',
		loadTime: 1000,
		priority: 'medium'
	},
	'base/index.es.js': {
		max: 25,
		warning: 20,
		critical: false,
		description: 'Base utilities and primitives',
		loadTime: 500,
		priority: 'low'
	},
	'constants.es.js': {
		max: 15,
		warning: 10,
		critical: true,
		description: 'Theme constants and tokens',
		loadTime: 300,
		priority: 'high'
	},
	'hooks.es.js': {
		max: 10,
		warning: 8,
		critical: true,
		description: 'React hooks utilities',
		loadTime: 200,
		priority: 'high'
	},
	'utils.es.js': {
		max: 20,
		warning: 15,
		critical: true,
		description: 'Utility functions',
		loadTime: 400,
		priority: 'high'
	},
};

/**
 * Network speed configurations for load time estimation
 */
const NETWORK_SPEEDS = {
	'3G': 1.6, // Mbps
	'4G': 10,  // Mbps
	'WiFi': 50, // Mbps
	'Cable': 100, // Mbps
};

/**
 * Get file size in bytes
 */
function getFileSizeBytes(filePath) {
	try {
		const stats = fs.statSync(filePath);
		return stats.size;
	} catch (error) {
		return 0;
	}
}

/**
 * Get file size in KB
 */
function getFileSize(filePath) {
	return (getFileSizeBytes(filePath) / 1024).toFixed(2);
}

/**
 * Get gzipped file size with better cross-platform support
 */
function getGzippedSize(filePath) {
	try {
		if (!fs.existsSync(filePath)) {
			return '0.00';
		}

		// Use Node.js zlib for consistent cross-platform compression
		const zlib = require('zlib');
		const fileContent = fs.readFileSync(filePath);
		const compressed = zlib.gzipSync(fileContent);
		return (compressed.length / 1024).toFixed(2);
	} catch (error) {
		console.warn(`Warning: Could not compress ${filePath}:`, error.message);
		// Fallback: estimate gzip size as ~30% of original
		return (getFileSizeBytes(filePath) * 0.3 / 1024).toFixed(2);
	}
}

/**
 * Get Brotli compressed size
 */
function getBrotliSize(filePath) {
	try {
		if (!fs.existsSync(filePath)) {
			return '0.00';
		}

		// Use Node.js zlib for Brotli compression if available
		const zlib = require('zlib');
		if (zlib.brotliCompressSync) {
			const fileContent = fs.readFileSync(filePath);
			const compressed = zlib.brotliCompressSync(fileContent);
			return (compressed.length / 1024).toFixed(2);
		}
	} catch (error) {
		console.warn(`Warning: Could not Brotli compress ${filePath}:`, error.message);
	}

	// Fallback: estimate brotli size as ~25% of original
	return (getFileSizeBytes(filePath) * 0.25 / 1024).toFixed(2);
}

/**
 * Calculate load time for different network speeds
 */
function calculateLoadTimes(sizeKB) {
	const loadTimes = {};

	Object.entries(NETWORK_SPEEDS).forEach(([network, speedMbps]) => {
		// Convert KB to Mb and calculate time in seconds
		const sizeMb = sizeKB * 8 / 1024;
		const timeSeconds = sizeMb / speedMbps;
		loadTimes[network] = Math.round(timeSeconds * 1000); // Convert to ms
	});

	return loadTimes;
}

/**
 * Calculate performance score (0-100)
 */
function calculatePerformanceScore(bundle, budget) {
	let score = 100;

	// Size penalty
	if (bundle.gzippedSize > budget.max) {
		score -= 40; // Major penalty for exceeding limit
	} else if (bundle.gzippedSize > budget.warning) {
		const warningRatio = (bundle.gzippedSize - budget.warning) / (budget.max - budget.warning);
		score -= warningRatio * 20; // Graduated penalty
	}

	// Compression efficiency bonus/penalty
	const compressionRatio = bundle.gzippedSize / bundle.rawSize;
	if (compressionRatio < 0.3) {
		score += 10; // Good compression
	} else if (compressionRatio > 0.5) {
		score -= 10; // Poor compression
	}

	// Load time penalty
	const loadTime3G = bundle.loadTimes['3G'];
	if (loadTime3G > budget.loadTime) {
		const timeRatio = loadTime3G / budget.loadTime;
		score -= (timeRatio - 1) * 20;
	}

	return Math.max(0, Math.min(100, Math.round(score)));
}

/**
 * Enhanced bundle monitoring with performance analysis
 */
function monitorBundleSizesEnhanced() {
	console.log('🚀 Enhanced Bundle Size Monitoring Report');
	console.log('='.repeat(100));
	console.log(`Generated: ${new Date().toISOString()}`);
	console.log('='.repeat(100));

	const results = [];
	let totalSize = 0;
	let totalGzipped = 0;
	let totalBrotli = 0;
	let criticalPathSize = 0;
	let violations = [];
	let warnings = [];
	let performanceIssues = [];

	console.log(`${'Bundle'.padEnd(35)} ${'Raw'.padStart(8)} ${'Gzip'.padStart(8)} ${'Brotli'.padStart(8)} ${'3G Load'.padStart(8)} ${'Score'.padStart(6)} ${'Status'.padStart(8)}`);
	console.log('='.repeat(100));

	Object.entries(PERFORMANCE_BUDGETS).forEach(([filename, budget]) => {
		const filePath = path.join(DIST_DIR, filename);
		const rawSize = parseFloat(getFileSize(filePath));
		const gzippedSize = parseFloat(getGzippedSize(filePath));
		const brotliSize = parseFloat(getBrotliSize(filePath));
		const loadTimes = calculateLoadTimes(gzippedSize);

		totalSize += rawSize;
		totalGzipped += gzippedSize;
		totalBrotli += brotliSize;

		if (budget.critical) {
			criticalPathSize += gzippedSize;
		}

		const bundle = {
			filename,
			rawSize,
			gzippedSize,
			brotliSize,
			loadTimes,
			budget,
		};

		const performanceScore = calculatePerformanceScore(bundle, budget);
		bundle.performanceScore = performanceScore;

		let status = '✅ OK';
		let statusColor = '\x1b[32m'; // Green

		if (gzippedSize > budget.max) {
			status = '❌ FAIL';
			statusColor = '\x1b[31m'; // Red
			violations.push({
				file: filename,
				size: gzippedSize,
				limit: budget.max,
				excess: gzippedSize - budget.max,
				critical: budget.critical,
				description: budget.description,
				loadTime: loadTimes['3G'],
				targetLoadTime: budget.loadTime,
			});
		} else if (gzippedSize > budget.warning) {
			status = '⚠️  WARN';
			statusColor = '\x1b[33m'; // Yellow
			warnings.push({
				file: filename,
				size: gzippedSize,
				warning: budget.warning,
				limit: budget.max,
				remaining: budget.max - gzippedSize,
				description: budget.description,
			});
		}

		// Performance issues
		if (performanceScore < 70) {
			performanceIssues.push({
				file: filename,
				score: performanceScore,
				issues: getPerformanceIssues(bundle, budget),
			});
		}

		const loadTime3G = `${loadTimes['3G']}ms`;
		console.log(`${filename.padEnd(35)} ${rawSize.toFixed(1).padStart(6)} KB ${gzippedSize.toFixed(1).padStart(6)} KB ${brotliSize.toFixed(1).padStart(6)} KB ${loadTime3G.padStart(8)} ${performanceScore.toString().padStart(6)} ${statusColor}${status.padStart(8)}\x1b[0m`);

		results.push(bundle);
	});

	console.log('='.repeat(100));
	console.log(`${'TOTALS'.padEnd(35)} ${totalSize.toFixed(1).padStart(6)} KB ${totalGzipped.toFixed(1).padStart(6)} KB ${totalBrotli.toFixed(1).padStart(6)} KB ${calculateLoadTimes(totalGzipped)['3G'].toString().padStart(6)}ms`);
	console.log(`${'Critical Path'.padEnd(35)} ${'-'.padStart(6)}    ${criticalPathSize.toFixed(1).padStart(6)} KB ${'-'.padStart(6)}    ${calculateLoadTimes(criticalPathSize)['3G'].toString().padStart(6)}ms`);
	console.log();

	// Enhanced reporting
	reportViolations(violations);
	reportWarnings(warnings);
	reportPerformanceIssues(performanceIssues);
	reportNetworkAnalysis(totalGzipped, criticalPathSize);
	reportCompressionAnalysis(totalSize, totalGzipped, totalBrotli);
	reportCodeSplittingAnalysis(results);

	return {
		results,
		violations,
		warnings,
		performanceIssues,
		totals: {
			rawSize: totalSize,
			gzippedSize: totalGzipped,
			brotliSize: totalBrotli,
			criticalPathSize,
		},
		analysis: {
			compressionRatio: (totalGzipped / totalSize) * 100,
			brotliImprovement: ((totalGzipped - totalBrotli) / totalGzipped) * 100,
			averagePerformanceScore: results.reduce((sum, r) => sum + r.performanceScore, 0) / results.length,
		},
	};
}

/**
 * Get specific performance issues for a bundle
 */
function getPerformanceIssues(bundle, budget) {
	const issues = [];

	if (bundle.gzippedSize > budget.max) {
		issues.push(`Size exceeds limit by ${(bundle.gzippedSize - budget.max).toFixed(1)} KB`);
	}

	if (bundle.loadTimes['3G'] > budget.loadTime) {
		issues.push(`3G load time (${bundle.loadTimes['3G']}ms) exceeds target (${budget.loadTime}ms)`);
	}

	const compressionRatio = bundle.gzippedSize / bundle.rawSize;
	if (compressionRatio > 0.5) {
		issues.push(`Poor compression ratio (${(compressionRatio * 100).toFixed(1)}%)`);
	}

	return issues;
}

/**
 * Report violations with enhanced details
 */
function reportViolations(violations) {
	if (violations.length === 0) return;

	console.log('❌ BUNDLE SIZE VIOLATIONS:');
	console.log('-'.repeat(80));

	violations.forEach(v => {
		const criticalNote = v.critical ? ' 🚨 CRITICAL PATH' : '';
		const loadTimeNote = v.loadTime > v.targetLoadTime ? ` (Load time: ${v.loadTime}ms > ${v.targetLoadTime}ms target)` : '';

		console.log(`   📦 ${v.file}${criticalNote}`);
		console.log(`      Size: ${v.size.toFixed(1)} KB (${v.excess.toFixed(1)} KB over ${v.limit} KB limit)`);
		console.log(`      Description: ${v.description}`);
		console.log(`      Impact: ${loadTimeNote || 'Within load time target'}`);
		console.log();
	});
}

/**
 * Report warnings with enhanced details
 */
function reportWarnings(warnings) {
	if (warnings.length === 0) return;

	console.log('⚠️  BUNDLE SIZE WARNINGS:');
	console.log('-'.repeat(80));

	warnings.forEach(w => {
		console.log(`   📦 ${w.file}`);
		console.log(`      Size: ${w.size.toFixed(1)} KB (${w.remaining.toFixed(1)} KB remaining before limit)`);
		console.log(`      Description: ${w.description}`);
		console.log(`      Progress: ${((w.size / w.limit) * 100).toFixed(1)}% of limit used`);
		console.log();
	});
}

/**
 * Report performance issues
 */
function reportPerformanceIssues(issues) {
	if (issues.length === 0) return;

	console.log('🐌 PERFORMANCE ISSUES:');
	console.log('-'.repeat(80));

	issues.forEach(issue => {
		console.log(`   📦 ${issue.file} (Score: ${issue.score}/100)`);
		issue.issues.forEach(desc => {
			console.log(`      • ${desc}`);
		});
		console.log();
	});
}

/**
 * Report network analysis
 */
function reportNetworkAnalysis(totalGzipped, criticalPathSize) {
	console.log('🌐 NETWORK IMPACT ANALYSIS:');
	console.log('-'.repeat(80));

	const totalLoadTimes = calculateLoadTimes(totalGzipped);
	const criticalLoadTimes = calculateLoadTimes(criticalPathSize);

	console.log('   Full Library Load Times:');
	Object.entries(totalLoadTimes).forEach(([network, time]) => {
		const status = time > 5000 ? '🔴' : time > 2000 ? '🟡' : '🟢';
		console.log(`      ${network.padEnd(6)}: ${time.toString().padStart(5)}ms ${status}`);
	});

	console.log();
	console.log('   Critical Path Load Times:');
	Object.entries(criticalLoadTimes).forEach(([network, time]) => {
		const status = time > 2000 ? '🔴' : time > 1000 ? '🟡' : '🟢';
		console.log(`      ${network.padEnd(6)}: ${time.toString().padStart(5)}ms ${status}`);
	});
	console.log();
}

/**
 * Report compression analysis
 */
function reportCompressionAnalysis(totalSize, totalGzipped, totalBrotli) {
	console.log('🗜️  COMPRESSION ANALYSIS:');
	console.log('-'.repeat(80));

	const gzipRatio = (totalGzipped / totalSize) * 100;
	const brotliRatio = (totalBrotli / totalSize) * 100;
	const brotliImprovement = ((totalGzipped - totalBrotli) / totalGzipped) * 100;

	console.log(`   Raw Size:      ${totalSize.toFixed(1)} KB`);
	console.log(`   Gzipped:       ${totalGzipped.toFixed(1)} KB (${gzipRatio.toFixed(1)}% of original)`);
	console.log(`   Brotli:        ${totalBrotli.toFixed(1)} KB (${brotliRatio.toFixed(1)}% of original)`);
	console.log(`   Brotli Gain:   ${brotliImprovement.toFixed(1)}% smaller than Gzip`);

	const compressionStatus = gzipRatio < 30 ? '🟢 Excellent' : gzipRatio < 40 ? '🟡 Good' : '🔴 Poor';
	console.log(`   Compression:   ${compressionStatus}`);
	console.log();
}

/**
 * Report code splitting analysis
 */
function reportCodeSplittingAnalysis(results) {
	console.log('✂️  CODE SPLITTING ANALYSIS:');
	console.log('-'.repeat(80));

	const mainBundle = results.find(r => r.filename === 'index.es.js');
	const factoryBundles = results.filter(r => r.filename.includes('factories'));
	const coreBundle = results.find(r => r.filename === 'core/index.es.js');

	const mainSize = mainBundle?.gzippedSize || 0;
	const factoryTotalSize = factoryBundles.reduce((sum, b) => sum + b.gzippedSize, 0);
	const coreSize = coreBundle?.gzippedSize || 0;

	const splittingRatio = factoryTotalSize / (mainSize + factoryTotalSize) * 100;
	const coreRatio = coreSize / mainSize * 100;

	console.log(`   Main Bundle:     ${mainSize.toFixed(1)} KB`);
	console.log(`   Core Bundle:     ${coreSize.toFixed(1)} KB (${coreRatio.toFixed(1)}% of main)`);
	console.log(`   Factory Total:   ${factoryTotalSize.toFixed(1)} KB`);
	console.log(`   Splitting Ratio: ${splittingRatio.toFixed(1)}% (target: >40%)`);

	const splittingStatus = splittingRatio > 50 ? '🟢 Excellent' : splittingRatio > 40 ? '🟡 Good' : '🔴 Needs Improvement';
	console.log(`   Effectiveness:   ${splittingStatus}`);
	console.log();
}

/**
 * Set up file watching for real-time monitoring
 */
function setupFileWatcher() {
	console.log('👀 Setting up real-time bundle monitoring...');
	console.log(`Watching: ${DIST_DIR}`);
	console.log('Press Ctrl+C to stop monitoring\n');

	const watcher = chokidar.watch(DIST_DIR, {
		ignored: /node_modules/,
		persistent: true,
		ignoreInitial: true,
	});

	let debounceTimer;

	watcher.on('change', (filePath) => {
		clearTimeout(debounceTimer);
		debounceTimer = setTimeout(() => {
			console.log(`\n📁 File changed: ${path.relative(DIST_DIR, filePath)}`);
			console.log('🔄 Re-analyzing bundles...\n');

			try {
				const results = monitorBundleSizesEnhanced();
				generateEnhancedReport(results);

				// Send alerts if needed
				if (results.violations.length > 0) {
					sendAlert('Bundle size violations detected!', results.violations);
				}
			} catch (error) {
				console.error('❌ Error during monitoring:', error.message);
			}
		}, 1000); // Debounce for 1 second
	});

	watcher.on('error', (error) => {
		console.error('❌ Watcher error:', error);
	});

	// Initial analysis
	try {
		const results = monitorBundleSizesEnhanced();
		generateEnhancedReport(results);
	} catch (error) {
		console.error('❌ Initial analysis failed:', error.message);
	}

	return watcher;
}

/**
 * Send alert (placeholder for webhook/email integration)
 */
function sendAlert(message, data) {
	console.log(`\n🚨 ALERT: ${message}`);

	// Here you could integrate with:
	// - Slack webhooks
	// - Email notifications
	// - Discord webhooks
	// - Custom monitoring systems

	// For now, just log to console
	console.log('Alert data:', JSON.stringify(data, null, 2));
}

/**
 * Generate enhanced report with more details
 */
function generateEnhancedReport(monitoringResults) {
	const report = {
		timestamp: new Date().toISOString(),
		version: getPackageVersion(),
		summary: {
			totalBundles: monitoringResults.results.length,
			violations: monitoringResults.violations.length,
			warnings: monitoringResults.warnings.length,
			performanceIssues: monitoringResults.performanceIssues.length,
			averageScore: monitoringResults.analysis.averagePerformanceScore,
			totalSize: monitoringResults.totals,
			analysis: monitoringResults.analysis,
		},
		bundles: monitoringResults.results.map(bundle => ({
			...bundle,
			recommendations: generateBundleRecommendations(bundle),
		})),
		violations: monitoringResults.violations,
		warnings: monitoringResults.warnings,
		performanceIssues: monitoringResults.performanceIssues,
		recommendations: generateGlobalRecommendations(monitoringResults),
		buildInfo: getBuildInfo(),
		networkAnalysis: {
			loadTimes: calculateLoadTimes(monitoringResults.totals.gzippedSize),
			criticalPathLoadTimes: calculateLoadTimes(monitoringResults.totals.criticalPathSize),
		},
	};

	// Save enhanced report
	const reportPath = path.join(REPORTS_DIR, `bundle-report-enhanced-${Date.now()}.json`);
	fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

	// Save latest report
	const latestReportPath = path.join(REPORTS_DIR, 'bundle-report-enhanced-latest.json');
	fs.writeFileSync(latestReportPath, JSON.stringify(report, null, 2));

	console.log(`📄 Enhanced report saved: ${reportPath}`);
	console.log(`📄 Latest report updated: ${latestReportPath}`);
	console.log();

	return report;
}

/**
 * Generate bundle-specific recommendations
 */
function generateBundleRecommendations(bundle) {
	const recommendations = [];
	const compressionRatio = bundle.gzippedSize / bundle.rawSize;

	if (bundle.gzippedSize > bundle.budget.max) {
		recommendations.push('Consider code splitting or lazy loading');
		recommendations.push('Remove unused exports and dependencies');
		recommendations.push('Optimize component implementations');
	}

	if (compressionRatio > 0.4) {
		recommendations.push('Improve code structure for better compression');
		recommendations.push('Minimize repetitive code patterns');
	}

	if (bundle.loadTimes['3G'] > bundle.budget.loadTime) {
		recommendations.push('Implement progressive loading');
		recommendations.push('Consider service worker caching');
	}

	return recommendations;
}

/**
 * Generate global recommendations
 */
function generateGlobalRecommendations(results) {
	const recommendations = [];

	if (results.violations.length > 0) {
		recommendations.push({
			type: 'critical',
			title: 'Bundle Size Violations',
			description: 'Immediate action required for bundle size compliance',
			actions: [
				'Review and optimize oversized bundles',
				'Implement dynamic imports for heavy components',
				'Enable more aggressive tree-shaking',
				'Consider moving large dependencies to separate chunks',
			],
		});
	}

	if (results.analysis.averagePerformanceScore < 80) {
		recommendations.push({
			type: 'performance',
			title: 'Performance Optimization',
			description: 'Overall performance score needs improvement',
			actions: [
				'Optimize compression ratios',
				'Improve code splitting strategy',
				'Implement lazy loading for non-critical components',
				'Review and optimize large bundles',
			],
		});
	}

	return recommendations;
}

/**
 * Get package version
 */
function getPackageVersion() {
	try {
		const packageJson = JSON.parse(fs.readFileSync(PACKAGE_JSON, 'utf8'));
		return packageJson.version;
	} catch (error) {
		return 'unknown';
	}
}

/**
 * Get build information
 */
function getBuildInfo() {
	return {
		nodeVersion: process.version,
		platform: process.platform,
		arch: process.arch,
		timestamp: new Date().toISOString(),
		environment: process.env.NODE_ENV || 'development',
	};
}

/**
 * Main execution
 */
function main() {
	const args = process.argv.slice(2);
	const command = args[0];

	switch (command) {
		case '--watch':
			setupFileWatcher();
			break;
		case '--analyze':
			console.log('📊 Running detailed bundle analysis...\n');
			const results = monitorBundleSizesEnhanced();
			generateEnhancedReport(results);
			break;
		case '--config':
			console.log('⚙️  Bundle monitoring configuration:');
			console.log(JSON.stringify(PERFORMANCE_BUDGETS, null, 2));
			break;
		default:
			// Run enhanced monitoring
			const monitoringResults = monitorBundleSizesEnhanced();
			const report = generateEnhancedReport(monitoringResults);

			// Exit with appropriate code
			if (monitoringResults.violations.length > 0) {
				console.log('❌ Bundle monitoring failed due to violations');
				process.exit(1);
			} else if (monitoringResults.warnings.length > 0) {
				console.log('⚠️  Bundle monitoring completed with warnings');
				process.exit(0);
			} else {
				console.log('✅ Bundle monitoring passed');
				process.exit(0);
			}
	}
}

// Handle graceful shutdown for watch mode
process.on('SIGINT', () => {
	console.log('\n👋 Stopping bundle monitoring...');
	process.exit(0);
});

// Run if called directly
if (require.main === module) {
	main();
}

module.exports = {
	monitorBundleSizesEnhanced,
	generateEnhancedReport,
	setupFileWatcher,
	PERFORMANCE_BUDGETS,
};
