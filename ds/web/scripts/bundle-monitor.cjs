#!/usr/bin/env node

/**
 * Bundle Size Monitoring Script
 * Monitors bundle sizes and provides detailed analysis and alerts
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const DIST_DIR = path.join(__dirname, '../dist');
const REPORTS_DIR = path.join(__dirname, '../reports');
const PACKAGE_JSON = path.join(__dirname, '../package.json');

// Ensure reports directory exists
if (!fs.existsSync(REPORTS_DIR)) {
	fs.mkdirSync(REPORTS_DIR, { recursive: true });
}

/**
 * Get file size in bytes
 */
function getFileSizeBytes(filePath) {
	try {
		const stats = fs.statSync(filePath);
		return stats.size;
	} catch (error) {
		return 0;
	}
}

/**
 * Get file size in KB
 */
function getFileSize(filePath) {
	return (getFileSizeBytes(filePath) / 1024).toFixed(2);
}

/**
 * Get gzipped file size
 */
function getGzippedSize(filePath) {
	try {
		const gzipCommand = process.platform === 'win32'
			? `powershell -command "& {[System.IO.File]::ReadAllBytes('${filePath}') | ForEach-Object {$_} | Measure-Object -Sum | Select-Object -ExpandProperty Sum}"`
			: `gzip -c "${filePath}" | wc -c`;
		const size = execSync(gzipCommand, { encoding: 'utf8' });
		return (parseInt(size.trim()) / 1024).toFixed(2);
	} catch (error) {
		// Fallback: estimate gzip size as ~30% of original
		return (getFileSizeBytes(filePath) * 0.3 / 1024).toFixed(2);
	}
}

/**
 * Get Brotli compressed size
 */
function getBrotliSize(filePath) {
	try {
		const brotliCommand = process.platform === 'win32'
			? `powershell -command "& {[System.IO.File]::ReadAllBytes('${filePath}') | ForEach-Object {$_} | Measure-Object -Sum | Select-Object -ExpandProperty Sum}"`
			: `brotli -c "${filePath}" | wc -c`;
		const size = execSync(brotliCommand, { encoding: 'utf8' });
		return (parseInt(size.trim()) / 1024).toFixed(2);
	} catch (error) {
		// Fallback: estimate brotli size as ~25% of original
		return (getFileSizeBytes(filePath) * 0.25 / 1024).toFixed(2);
	}
}

/**
 * Bundle size limits configuration
 */
const BUNDLE_LIMITS = {
	'index.es.js': { max: 120, warning: 100, critical: true },
	'factories-light.es.js': { max: 60, warning: 45, critical: false },
	'factories.es.js': { max: 80, warning: 60, critical: false },
	'factories-heavy.es.js': { max: 250, warning: 200, critical: false },
	'core/index.es.js': { max: 40, warning: 30, critical: true },
	'elements/index.es.js': { max: 60, warning: 45, critical: true },
	'collections/index.es.js': { max: 35, warning: 25, critical: false },
	'modules/index.es.js': { max: 50, warning: 40, critical: false },
	'base/index.es.js': { max: 25, warning: 20, critical: false },
	'constants.es.js': { max: 15, warning: 10, critical: true },
	'hooks.es.js': { max: 10, warning: 8, critical: true },
	'utils.es.js': { max: 20, warning: 15, critical: true },
};

/**
 * Monitor bundle sizes with comprehensive analysis
 */
function monitorBundleSizes() {
	console.log('📊 Bundle Size Monitoring Report');
	console.log('='.repeat(80));
	console.log(`Generated: ${new Date().toISOString()}`);
	console.log('='.repeat(80));

	const results = [];
	let totalSize = 0;
	let totalGzipped = 0;
	let totalBrotli = 0;
	let criticalPathSize = 0;
	let violations = [];
	let warnings = [];

	console.log(`${'Bundle'.padEnd(30)} ${'Raw'.padStart(10)} ${'Gzipped'.padStart(10)} ${'Brotli'.padStart(10)} ${'Status'.padStart(10)}`);
	console.log('='.repeat(80));

	Object.entries(BUNDLE_LIMITS).forEach(([filename, limits]) => {
		const filePath = path.join(DIST_DIR, filename);
		const rawSize = parseFloat(getFileSize(filePath));
		const gzippedSize = parseFloat(getGzippedSize(filePath));
		const brotliSize = parseFloat(getBrotliSize(filePath));

		totalSize += rawSize;
		totalGzipped += gzippedSize;
		totalBrotli += brotliSize;

		if (limits.critical) {
			criticalPathSize += gzippedSize;
		}

		let status = '✅ OK';
		if (gzippedSize > limits.max) {
			status = '❌ FAIL';
			violations.push({
				file: filename,
				size: gzippedSize,
				limit: limits.max,
				excess: gzippedSize - limits.max,
				critical: limits.critical,
			});
		} else if (gzippedSize > limits.warning) {
			status = '⚠️  WARN';
			warnings.push({
				file: filename,
				size: gzippedSize,
				warning: limits.warning,
				limit: limits.max,
				remaining: limits.max - gzippedSize,
			});
		}

		console.log(`${filename.padEnd(30)} ${rawSize.toFixed(2).padStart(8)} KB ${gzippedSize.toFixed(2).padStart(8)} KB ${brotliSize.toFixed(2).padStart(8)} KB ${status.padStart(10)}`);

		results.push({
			filename,
			rawSize,
			gzippedSize,
			brotliSize,
			limits,
			status: status.includes('FAIL') ? 'fail' : status.includes('WARN') ? 'warn' : 'ok',
		});
	});

	console.log('='.repeat(80));
	console.log(`${'TOTAL'.padEnd(30)} ${totalSize.toFixed(2).padStart(8)} KB ${totalGzipped.toFixed(2).padStart(8)} KB ${totalBrotli.toFixed(2).padStart(8)} KB`);
	console.log(`${'Critical Path'.padEnd(30)} ${'-'.padStart(8)}    ${criticalPathSize.toFixed(2).padStart(8)} KB ${'-'.padStart(8)}    ${'-'.padStart(8)}  `);
	console.log();

	// Report violations
	if (violations.length > 0) {
		console.log('❌ SIZE LIMIT VIOLATIONS:');
		violations.forEach(v => {
			const criticalNote = v.critical ? ' (CRITICAL PATH)' : '';
			console.log(`   ${v.file}: ${v.size.toFixed(2)} KB (${v.excess.toFixed(2)} KB over limit)${criticalNote}`);
		});
		console.log();
	}

	// Report warnings
	if (warnings.length > 0) {
		console.log('⚠️  SIZE WARNINGS:');
		warnings.forEach(w => {
			console.log(`   ${w.file}: ${w.size.toFixed(2)} KB (${w.remaining.toFixed(2)} KB remaining)`);
		});
		console.log();
	}

	// Performance analysis
	console.log('📈 PERFORMANCE ANALYSIS:');
	console.log(`   Total library size: ${totalGzipped.toFixed(2)} KB (gzipped)`);
	console.log(`   Critical path size: ${criticalPathSize.toFixed(2)} KB (gzipped)`);
	console.log(`   Compression ratio: ${((totalGzipped / totalSize) * 100).toFixed(1)}%`);
	console.log(`   Brotli improvement: ${(((totalGzipped - totalBrotli) / totalGzipped) * 100).toFixed(1)}%`);
	console.log();

	// Bundle efficiency analysis
	const heavyBundles = results.filter(r => r.gzippedSize > 50);
	if (heavyBundles.length > 0) {
		console.log('🔍 HEAVY BUNDLES (>50KB):');
		heavyBundles.forEach(bundle => {
			console.log(`   ${bundle.filename}: ${bundle.gzippedSize.toFixed(2)} KB`);
		});
		console.log();
	}

	// Code splitting effectiveness
	const factoryBundles = results.filter(r => r.filename.includes('factories'));
	const factoryTotalSize = factoryBundles.reduce((sum, b) => sum + b.gzippedSize, 0);
	const mainBundleSize = results.find(r => r.filename === 'index.es.js')?.gzippedSize || 0;
	const splittingRatio = factoryTotalSize / (mainBundleSize + factoryTotalSize) * 100;

	console.log('✂️  CODE SPLITTING ANALYSIS:');
	console.log(`   Main bundle: ${mainBundleSize.toFixed(2)} KB`);
	console.log(`   Factory bundles: ${factoryTotalSize.toFixed(2)} KB`);
	console.log(`   Splitting ratio: ${splittingRatio.toFixed(1)}% (target: >40%)`);
	console.log(`   Splitting effectiveness: ${splittingRatio > 40 ? '✅ Good' : '⚠️  Could improve'}`);
	console.log();

	return {
		results,
		violations,
		warnings,
		totals: {
			rawSize: totalSize,
			gzippedSize: totalGzipped,
			brotliSize: totalBrotli,
			criticalPathSize,
		},
		analysis: {
			compressionRatio: (totalGzipped / totalSize) * 100,
			brotliImprovement: ((totalGzipped - totalBrotli) / totalGzipped) * 100,
			splittingRatio,
		},
	};
}

/**
 * Generate detailed bundle report
 */
function generateBundleReport(monitoringResults) {
	const report = {
		timestamp: new Date().toISOString(),
		version: getPackageVersion(),
		summary: {
			totalBundles: monitoringResults.results.length,
			violations: monitoringResults.violations.length,
			warnings: monitoringResults.warnings.length,
			totalSize: monitoringResults.totals,
			analysis: monitoringResults.analysis,
		},
		bundles: monitoringResults.results,
		violations: monitoringResults.violations,
		warnings: monitoringResults.warnings,
		recommendations: generateRecommendations(monitoringResults),
		buildInfo: getBuildInfo(),
	};

	// Save detailed report
	const reportPath = path.join(REPORTS_DIR, `bundle-report-${Date.now()}.json`);
	fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

	// Save latest report
	const latestReportPath = path.join(REPORTS_DIR, 'bundle-report-latest.json');
	fs.writeFileSync(latestReportPath, JSON.stringify(report, null, 2));

	console.log(`📄 Detailed report saved: ${reportPath}`);
	console.log(`📄 Latest report updated: ${latestReportPath}`);
	console.log();

	return report;
}

/**
 * Get package version
 */
function getPackageVersion() {
	try {
		const packageJson = JSON.parse(fs.readFileSync(PACKAGE_JSON, 'utf8'));
		return packageJson.version;
	} catch (error) {
		return 'unknown';
	}
}

/**
 * Get build information
 */
function getBuildInfo() {
	return {
		nodeVersion: process.version,
		platform: process.platform,
		arch: process.arch,
		timestamp: new Date().toISOString(),
		environment: process.env.NODE_ENV || 'development',
	};
}

/**
 * Generate optimization recommendations
 */
function generateRecommendations(results) {
	const recommendations = [];

	// Size-based recommendations
	if (results.violations.length > 0) {
		recommendations.push({
			type: 'critical',
			title: 'Bundle Size Violations',
			description: 'Some bundles exceed their size limits',
			actions: [
				'Move heavy components to separate bundles',
				'Optimize imports and remove unused code',
				'Use dynamic imports for large dependencies',
				'Enable more aggressive tree-shaking',
			],
		});
	}

	if (results.warnings.length > 0) {
		recommendations.push({
			type: 'warning',
			title: 'Bundle Size Warnings',
			description: 'Some bundles are approaching their size limits',
			actions: [
				'Monitor bundle growth closely',
				'Consider preemptive optimization',
				'Review recent changes for size impact',
			],
		});
	}

	// Performance recommendations
	if (results.totals.gzippedSize > 300) {
		recommendations.push({
			type: 'performance',
			title: 'Large Total Bundle Size',
			description: 'Total library size is quite large',
			actions: [
				'Implement progressive loading',
				'Use service workers for caching',
				'Consider lazy loading for non-critical components',
				'Optimize CSS output with better tree-shaking',
			],
		});
	}

	// Code splitting recommendations
	if (results.analysis.splittingRatio < 40) {
		recommendations.push({
			type: 'optimization',
			title: 'Improve Code Splitting',
			description: 'Code splitting could be more effective',
			actions: [
				'Move more components to factory bundles',
				'Implement dynamic imports for heavy features',
				'Review main bundle composition',
				'Consider lazy loading strategies',
			],
		});
	}

	// Compression recommendations
	if (results.analysis.compressionRatio > 40) {
		recommendations.push({
			type: 'optimization',
			title: 'Improve Compression',
			description: 'Bundle compression could be better',
			actions: [
				'Enable Brotli compression on server',
				'Optimize CSS output for better compression',
				'Remove unnecessary whitespace and comments',
				'Use shorter variable names in production',
			],
		});
	}

	return recommendations;
}

/**
 * Compare with previous report
 */
function compareWithPrevious() {
	const currentReportPath = path.join(REPORTS_DIR, 'bundle-report-latest.json');
	const previousReportPath = path.join(REPORTS_DIR, 'bundle-report-previous.json');

	if (!fs.existsSync(currentReportPath)) {
		console.log('⚠️  No current report found. Run monitoring first.');
		return;
	}

	try {
		const currentReport = JSON.parse(fs.readFileSync(currentReportPath, 'utf8'));

		if (fs.existsSync(previousReportPath)) {
			const previousReport = JSON.parse(fs.readFileSync(previousReportPath, 'utf8'));

			console.log('📊 Bundle Size Comparison');
			console.log('='.repeat(80));
			console.log(`Current: ${currentReport.timestamp}`);
			console.log(`Previous: ${previousReport.timestamp}`);
			console.log('='.repeat(80));

			console.log(`${'Bundle'.padEnd(30)} ${'Previous'.padStart(12)} ${'Current'.padStart(12)} ${'Change'.padStart(12)} ${'Status'.padStart(8)}`);
			console.log('='.repeat(80));

			currentReport.bundles.forEach(currentBundle => {
				const previousBundle = previousReport.bundles.find(b => b.filename === currentBundle.filename);

				if (previousBundle) {
					const change = currentBundle.gzippedSize - previousBundle.gzippedSize;
					const changePercent = previousBundle.gzippedSize > 0 ? (change / previousBundle.gzippedSize) * 100 : 0;

					let status = '📊';
					if (change > 0) {
						status = changePercent > 10 ? '🔴' : changePercent > 5 ? '🟡' : '📈';
					} else if (change < 0) {
						status = '🟢';
					}

					const changeStr = change > 0 ? `+${change.toFixed(2)}` : change.toFixed(2);
					const percentStr = changePercent !== 0 ? ` (${changePercent > 0 ? '+' : ''}${changePercent.toFixed(1)}%)` : '';

					console.log(`${currentBundle.filename.padEnd(30)} ${previousBundle.gzippedSize.toFixed(2).padStart(10)} KB ${currentBundle.gzippedSize.toFixed(2).padStart(10)} KB ${(changeStr + percentStr).padStart(12)} ${status.padStart(8)}`);
				} else {
					console.log(`${currentBundle.filename.padEnd(30)} ${'NEW'.padStart(10)}    ${currentBundle.gzippedSize.toFixed(2).padStart(10)} KB ${'NEW'.padStart(12)} ${'🆕'.padStart(8)}`);
				}
			});

			console.log();

			// Overall comparison
			const currentTotal = currentReport.summary.totalSize.gzippedSize;
			const previousTotal = previousReport.summary.totalSize.gzippedSize;
			const totalChange = currentTotal - previousTotal;
			const totalChangePercent = previousTotal > 0 ? (totalChange / previousTotal) * 100 : 0;

			console.log('📈 OVERALL CHANGE:');
			console.log(`   Total size: ${previousTotal.toFixed(2)} KB → ${currentTotal.toFixed(2)} KB`);
			console.log(`   Change: ${totalChange > 0 ? '+' : ''}${totalChange.toFixed(2)} KB (${totalChangePercent > 0 ? '+' : ''}${totalChangePercent.toFixed(1)}%)`);
			console.log(`   Status: ${totalChange > 0 ? (totalChangePercent > 10 ? '🔴 Significant increase' : '🟡 Moderate increase') : totalChange < 0 ? '🟢 Decrease' : '📊 No change'}`);
			console.log();

		} else {
			console.log('📝 No previous report found. Current report will be the baseline.');
		}

		// Save current as previous for next comparison
		fs.copyFileSync(currentReportPath, previousReportPath);

	} catch (error) {
		console.log('⚠️  Error comparing reports:', error.message);
	}
}

/**
 * Set up continuous monitoring
 */
function setupContinuousMonitoring() {
	console.log('🔄 Setting up continuous bundle monitoring...');

	// Create monitoring configuration
	const monitoringConfig = {
		enabled: true,
		interval: 60000, // 1 minute
		thresholds: BUNDLE_LIMITS,
		alerts: {
			email: false,
			webhook: false,
			console: true,
		},
		history: {
			maxReports: 100,
			retentionDays: 30,
		},
	};

	const configPath = path.join(REPORTS_DIR, 'monitoring-config.json');
	fs.writeFileSync(configPath, JSON.stringify(monitoringConfig, null, 2));

	console.log(`✅ Monitoring configuration saved: ${configPath}`);
	console.log('   To enable continuous monitoring, run: pnpm bundle:monitor --watch');
	console.log();
}

/**
 * Clean old reports
 */
function cleanOldReports() {
	console.log('🧹 Cleaning old bundle reports...');

	try {
		const files = fs.readdirSync(REPORTS_DIR);
		const reportFiles = files.filter(f => f.startsWith('bundle-report-') && f.endsWith('.json') && f !== 'bundle-report-latest.json' && f !== 'bundle-report-previous.json');

		// Keep only the last 10 reports
		const sortedReports = reportFiles
			.map(f => ({
				name: f,
				path: path.join(REPORTS_DIR, f),
				mtime: fs.statSync(path.join(REPORTS_DIR, f)).mtime,
			}))
			.sort((a, b) => b.mtime - a.mtime);

		const toDelete = sortedReports.slice(10);

		toDelete.forEach(report => {
			fs.unlinkSync(report.path);
			console.log(`   Deleted: ${report.name}`);
		});

		console.log(`✅ Cleaned ${toDelete.length} old reports (kept ${sortedReports.length - toDelete.length})`);
		console.log();

	} catch (error) {
		console.log('⚠️  Error cleaning reports:', error.message);
	}
}

/**
 * Main execution
 */
function main() {
	const args = process.argv.slice(2);
	const command = args[0];

	switch (command) {
		case '--compare':
			compareWithPrevious();
			break;
		case '--setup':
			setupContinuousMonitoring();
			break;
		case '--clean':
			cleanOldReports();
			break;
		case '--watch':
			console.log('🔄 Continuous monitoring not implemented yet');
			console.log('   Use --setup to configure monitoring');
			break;
		default:
			// Run full monitoring
			const results = monitorBundleSizes();
			const report = generateBundleReport(results);

			// Exit with error code if there are violations
			if (results.violations.length > 0) {
				console.log('❌ Bundle size monitoring failed due to violations');
				process.exit(1);
			} else if (results.warnings.length > 0) {
				console.log('⚠️  Bundle size monitoring completed with warnings');
				process.exit(0);
			} else {
				console.log('✅ Bundle size monitoring passed');
				process.exit(0);
			}
	}
}

// Run if called directly
if (require.main === module) {
	main();
}

module.exports = {
	monitorBundleSizes,
	generateBundleReport,
	compareWithPrevious,
	setupContinuousMonitoring,
	cleanOldReports,
};
