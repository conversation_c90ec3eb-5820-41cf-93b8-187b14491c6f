#!/usr/bin/env node

/**
 * Advanced CSS Optimization Script
 * Post-processes CSS files for optimal compression and performance
 */

const fs = require('fs');
const path = require('path');
const { glob } = require('glob');

const DIST_DIR = path.join(__dirname, '../dist');

/**
 * Advanced CSS optimization functions
 */
const CSSOptimizer = {
	/**
	 * Optimize CSS content with advanced transformations
	 */
	optimizeCSS: (css) => {
		return css
			// Remove development comments and debug styles
			.replace(/\/\*\s*dev[\s\S]*?\*\//g, '')
			.replace(/\/\*\s*debug[\s\S]*?\*\//g, '')
			.replace(/outline:\s*[^;]+;/g, '')

			// Optimize color values
			.replace(/#([0-9a-f])\1([0-9a-f])\2([0-9a-f])\3/gi, '#$1$2$3')
			.replace(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*1\)/g, (match, r, g, b) => {
				const hex = ((1 << 24) + (parseInt(r) << 16) + (parseInt(g) << 8) + parseInt(b)).toString(16).slice(1);
				return `#${hex}`;
			})
			.replace(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/g, (match, r, g, b) => {
				const hex = ((1 << 24) + (parseInt(r) << 16) + (parseInt(g) << 8) + parseInt(b)).toString(16).slice(1);
				return `#${hex}`;
			})

			// Optimize zero values
			.replace(/\b0px\b/g, '0')
			.replace(/\b0em\b/g, '0')
			.replace(/\b0rem\b/g, '0')
			.replace(/\b0%\b/g, '0')
			.replace(/\b0deg\b/g, '0')
			.replace(/\b0s\b/g, '0')
			.replace(/\b0ms\b/g, '0')

			// Optimize decimal values
			.replace(/0\.(\d+)/g, '.$1')
			.replace(/(\d+)\.0+(?!\d)/g, '$1')

			// Remove unnecessary whitespace
			.replace(/\s+/g, ' ')
			.replace(/;\s*}/g, '}')
			.replace(/\{\s+/g, '{')
			.replace(/\s+\{/g, '{')
			.replace(/;\s+/g, ';')
			.replace(/,\s+/g, ',')
			.replace(/:\s+/g, ':')

			// Remove empty rules
			.replace(/[^{}]+\{\s*\}/g, '')

			// Optimize font-weight values
			.replace(/font-weight:\s*normal/g, 'font-weight:400')
			.replace(/font-weight:\s*bold/g, 'font-weight:700')

			// Optimize margin and padding shorthand
			.replace(/margin:\s*(\S+)\s+\1\s+\1\s+\1/g, 'margin:$1')
			.replace(/padding:\s*(\S+)\s+\1\s+\1\s+\1/g, 'padding:$1')
			.replace(/margin:\s*(\S+)\s+(\S+)\s+\1\s+\2/g, 'margin:$1 $2')
			.replace(/padding:\s*(\S+)\s+(\S+)\s+\1\s+\2/g, 'padding:$1 $2')

			// Remove trailing semicolons
			.replace(/;}/g, '}')

			.trim();
	},

	/**
	 * Process all CSS files in the dist directory
	 */
	processDistFiles: () => {
		const cssFiles = glob.sync('**/*.css', { cwd: DIST_DIR });
		let totalOriginalSize = 0;
		let totalOptimizedSize = 0;
		let filesProcessed = 0;

		console.log('🎨 CSS Optimization Report');
		console.log('='.repeat(80));

		cssFiles.forEach(file => {
			const filePath = path.join(DIST_DIR, file);

			try {
				const originalContent = fs.readFileSync(filePath, 'utf8');
				const optimizedContent = CSSOptimizer.optimizeCSS(originalContent);

				const originalSize = Buffer.byteLength(originalContent, 'utf8');
				const optimizedSize = Buffer.byteLength(optimizedContent, 'utf8');
				const savings = originalSize - optimizedSize;
				const savingsPercent = originalSize > 0 ? ((savings / originalSize) * 100).toFixed(1) : '0.0';

				totalOriginalSize += originalSize;
				totalOptimizedSize += optimizedSize;
				filesProcessed++;

				// Write optimized content back
				fs.writeFileSync(filePath, optimizedContent);

				console.log(`${file.padEnd(40)} ${(originalSize / 1024).toFixed(2).padStart(8)} KB → ${(optimizedSize / 1024).toFixed(2).padStart(8)} KB (${savingsPercent.padStart(5)}% saved)`);

			} catch (error) {
				console.error(`❌ Error processing ${file}:`, error.message);
			}
		});

		const totalSavings = totalOriginalSize - totalOptimizedSize;
		const totalSavingsPercent = totalOriginalSize > 0 ? ((totalSavings / totalOriginalSize) * 100).toFixed(1) : '0.0';

		console.log('='.repeat(80));
		console.log(`${'TOTAL'.padEnd(40)} ${(totalOriginalSize / 1024).toFixed(2).padStart(8)} KB → ${(totalOptimizedSize / 1024).toFixed(2).padStart(8)} KB (${totalSavingsPercent.padStart(5)}% saved)`);
		console.log(`Files processed: ${filesProcessed}`);
		console.log(`Total savings: ${(totalSavings / 1024).toFixed(2)} KB`);
		console.log();

		return {
			filesProcessed,
			originalSize: totalOriginalSize,
			optimizedSize: totalOptimizedSize,
			savings: totalSavings,
			savingsPercent: parseFloat(totalSavingsPercent),
		};
	},

	/**
	 * Analyze CSS for further optimization opportunities
	 */
	analyzeOptimizationOpportunities: () => {
		const cssFiles = glob.sync('**/*.css', { cwd: DIST_DIR });
		const opportunities = [];

		console.log('🔍 CSS Optimization Opportunities');
		console.log('='.repeat(80));

		cssFiles.forEach(file => {
			const filePath = path.join(DIST_DIR, file);

			try {
				const content = fs.readFileSync(filePath, 'utf8');
				const fileOpportunities = [];

				// Check for duplicate selectors
				const selectors = content.match(/[^{}]+(?=\{)/g) || [];
				const selectorCounts = {};
				selectors.forEach(selector => {
					const cleanSelector = selector.trim();
					selectorCounts[cleanSelector] = (selectorCounts[cleanSelector] || 0) + 1;
				});

				Object.entries(selectorCounts).forEach(([selector, count]) => {
					if (count > 1) {
						fileOpportunities.push({
							type: 'duplicate-selector',
							message: `Duplicate selector "${selector}" appears ${count} times`,
							impact: 'medium',
						});
					}
				});

				// Check for unused vendor prefixes (basic check)
				const vendorPrefixes = ['-webkit-', '-moz-', '-ms-', '-o-'];
				vendorPrefixes.forEach(prefix => {
					const prefixCount = (content.match(new RegExp(prefix, 'g')) || []).length;
					if (prefixCount > 10) {
						fileOpportunities.push({
							type: 'vendor-prefixes',
							message: `High usage of ${prefix} prefix (${prefixCount} occurrences)`,
							impact: 'low',
							suggestion: 'Consider using autoprefixer or removing unnecessary prefixes',
						});
					}
				});

				// Check for long color values that could be optimized
				const longColors = content.match(/#[0-9a-f]{6}/gi) || [];
				const optimizableColors = longColors.filter(color => {
					const hex = color.substring(1);
					return hex[0] === hex[1] && hex[2] === hex[3] && hex[4] === hex[5];
				});

				if (optimizableColors.length > 0) {
					fileOpportunities.push({
						type: 'color-optimization',
						message: `${optimizableColors.length} colors can be shortened`,
						impact: 'low',
						suggestion: 'Convert #aabbcc to #abc format',
					});
				}

				if (fileOpportunities.length > 0) {
					opportunities.push({ file, opportunities: fileOpportunities });
				}

			} catch (error) {
				console.error(`❌ Error analyzing ${file}:`, error.message);
			}
		});

		if (opportunities.length === 0) {
			console.log('✅ No major optimization opportunities found');
		} else {
			opportunities.forEach(({ file, opportunities: fileOpps }) => {
				console.log(`\n📄 ${file}:`);
				fileOpps.forEach(opp => {
					const impactIcon = opp.impact === 'high' ? '🔴' : opp.impact === 'medium' ? '🟡' : '🟢';
					console.log(`   ${impactIcon} ${opp.message}`);
					if (opp.suggestion) {
						console.log(`      💡 ${opp.suggestion}`);
					}
				});
			});
		}

		console.log();
		return opportunities;
	},
};

/**
 * Main execution
 */
function main() {
	console.log('🚀 Advanced CSS Optimization');
	console.log(`Processing files in: ${DIST_DIR}`);
	console.log();

	// Check if dist directory exists
	if (!fs.existsSync(DIST_DIR)) {
		console.error('❌ Dist directory not found. Run build first.');
		process.exit(1);
	}

	// Process CSS files
	const results = CSSOptimizer.processDistFiles();

	// Analyze optimization opportunities
	const opportunities = CSSOptimizer.analyzeOptimizationOpportunities();

	// Summary
	console.log('📊 Optimization Summary');
	console.log('='.repeat(80));
	console.log(`Files processed: ${results.filesProcessed}`);
	console.log(`Total size reduction: ${(results.savings / 1024).toFixed(2)} KB (${results.savingsPercent}%)`);
	console.log(`Optimization opportunities: ${opportunities.length} files`);
	console.log();

	if (results.savingsPercent > 0) {
		console.log('✅ CSS optimization completed successfully');
	} else {
		console.log('ℹ️  No significant optimizations applied');
	}
}

// Run if called directly
if (require.main === module) {
	main();
}

module.exports = { CSSOptimizer };
