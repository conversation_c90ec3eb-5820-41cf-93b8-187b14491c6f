{"extends": "./tsconfig.json", "compilerOptions": {"noEmit": false, "emitDeclarationOnly": true, "declaration": true, "declarationMap": true, "outDir": "./dist", "rootDir": "./src", "moduleResolution": "bundler", "skipLibCheck": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx", "**/*.stories.ts", "**/*.stories.tsx", "**/__tests__/**/*", "src/test/**/*"]}